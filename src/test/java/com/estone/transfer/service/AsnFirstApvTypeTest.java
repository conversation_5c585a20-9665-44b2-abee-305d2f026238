package com.estone.transfer.service;

import com.estone.apv.enums.ApvTypeEnum;
import com.estone.picking.enums.PickingTaskType;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.service.impl.WhFbaAllocationHandleServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 海外仓头程单APV类型处理测试
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AsnFirstApvTypeTest {

    @Resource
    private WhFbaAllocationHandleServiceImpl whFbaAllocationHandleService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    /**
     * 测试海外仓头程单任务类型确定逻辑
     */
    @Test
    public void testDetermineAsnFirstTaskType() {
        // 测试单品类型（只包含SS和SM）
        WhFbaAllocation allocation1 = new WhFbaAllocation();
        allocation1.setApvType(ApvTypeEnum.SS.getCode());

        WhFbaAllocation allocation2 = new WhFbaAllocation();
        allocation2.setApvType(ApvTypeEnum.SM.getCode());

        List<WhFbaAllocation> singleTypeList = Arrays.asList(allocation1, allocation2);

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = WhFbaAllocationHandleServiceImpl.class
                    .getDeclaredMethod("determineAsnFirstTaskType", List.class);
            method.setAccessible(true);

            Integer taskType = (Integer) method.invoke(whFbaAllocationHandleService, singleTypeList);
            assertEquals("单品类型应该返回ASN_FIRST_SINGLE",
                    PickingTaskType.ASN_FIRST_SINGLE.intCode(), taskType);
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }

        // 测试多品类型（包含MM）
        WhFbaAllocation allocation3 = new WhFbaAllocation();
        allocation3.setApvType(ApvTypeEnum.MM.getCode());

        List<WhFbaAllocation> multipleTypeList = Arrays.asList(allocation1, allocation3);

        try {
            java.lang.reflect.Method method = WhFbaAllocationHandleServiceImpl.class
                    .getDeclaredMethod("determineAsnFirstTaskType", List.class);
            method.setAccessible(true);

            Integer taskType = (Integer) method.invoke(whFbaAllocationHandleService, multipleTypeList);
            assertEquals("包含多品类型应该返回ASN_FIRST_MULTIPLE",
                    PickingTaskType.ASN_FIRST_MULTIPLE.intCode(), taskType);
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    /**
     * 测试APV类型列表查询条件
     */
    @Test
    public void testApvTypeListQuery() {
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();

        // 测试设置APV类型列表
        List<String> apvTypeList = Arrays.asList(ApvTypeEnum.SS.getCode(), ApvTypeEnum.SM.getCode());
        queryCondition.setApvTypeList(apvTypeList);

        assertNotNull("APV类型列表不应为空", queryCondition.getApvTypeList());
        assertEquals("APV类型列表大小应为2", 2, queryCondition.getApvTypeList().size());
        assertTrue("应包含SS类型", queryCondition.getApvTypeList().contains(ApvTypeEnum.SS.getCode()));
        assertTrue("应包含SM类型", queryCondition.getApvTypeList().contains(ApvTypeEnum.SM.getCode()));
    }

    /**
     * 测试包装页面复用逻辑中的APV类型处理
     */
    @Test
    public void testPackagingPageReuseLogic() {
        // 创建测试数据
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setTaskNo("TEST_TASK_001");

        // 测试中转仓单品单件的APV类型设置（海外仓复用此页面）
        queryCondition.setApvType(ApvTypeEnum.SS.getCode());
        assertEquals("中转仓单品单件APV类型应为SS", ApvTypeEnum.SS.getCode(), queryCondition.getApvType());

        // 测试中转仓单品多件的APV类型设置（海外仓复用此页面）
        queryCondition.setApvType(ApvTypeEnum.SM.getCode());
        assertEquals("中转仓单品多件APV类型应为SM", ApvTypeEnum.SM.getCode(), queryCondition.getApvType());

        // 测试中转仓多品多件的APV类型设置（海外仓复用此页面）
        queryCondition.setApvType(ApvTypeEnum.MM.getCode());
        assertEquals("中转仓多品多件APV类型应为MM", ApvTypeEnum.MM.getCode(), queryCondition.getApvType());
    }

    /**
     * 测试播种逻辑中的APV类型验证
     */
    @Test
    public void testGridLogicWithApvType() {
        // 测试单品任务验证
        WhFbaAllocation singleAllocation = new WhFbaAllocation();
        singleAllocation.setApvType(ApvTypeEnum.SS.getCode());

        // 这里应该通过验证（单品任务允许SS类型）
        assertTrue("单品任务应该允许SS类型",
                ApvTypeEnum.SS.getCode().equals(singleAllocation.getApvType()) ||
                ApvTypeEnum.SM.getCode().equals(singleAllocation.getApvType()));

        // 测试多品任务验证
        WhFbaAllocation multipleAllocation = new WhFbaAllocation();
        multipleAllocation.setApvType(ApvTypeEnum.MM.getCode());

        // 这里应该通过验证（多品任务允许MM类型）
        assertTrue("多品任务应该允许MM类型",
                ApvTypeEnum.MM.getCode().equals(multipleAllocation.getApvType()));
    }

    /**
     * 测试页面复用的映射关系
     */
    @Test
    public void testPageReuseMappingRelation() {
        // 验证海外仓任务类型与中转仓页面类型的映射关系

        // 海外仓单品任务包含的APV类型
        assertTrue("海外仓单品任务应包含SS类型",
                PickingTaskType.ASN_FIRST_SINGLE.intCode() != null);
        assertTrue("海外仓单品任务应包含SM类型",
                PickingTaskType.ASN_FIRST_SINGLE.intCode() != null);

        // 海外仓多品任务包含的APV类型
        assertTrue("海外仓多品任务应包含MM类型",
                PickingTaskType.ASN_FIRST_MULTIPLE.intCode() != null);

        // 验证中转仓任务类型存在
        assertNotNull("中转仓单品单件任务类型应存在", PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        assertNotNull("中转仓单品多件任务类型应存在", PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode());
        assertNotNull("中转仓多品多件任务类型应存在", PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());

        // 验证APV类型枚举
        assertEquals("SS类型编码应正确", "SS", ApvTypeEnum.SS.getCode());
        assertEquals("SM类型编码应正确", "SM", ApvTypeEnum.SM.getCode());
        assertEquals("MM类型编码应正确", "MM", ApvTypeEnum.MM.getCode());
    }
}
