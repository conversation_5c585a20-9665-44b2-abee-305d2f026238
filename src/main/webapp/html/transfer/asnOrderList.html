<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.top_bar {
				position: fixed;
				top: 0px;
			}

			.btn-xs {
				padding: 0px 5px;
			}

			#task-list td {
				vertical-align: middle;
			}
			#task-list thead>tr>th {
				vertical-align: middle;
			}
			#fixedDiv thead>tr>th {
				vertical-align: middle;
			}
			#add_modal{
			    margin-top:50px;overflow:hidden;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="15040100"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">中转仓</a>
						</li>
						<li class="active">海外仓出库单管理</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12 col-new-wms-8">
						<#assign query=domain.query>
						<form action="${CONTEXT_PATH}fba/allocation/search?orderType=21" class="form-horizontal form-bordered form-row-stripped"
							  method="post" modelAttribute="domain" name="fbaAllocationForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<#--<input type="hidden" name="orderType" value="21">-->
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1" style="width:65px">创建时间:</label>
									<div class="col-md-2" style="width:150px">
                                        <input class="form-control Wdate" type="text" name="query.startTime" placeholder="" readonly="readonly" value="${query.startTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
                                    <label class="control-label col-md-1" style="width:25px">到</label>
                                    <div class="col-md-2" style="width:150px">
                                        <input class="form-control Wdate" type="text" name="query.endTime" placeholder="" readonly="readonly" value="${query.endTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    </div>
									<label class="control-label col-md-1" style="width:65px">出库单号:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.fbaNo" placeholder="多个逗号分开" value="${query.fbaNo }">
									</div>
									<label class="control-label col-md-1" style="width:50px">状态:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.status" value="${query.status}">
									</div>
									<label class="control-label col-md-1" style="width:50px">SKU:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${query.sku }">
									</div>
									<label class="control-label col-md-1" style="width:110px">快递单号/箱唛号:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.trackingNumber" placeholder="多个逗号分开" value="${query.trackingNumber }">
									</div>
									<label class="control-label col-md-1" style="width:65px">货件单号:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" name="query.splitShipmentId" type="text" placeholder="多个逗号分开" value="${query.splitShipmentId }">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-1" style="width:65px">交运时间:</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.upStartDeliverTime" placeholder="" readonly="readonly" value="${query.upStartDeliverTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-2" style="width:25px">到</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.upEndDeliverTime" placeholder="" readonly="readonly" value="${query.upEndDeliverTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:115px">装箱推送时间:</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.boxPushStartTime" placeholder="" readonly="readonly" value="${query.boxPushStartTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:40px">到</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.boxPushEndTime" placeholder="" readonly="readonly" value="${query.boxPushEndTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:50px">平台:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.purposeHouse" value="${query.purposeHouse}">
									</div>
									<label class="control-label col-md-1" style="width:65px">类型:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.packageMethod" value="${query.packageMethod}">
									</div>

									<label class="control-label col-md-1" style="width:65px">合单时间:</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.fromMergeTime" placeholder="" readonly="readonly" value="${query.fromMergeTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-2" style="width:25px">到</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.toMergeTime" placeholder="" readonly="readonly" value="${query.toMergeTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>
                                <div class="form-group">
                                    <label class="control-label col-md-1" style="width:85px">打印标签箱唛</label>
                                    <div class="col-md-3" style="width:305px">
                                        <select name="query.canPrint" placeholder="是否可打印标签箱唛" class="form-control" type="text" value="${domain.query.canPrint}" >
                                            <option value=""></option>
                                            <option value="true" <#if domain.query.canPrint?? && domain.query.canPrint?string == 'true'>selected="selected"</#if> >是</option>
                                            <option value="false" <#if domain.query.canPrint?? && domain.query.canPrint?string == 'false'>selected="selected"</#if> >否</option>
                                        </select>
                                    </div>
									<label class="control-label col-md-1" style="width:65px">揽收单号:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" name="query.pickupOrderId" type="text" placeholder="多个逗号分开" value="${query.pickupOrderId }">
									</div>
									<label class="control-label col-md-1" style="width:65px">分拣框号:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" name="query.pickBoxNumberStr" type="text" placeholder="多个逗号分开" value="${query.pickBoxNumberStr }">
									</div>
									<label class="control-label col-md-1" style="width:65px">发货单号:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" name="query.consignOrderNoStr" type="text" placeholder="多个逗号分开" value="${query.consignOrderNoStr }">
									</div>
									<label class="control-label col-md-1" style="width:85px">异常单据:</label>
									<div class="col-md-2">
										<select name="query.exceptionOrder" placeholder="异常单据" class="form-control" type="text" value="${domain.query.exceptionOrder}" >
											<option value=""></option>
											<option value="true" <#if domain.query.exceptionOrder?? && domain.query.exceptionOrder == true>selected="selected"</#if> >是</option>
											<option value="false" <#if domain.query.exceptionOrder?? && domain.query.exceptionOrder == false>selected="selected"</#if> >否</option>
										</select>
									</div>
                                </div>
							</div>


							<div>
								<div class="pull-left" style="margin-left: 10px;">
									<!--<button type="button" id="preBtn" class="btn btn-default" onclick="allot()">
										 分配库存
									</button>-->
                                    <@header method="auth" authCode="ABROAD_WAREHOUSE_SEND_MANAGE_GENERATE_PICK_TASK">
									<button type="button" id="batchCreatePreparePickingTaskBtn" class="btn btn-default" onclick="openOverseasMergeModal()">
										生成拣货任务
									</button>
                                    </@header>

									<@header method="auth" authCode="FBA_TRANSFER_CREATE_CO_ORDER">
										<button type="button" id="batchCreatePickupOrderBtn" class="btn btn-default" onclick="createJitCoOrderOrder()">
											创建发货单
										</button>
									</@header>

									<@header method="auth" authCode="ANS_PICKUP_ORDER_CREATE">
										<button type="button" id="batchCreatePickupOrderBtn" class="btn btn-default" onclick="createPickupOrder()">
											创建揽收单
										</button>
									</@header>

									<span class="btn btn-default fileinput-button">
                                            <span class="icon-plus"> 导入SKU条码</span>
									        <input type="file" name="file" onchange="importSkuBarcode(this)" />
									</span>

								</div>

								<div class="col-md-offset-10" style="text-align: right">
                                    <@header method="auth" authCode="ABROAD_WAREHOUSE_SEND_MANAGE_DOWNLOAD">
									<button type="button" class="btn btn-default" onclick="downloadRecord()">
										<i class="icon-download"></i> 导出
									</button>
                                    </@header>
									<button type="button" class="btn default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div id="fixedDiv" class="col-md-12">
						<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
							<colgroup>
								<col width="5%" />
								<col width="8%" />
								<col width="5%" />
								<col width="6%" />
								<col width="6%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="3%" />
								<col width="5%" />
                                <col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="8%" />
								<col width="8%" />
								<col width="10%" />
								<col width="10%" />
								<col width="8%" />
							</colgroup>
							<thead>
								<tr>
									<th> 编号</th>
									<th>出库单号</th>
									<th>平台<br/><span style="color: red; font-size: 14px">销售</span></th>
									<th>SKU数</th>
									<th>SKU件数</th>
									<th>已分配数量</th>
									<th>已拣数量</th>
									<th>装箱<br>数量</th>
									<th>海外仓<br>上架数量</th>
									<th>状态</th>
                                    <th>包装方式</th>
									<th>发货单号</th>
									<th>揽收单号</th>
									<th>分拣框号</th>
									<th>渠道/追踪号</th>
									<th>渠道(tms)/追踪号(tms)</th>
									<th>发货地址</th>
									<th>创建/更新时间</th>
									<th>操作</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="col-md-12" id="task-list-warp">
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
							<colgroup>
								<col width="5%" />
								<col width="8%" />
								<col width="5%" />
								<col width="6%" />
								<col width="6%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="3%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="8%" />
								<col width="8%" />
								<col width="10%" />
								<col width="10%" />
								<col width="8%" />
							</colgroup>
							<thead>
								<tr>
									<th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> 编号</label></th>
									<th>出库单号</th>
									<th>平台<br/><span style="color: red; font-size: 14px">销售</span></th>
									<th>SKU数</th>
									<th>SKU件数</th>
									<th>已分配数量</th>
									<th>已拣数量</th>
									<th>装箱<br>数量</th>
									<th>海外仓<br>上架数量</th>
									<th>状态</th>
                                    <th>包装方式</th>
									<th>发货单号</th>
									<th>揽收单号</th>
									<th>分拣框号</th>
									<th><span style="color: #3f8fb5">渠道</span><br/>追踪号</th>
                                    <th><span style="color: #5c5584">渠道(tms)</span><br/>追踪号(tms)</th>
									<th>发货地址</th>
									<th>创建/更新时间</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
							<#list domain.whFbaAllocations as fba>
								<tr>
									<td><label class="checkbox-inline"><input type="checkbox" value="${fba.id}" name="ids">${fba.id}</label></td>
									<td>${fba.fbaNo}</td>
									<td>${fba.purposeHouse}
										<br/><span style="color: red;font-size: 14px">${fba.salesperson}</span><br/>
									</td>
									<td>${fba.skuNum}</td>
									<td>${fba.skuQueryNum}</td>
									<td>${fba.skuAllocatedNum}</td>
									<td>${fba.skuPickedNum}</td>
									<td>${fba.skuBoxNum}</td>
									<td>${fba.skuPutawayNum}</td>
									<td>${util('enumName', 'com.estone.transfer.enums.AsnPrepareStatus', fba.status)}
										<br/> <span style="color: red;font-weight:bold">${util('enumName', 'com.estone.transfer.enums.AsnTagStatus', fba.tags)}</span>
									</td>
									<td>
										<#if fba.whAsnExtra.packageMethodStr?? && fba.whAsnExtra.packageMethodStr != 'FBA'>
											${fba.whAsnExtra.packageMethodStr}
										</#if>
									</td>
									<td>
										<#if fba.whAsnExtra??>
											${fba.whAsnExtra.consignOrderNo}
										</#if>
									</td>
									<td>
										<#if fba.whAsnExtra??>
											${fba.whAsnExtra.pickupOrderId}
										</#if>
									</td>
									<td>
										<#if fba.asnPickBox??>${fba.asnPickBox.number}</#if>
									</td>
									<td>
										<#if fba.shippingMethod??><span style="color: #3f8fb5">${fba.shippingMethod}</span></#if>
										<#if fba.trackingNumber??><br/>${fba.trackingNumber}</#if>
									</td>
									<td>
										<#if fba.shippingMethodByTms?? || fba.trackingNumberByTms??>
											<#if fba.shippingMethodByTms??>
												<span style="color: #5c5584">${fba.shippingMethodByTms}</span>
												<br/>
											</#if>
											<#if fba.trackingNumberByTms??>
												${fba.trackingNumberByTms}
											</#if>
										<#else>
											-
										</#if>
									</td>
									<td>
										<#if  fba.purposeHouse?? && fba.purposeHouse?lower_case == 'temu'>
											${fba.whAsnExtra.warehouseCode}<br/>
											${fba.whAsnExtra.receiptPerson}<br/>
											${fba.whAsnExtra.phoneNumber}<br/>
										</#if>
										${fba.whAsnExtra.receiptAddress}
									</td>
									<td>
										<#if fba.pushTime??>推单时间: ${fba.pushTime}</#if>
										<#if fba.mergeTime??><br/>合单时间: ${fba.mergeTime}</#if>
										<#if fba.boxPushTime??><br/>装箱推送: ${fba.boxPushTime}</#if>
										<#if fba.confirmTime??><br/>确认时间: ${fba.confirmTime}</#if>
										<#if fba.deliverTime??><br/>交运时间: ${fba.deliverTime}</#if>
										<#if fba.cancelTime??><br/>取消时间: ${fba.cancelTime}</#if>
										<#if fba.overseasUpTime??><br/>海外仓上架时间: ${fba.overseasUpTime}</#if>
									</td>
									<td>
										<a type="button" class="btn btn-xs btn-info" target="_blank" href="${CONTEXT_PATH}fba/allocation/asnDetail?fbaId=${fba.id}">详情</a><br/>
										<#if fba.purposeHouse?? &&  fba.purposeHouse?lower_case == 'shein'>
											<a type="button" class="btn btn-xs btn-info" onclick="printTag('${fba.whAsnExtra.id}','${fba.whAsnExtra.printSkuTagUrl}','SKU')">打印SKU</a>
										<#else>
											<#if fba.purposeHouse?? && fba.purposeHouse?lower_case == 'smt'>
												<#if fba.whAsnExtra.packageMethodStr?? && fba.whAsnExtra.packageMethodStr == 'SMT优选仓'>
													<a type="button" class="btn btn-xs btn-info" onclick="printSMTSKU('${fba.id}','SMT条码标签')">打印SKU</a>
												<#else>
													<a type="button" class="btn btn-xs btn-info" onclick="printSMTSKU('${fba.id}','SMT条码标签')">打印SKU</a>
												</#if>
											<#elseif fba.whAsnExtra.packageMethodStr?? && fba.whAsnExtra.packageMethodStr == '谷仓/海外仓'>
												<a type="button" class="btn btn-xs btn-info" onclick="printBarnSKU('${fba.id}')">打印SKU</a>
											<#elseif fba.whAsnExtra.packageMethodStr??
												&& (fba.whAsnExtra.packageMethodStr == 'JIT备货单' || fba.whAsnExtra.packageMethodStr == 'JIT半托管备货单' || fba.whAsnExtra.packageMethodStr == 'SMT优选仓')>
												<a type="button" class="btn btn-xs btn-info" onclick="printJitSKU('${fba.id}')">打印SKU</a>
											<#elseif  fba.purposeHouse?? && fba.purposeHouse?lower_case == 'shopee'>
												<a type="button" class="btn btn-xs btn-info" onclick="printTemuTag('${fba.id}','sku标签')">打印sku标签</a>
											<#elseif  fba.purposeHouse?? && fba.purposeHouse?lower_case != 'temu'>
												<a type="button" class="btn btn-xs btn-info" onclick="printSKU('${fba.id}')">打印SKU</a>
											</#if>
										</#if>
										<#if fba.isAsn?? && fba.isAsn==true && fba.whAsnExtra.packageMethodStr?? && fba.whAsnExtra.packageMethodStr != 'SMT优选仓'>
										<#else>
											<#if fba.status?? && (fba.status==9 || fba.status==7)>
												<a type="button" class="btn btn-xs btn-info" onclick="box('${fba.id}')">装箱</a>
												<a type="button" class="btn btn-xs btn-info" onclick="toBoxCheck('${fba.id}')">推送</a>
											</#if>
										</#if>

										<#if fba.purposeHouse?? && fba.purposeHouse?lower_case == 'shopee'>
											<#if fba.whAsnExtra.boxMarkUrl??>
												<a type="button" class="btn btn-xs btn-info" onclick="showPdf('${fba.whAsnExtra.boxMarkUrl}')">打印面单</a>
											<#else>
												<a type="button" class="btn btn-xs btn-info" disabled="disabled">打印面单</a>
											</#if>
										<#elseif fba.purposeHouse?? && fba.purposeHouse?lower_case == 'smt'>
											<#if fba.whAsnExtra.collectLabelUrl??>
												<a type="button" class="btn btn-xs btn-info" onclick="showPdf('${fba.whAsnExtra.collectLabelUrl}')">打印面单</a>
											<#else>
												<a type="button" class="btn btn-xs btn-info" disabled="disabled">打印面单</a>
											</#if>
										<#elseif (!fba.purposeHouse?? || fba.purposeHouse?lower_case != 'shein') && (fba.purposeHouse?? && fba.purposeHouse?lower_case != 'temu') >
											<#if fba.shippingMethod?? || (fba.whAsnExtra.packageMethodStr?? && (fba.whAsnExtra.packageMethodStr == 'JIT备货单' || (fba.whAsnExtra.packageMethodStr == 'JIT半托管备货单' && fba.whAsnExtra.pickupOrderId??)))>
												<a type="button" class="btn btn-xs btn-info" onclick="labelPrint('${fba.id}')">打印面单</a>
											</#if>
										</#if>
										<#if fba.purposeHouse?? && fba.purposeHouse?lower_case == 'smt'>
											<#if fba.whAsnExtra.boxMarkUrl??>
												<a type="button" class="btn btn-xs btn-info" onclick="showPdf('${fba.whAsnExtra.boxMarkUrl}')">打印箱唛</a>
											<#elseif fba.whAsnExtra.packageMethodStr?? && (fba.whAsnExtra.packageMethodStr == 'JIT备货单' || fba.whAsnExtra.packageMethodStr == 'JIT半托管备货单')
													&& fba.whAsnExtra.pickupOrderId?? && fba.whAsnExtra.consignOrderNo?? >
												<a type="button" class="btn btn-xs btn-info" onclick="printSMTXiangmai('${fba.id}')">打印箱唛</a>
											<#else>
												<a type="button" class="btn btn-xs btn-info" disabled="disabled">打印箱唛</a>
											</#if>
										<#elseif fba.status?? && (fba.status==13 || fba.status==17 || fba.status==18) && fba.purposeHouse?? && fba.purposeHouse?lower_case != 'shopee'>
											<#if fba.transitType == 1 || (fba.whAsnExtra.packageMethodStr??
												&& (fba.whAsnExtra.packageMethodStr == 'JIT备货单' || fba.whAsnExtra.packageMethodStr == 'JIT半托管备货单' || fba.whAsnExtra.packageMethodStr == 'SMT优选仓'))>
												<a type="button" class="btn btn-xs btn-info" onclick="printXiangmai('${fba.id}')">打印箱唛</a>
											<#else>
												<a type="button" class="btn btn-xs btn-info" disabled="disabled">打印箱唛</a>
											</#if>
										</#if>
										<#if fba.status?? && fba.status==13 >
											<#if fba.isAsn?? && fba.isAsn==true && fba.whAsnExtra.packageMethodStr?? && fba.whAsnExtra.packageMethodStr != 'SMT优选仓'>
											<#else>
												<#if !fba.shippingMethod?? && !fba.trackingNumber??>
													<a type="button" class="btn btn-xs btn-info" onclick="editAndDeliver('${fba.id}','${fba.fbaNo}','${fba.boxTotal}','${fba.shippingMethodByTms}','${fba.trackingNumberByTms}',1)">交运</a>
												<#else>
													<a type="button" class="btn btn-xs btn-info" onclick="editAndDeliver('${fba.id}','${fba.fbaNo}','${fba.boxTotal}','${fba.shippingMethod}','${fba.trackingNumber}',1)">交运</a>
												</#if>
											</#if>
										</#if>
										<#if fba.purposeHouse?? &&  fba.purposeHouse?lower_case == 'shein'>
											<a type="button" class="btn btn-xs btn-info" onclick="printTag('${fba.whAsnExtra.id}','${fba.whAsnExtra.printEnvironmentalProtectionTagUrl}','环保标签')">打印环保标签</a>
											<a type="button" class="btn btn-xs btn-info" onclick="printTag('${fba.whAsnExtra.id}','${fba.whAsnExtra.printTextileTagUrl}','纺织品标签')">打印纺织品标签</a>
										</#if>

										<#if  fba.purposeHouse?? && fba.purposeHouse?lower_case == 'temu'>
											<a type="button" class="btn btn-xs btn-info" onclick="printTemuTag('${fba.id}','条码标签','${fba.temuTag}')">打印条码标签</a>
											<a type="button" class="btn btn-xs btn-info" onclick="printTemuTag('${fba.id}','打包标签')">打印打包标签</a>
										</#if>

										<#if fba.status?? && (fba.status<17)>
											<#if fba.isAsn?? && fba.isAsn==true >
											<#else>
												<a type="button" class="btn btn-xs btn-info" onclick="editAndDeliver('${fba.id}','${fba.fbaNo}','${fba.boxTotal}','${fba.shippingMethod}','${fba.trackingNumber}',2)">编辑</a>
											</#if>
										</#if>
										<#if fba.isAsn?? && fba.isAsn==true >
											<a type="button" class="btn btn-xs btn-info" onclick="printAsnLanShou(${fba.id})">打印揽收单</a>
										</#if>
										<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${fba.id}, 'fbaAllocation')">日志</button>
									</td>
								</tr>
							</#list>
							</tbody>
						</table>
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>

			<div style="margin-top: 100px" class="modal fade" id="editAndDeliverModal" tabindex="-1" role="dialog" aria-labelledby="editAndDeliverModalLabel" aria-hidden="true">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
							<h4 class="modal-title" id="editAndDeliverModalLabel">交运</h4>
							<input type="hidden" id="editAndDeliverType" value=""/>
							<input type="hidden" id="editId" value=""/>
						</div>
						<div class="modal-body">
							<form>
								<div class="form-body">
									<table class="">
										<colgroup>
											<col width="10%"/>
											<col width="10%"/>
											<col width="10%"/>
											<col width="10%"/>
										</colgroup>
										<tbody>
										<tr style="height: 40px;">
											<td >出库单号号：</td>
											<td id="receiving_code"></td>
											<td>箱数：</td>
											<td id="box_total"></td>
										</tr>
										<tr style="height: 40px;">
											<td>物流公司：</td>
											<td><input style="" class="form-control input-small" type="text" name="shipping_method" id="shipping_method"></td>
											<td style="">物流单号：</td>
											<td><input style="" class="form-control input-small" type="text" name="tracking_number" id="tracking_number"></td>
										</tr>
										</tbody>
									</table>
								</div>
							</form>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">
								取消
							</button>
							<button type="button" class="btn btn-primary" id="saveData">
								确认
							</button>
						</div>
					</div><!-- /.modal-content -->
				</div><!-- /.modal-dialog -->
			</div><!-- /.modal -->


			<div id="dialog-form"  style="display: none">
				<form  class="form-horizontal form-bordered form-row-stripped">
					<div class="form-group">
						<label class="control-label col-md-1" style="width:100px">FNSKU</label>
						<div class="col-md-3" style="width:250px">
							<input class="form-control" name="fnskuName" id="fnskuName" type="text"  autocomplete="off">
						</div>
					</div>
					<div class="form-group" >
						<div style="color: red;margin-left: 22px" id="hint"></div>
						<input id="oldFnskuName" type="hidden" name="oldFnskuName" value="">
					</div>
					<div class="form-group">
						<label class="control-label col-md-1" style="width:100px">打印数量</label>
						<div class="col-md-3" style="width:250px">
							<input  class="form-control" onblur="checkPercent(this)" type="number" name="printNum" id="printNum" autocomplete="off">
						</div>
					</div>

					<div style="margin: 25px 0px 0px 30px;">
						<button type="button" class="btn blue" onclick="affirm()">
							<i class="icon-4x"></i> 确定
						</button>
						<button type="button" class="btn default" onclick="closePopup()" style="margin-left: 150px">
							<i class="icon-4x"></i> 取消操作
						</button>
					</div>

				</form>
			</div>


			<div style="display:none;" id="shippingMethodList">
				<#if domain.shippingMethodList?? >
				[{"id":"", "text":""}<#list domain.shippingMethodList as item>,{"id":"${item}", "text":"${item}"}</#list>]
				</#if>
			</div>
			<#include "/common/footer.html">
		</div>



		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH }js/print.js?v=${.now?datetime}"></script>


		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";

			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			function importSkuBarcode(target) {
				//检测上传文件的类型
				var filename = target.value;

				var ext, idx;
				if (filename == '') {
					$("#submit-upload").attr("disabled", true);
					layer.alert("请选择需要上传的文件!");
					return;
				} else {
					idx = filename.lastIndexOf(".");
					if (idx != -1) {
						ext = filename.substr(idx + 1).toUpperCase();
						ext = ext.toLowerCase();

						if (ext != 'xls' && ext != 'xlsx') {
							layer.alert("只能上传.Excel类型的文件!");
							return;
						}
					} else {
						layer.alert("只能上传.Excel类型的文件!");
						return;
					}
				}

				var r = confirm("确定上传" + filename + "?");

				if(!r) {
					return;
				}

				var uploadUrl = CONTEXT_PATH + "fba/allocation/importSkuBarcode";

				var searchUrl = $("#domain").attr("action");

				$("#domain").attr("action", uploadUrl);

				$("#domain").ajaxSubmit(function(data) {
					if (data.status == 200) {
						layer.confirm(data.message,{
							icon: 1,
							btn: ['确定']
						},function () {
							window.location.reload();
						})
					} else {
						$(target).val(null);
						customizeLayer(data.message, "error");
					}

					$("#domain").attr("action", searchUrl);

				});

				$("#domain").attr("action", searchUrl);
			}


            // 创建揽收
            function createJitCoOrderOrder() {
                var ids = getCheckedApvs();
                var param = ids.serialize();
                $.ajax({
                    type:'get',
                    async:false,
                    url:CONTEXT_PATH+"fba/allocation/createJitCoOrder?"+param,
                    success:function (data) {
                        if (data.status == 200) {
                            debugger;
                            alert("成功");
                            window.location.reload();
                        }else{
                            customizeLayer(data.message);
                        }
                    },error:function (data) {
                        customizeLayer(data.message);
                    }
                });
            }


			// 全选
			var checkAll = $("input[name='checkAll']");
			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的记录
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}
			
			// 获取选中的APV记录（用于海外仓合单功能）
			function getCheckedApvs() {
				return getCheckedIds();
			}

			// 状态
			var resultJson = ${domain.statusJson};
			$("input[name='query.status']").select2({
				data : resultJson,
				placeholder : "状态",
				allowClear : true
				//multiple: true
			});

			// 类型
			var resultJson = ${domain.typeJson};
			$("input[name='query.packageMethod']").select2({
				data : resultJson,
				placeholder : "类型",
				allowClear : true
				//multiple: true
			});

            var shippingMethodArr = jQuery.parseJSON($("#shippingMethodList").text());
			$("#shipping_method").select2({
				data : shippingMethodArr,
				placeholder : "物流",
				allowClear : true
			});

			var saleChannelJson = ${domain.saleChannelJson };
			$("input[name='query.purposeHouse']").select2({
				data: saleChannelJson,
				placeholder: "平台",
				allowClear: true,
				multiple: true
			});

			//分配
			function allot() {
				debugger;
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					layer.alert("请选择要操作的出库单", 'error');
					return;
				}

				var ids = checkedDatas.serialize();
				$.getJSON(CONTEXT_PATH + "fba/allocation/allot?" + ids, function(result){
					if(result.status == 200) {
						//setTimeout( function(){
						layer.alert(result.message, "success");
						//}, 3000 );
						setTimeout(function () {
							window.location.reload();
						}, 2000);
					} else {
						customizeLayer(result.message, "error");
					}
				});
			}

			//生成拣货任务
			function createPreparePickingTask(){
				debugger;
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					layer.alert("请选择要操作的出库单", 'error');
					return;
				}

				var ids = checkedDatas.serialize();
				$.getJSON(CONTEXT_PATH + "fba/allocation/createPickingTask?orderType=21&" + ids, function(result){
					if(result.status == 200) {
						//setTimeout( function(){
						layer.alert(result.message, "success");
						//}, 200 );
						setTimeout(function () {
							window.location.reload();
						}, 2000);
					} else {
						customizeLayer(result.message, "error");
					}
				});
			}

			// 导出
			function downloadRecord() {
				var param = $("#domain").serialize();
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length > 0) {
					param = param + "&" +checkedDatas.serialize();
				}
				$.post(CONTEXT_PATH + "fba/allocation/asnDownload", param, function(data){
					if (data.status == 200) {
						if (data.message==null || data.message==''){
							layer.alert('成功',function (index) {
								layer.close(index);
								diglog.close().remove();
								location.reload();
							});
						}else{
							customizeLayer(data.message);
						}
					} else {
						customizeLayer(data.message);
					}
				});
			}



            function editAndDeliver(id,orderNo,boxTotal,shippingMethod,trackingNumber,type) {
			    if(type == 2){
                    $("#editAndDeliverModalLabel").text("编辑");
				}else {
                    $("#editAndDeliverModalLabel").text("交运");
				}
                $("#editAndDeliverType").val(type);
                $("#receiving_code").text(orderNo);
                $("#box_total").text(boxTotal);
                $("#editId").val(id);

                $("#shipping_method").val(shippingMethod);
                $("#tracking_number").val(trackingNumber);
                $("#shipping_method").select2({
                    data : shippingMethodArr,
                    placeholder : "物流",
                    allowClear : true
                });
                $("#editAndDeliverModal").modal('show');
            }

            $('#saveData').on('click',function () {
                //3.设置提交按钮失效，以实现防止按钮重复点击
                $('#saveData').attr("disabled", true);
                var receivingCode = $('#receiving_code').text();
                var trackingNumber = $('#tracking_number').val();
                var shippingMethod = $("#shipping_method").val();
                var type = $("#editAndDeliverType").val();
				var id = $("#editId").val();

                if(trackingNumber == ''){
                    layer.alert('物流单号不能为空！','error');
                    $('#saveData').removeAttr('disabled');
                    return false;
                }
                if(shippingMethod == ''){
                    layer.alert('物流公司不能为空！','error');
                    $('#saveData').removeAttr('disabled');
                    return false;
                }

                $.ajax({
                    url: CONTEXT_PATH + 'fba/allocation/editAndDeliver',
                    type: "POST",
                    data: {id:id,receivingCode: receivingCode,trackingNumber:trackingNumber,shippingMethod:shippingMethod,type:type},
                    success: function(response) {
                        $('#saveData').removeAttr('disabled');
                        if(response.status == "200"){
                            alert("操作成功");
                            setTimeout(function() {
                                //提交完成后按钮重新设置有效
                                window.location.reload();
                            }, 1500);
                        }else {
                            customizeLayer("操作失败：" + response.message,'error');
                        }
                    },
                    error:function () {
                        $('#saveData').removeAttr('disabled');
                        layer.alert("系统异常，操作失败!",'error');
                    }
                });

            });

			function printBarnSKU(id){
                var diglog = dialog({
                    title: '打印SKU标签',
                    width: 350,
                    url: CONTEXT_PATH + "fba/allocation/toPrintSKU?id=" + id,
                    cancelValue: '取消',
                    cancel: function () {}
                });
                diglog.show();
            }

            //打印SKU
            function printSKU(id) {
                var diglog = dialog({
                    title: '打印SKU标签',
                    width: 350,
                    url: CONTEXT_PATH + "fba/allocation/toPrintSKU?id=" + id,
                    okValue: '打印',
                    ok: function () {
                        var printWindow = $(this.iframeNode.contentWindow.document.body);
                        var body = printWindow.find("#printSku-body");

                        var items = body.find(".print-content");
                        if(items == null || items.length == 0){
                            layer.alert("没有要打印的SKU", "error");
                            return false;
						}
                        var whFbaAllocation = {};
                        var allocationItems= [];

						var result = false;
						items.each(function () {
							var fnSku = $(this).find('input[name="fnSku"]').val();
							var sellSkuName = $(this).find('input[name="sellSkuName"]').val();
							var productSku = $(this).find('input[name="productSku"]').val();
							var productBarcode = $(this).find('input[name="productBarcode"]').val();
							var quantity = $(this).find('input[name="quantity"]').val();
							var allocationItem = {};
							if(quantity == ""){
								layer.alert("打印数量不能为空", "error");
								result = false;
								return false;
							}

							allocationItem.fnSku = fnSku;
							allocationItem.quantity = quantity;
							allocationItem.sellSkuName = sellSkuName;
							allocationItem.productSku = productSku;
							allocationItem.productBarcode = productBarcode;

							allocationItems.push(allocationItem);
							result = true;
						});
						whFbaAllocation.items = allocationItems;
						if(result){
							$.ajax({
								url: CONTEXT_PATH + "fba/allocation/printSKU",
								type: "POST",
								data: {id: id,allocationStr:JSON.stringify(whFbaAllocation)},
								success: function(data){
									var printSkuWindow=window.open();
									printSkuWindow.document.write(data);
									printSkuWindow.focus();
									window.location.reload();
								}
							});
						}
                    },
                    cancelValue: '取消',
                    cancel: function () {}
                });
                diglog.show();
            }

            //装箱
			function box(id) {
				var diglog = dialog({
					title: '装箱确认',
					width: 800,
					height: 410,
					url: CONTEXT_PATH + "fba/allocation/toBoxAsn?id=" + id,
					okValue: '提交',
					ok: function () {
						var printWindow = $(this.iframeNode.contentWindow.document.body);
						var body = printWindow.find("#boxSku-body");

						if ($(body).find("a[data-action='saveTemplate']").length > 0){
							layer.alert("有未保存的数据，请保存后再提交!",'error');
							return;
						}

						var whFbaAllocation = {};
						var items = [];
						var index1 = 0;

						var box1AllNull = "";
						$(body).find('tr').each(function () {
							if ($(this).attr('name') == 'sku-tr') {
								var sku = $(this).children()[0].innerText;
								var pickNum = parseInt($(this).find("span[name='total-pickNum']").text());
								$(this).find('.box-line').each(function () {
									var boxNo = $(this).attr('data-index');
									var loadNum = $(this).find("input[name='loadNum']").val();
									if (boxNo == 1 && loadNum) {
										box1AllNull = box1AllNull + "false,";
									}

									var item = {};
									if (items[index1]) {
										item = items[index1];
									}
									item.boxNo = boxNo;
									item.fnSku = sku;
									item.loadNum = loadNum;
									item.pickQuantity = pickNum;
									items[index1] = item;
									index1++;

								});
							}
						});

						if (box1AllNull.indexOf("false") == -1){
							layer.alert("箱号1装车数量不能全为空!",'error');
							return;
						}
						var box1SizeNull = "";
						var box1WeightNull = "";
						$(body).find('tr').each(function () {
							if ($(this).attr('name') == 'handle-tr' || $(this).attr('name') == 'sku-tr'){
								return;
							}
							var index2 = 0;
							$(this).find('.box-line').each(function () {
								var length = $(this).find("input[name='length']").val();
								var width = $(this).find("input[name='width']").val();
								var height = $(this).find("input[name='height']").val();
								var weight = $(this).find("input[name='weight']").val();
								var item = {};
								if (items[index2]){
									item = items[index2];
								}
								if (length){
									item.productLength = length;
								}
								if (width){
									item.productWidth = width;
								}
								if (height){
									item.productHeight = height;
								}
								if (weight){
									item.productWeight = weight;
								}
								if (item.boxNo == 1 && length && width && height) {
									box1SizeNull = box1SizeNull + "false,";
								}
								if (item.boxNo == 1 && weight){
									box1WeightNull = box1WeightNull + "false,";
								}
								items[index2] = item;
								index2 ++;
							});

						});
						if (box1WeightNull.indexOf("false") == -1 || box1SizeNull.indexOf("false") == -1 ){
							layer.alert("箱号1尺寸、重量不能为空!",'error');
							return;
						}

						whFbaAllocation.id = id;
						whFbaAllocation.items = items;


						$.ajax({
							url: CONTEXT_PATH + 'fba/allocation/box',
							type: "POST",
							data: {id:id, allocation:JSON.stringify(whFbaAllocation)},
							success: function(response) {
								if(response.status == "200"){
									alert("操作成功");
									setTimeout(function() {
										//提交完成后按钮重新设置有效
										window.location.reload();
									}, 1500);
								}else {
									customizeLayer("操作失败：" + response.message,'error');
								}
							},
							error:function () {
								layer.alert("系统异常，操作失败!",'error');
							}
						});
					},
					cancelValue: '取消',
					cancel: function () {}
				});
				diglog.show();
			}

			//审核
			function toBoxCheck(id) {
				var diglog = dialog({
					title: '推送确认',
					width: 800,
					height: 400,
					url: CONTEXT_PATH + "fba/allocation/toBoxCheckAsn?id=" + id,
					okValue: '确认',
					ok: function () {
						var printWindow = $(this.iframeNode.contentWindow.document.body);
						var body = printWindow.find("#boxSku-body");
						var allEq = true;
						debugger
						$(body).find("tr[name='sku-tr']").each(function () {
							var sku = $(this).children()[0].innerText;
							var loadingQuantity = 0;
							$(this).find("input[name='loadNum']").each(function () {
								var loadStr = $(this).val();
								loadingQuantity = loadingQuantity + parseInt(loadStr == '' ? 0 : loadStr);
							});
							var pickNum = parseInt($(this).find("span[name='total-pickNum']").text());
							if (loadingQuantity != pickNum){
								layer.alert("SKU："+sku + "装车数量不等于已捡数量，请重新编辑后再推送！",'error');
								allEq = false;
								return false;
							}
						});
						if (allEq) {
							$.ajax({
								url: CONTEXT_PATH + 'fba/allocation/boxPush?id=' + id,
								type: "GET",
								success: function (response) {
									if (response.status == "200") {
										alert("操作成功");
										setTimeout(function () {
											//提交完成后按钮重新设置有效
											window.location.reload();
										}, 1500);
									} else {
										customizeLayer("操作失败：" + response.message, 'error');
									}
								},
								error: function () {
									layer.alert("系统异常，操作失败!", 'error');
								}
							});
						}

					},
					cancelValue: '取消',
					cancel: function () {}
				});
				diglog.show();
			}

			function printXiangmai(id) {
				if (confirm("确定打印？")) {
                    setTimeout(function() {
                        window.open(CONTEXT_PATH + "fba/allocation/printXiangmai?id=" + id);
                    }, 1000);
				}
			}
			function printSMTXiangmai(id) {
				$.ajax({
					url: CONTEXT_PATH + 'fba/allocation/localPrintXiangmai?id=' + id,
					type: "GET",
					success : function(response){
						if (response.status == '200') {
							var pdfUrl = response.body.pdfUrl;
							if (!pdfUrl) {
								pdfUrl = window.location.origin + CONTEXT_PATH + response.message;
							}
							var boxNumber = response.location;
							printXiangMaiPdf(pdfUrl, 1, boxNumber);
						} else {
							layer.alert(response.message, {closeBtn: 0}, function (index) {
								layer.close(index);
							});
						}
					},
					error:function(){
						layer.alert('扫描失败，请重新扫描');
					}
				});
			}

			function printXiangMaiPdf(message, copies, jitBoxNumber){
				var LODOP = getLodop();
				LODOP.SET_PRINT_PAGESIZE(0, '100mm', '100mm', 'Note'); // 设置纸张大小
				LODOP.ADD_PRINT_PDF(0, 0, '100mm', '100mm', message);
				if (jitBoxNumber) {
					LODOP.ADD_PRINT_TEXT(18, 304, 70, 41, jitBoxNumber);
					LODOP.SET_PRINT_STYLEA(0, "FontSize", 22);
				}
				LODOP.SET_PRINT_STYLEA(0,"PDFScalMode",2);
				if(copies === undefined){
					copies = 1;
				}
				LODOP.SET_PRINT_COPIES(copies); // 打印份数
				// LODOP.PRINT_DESIGN();
				LODOP.PRINT(); // 静默打印
			}

			function checkOutPrint(id) {
				if (confirm("确定打印？")) {
					print(id);
				}
			}


			function printJitSKU(id) {
				if (confirm("确定打印sku？")) {
					setTimeout(function() {
						var newWindow;
						newWindow = window.open(CONTEXT_PATH + "fba/allocation/printJitSKU?id=" + id);
					}, 1000);
				}
			}

			function labelPrint(id) {
				if (confirm("确定打印？")) {
					setTimeout(function() {
						var newWindow;
						newWindow = window.open(CONTEXT_PATH + "fba/allocation/labelPrint?id=" + id);
					}, 1000);
				}
			}

			function print(id) {
				setTimeout(function() {
					var newWindow;
					newWindow = window.open(CONTEXT_PATH + "fba/allocation/printCheckOut?id=" + id);
				}, 1000);
			}

			function printTag(id,url,type) {
				setTimeout(function() {
					var newWindow = window.open(CONTEXT_PATH + "fba/allocation/printTag?url=" + url+"&id="+id+"&type="+type);
				}, 1000);
			}

			function printTemuLabel(url, printerTag, copies) {
				if (url.indexOf("http") == -1) {
					url = window.location.origin + CONTEXT_PATH + url;
				}
				pageLength = "70mm";//纸张长
				pageWidth = "60mm";//纸张宽
				var LODOP = getLodop();
				LODOP.SET_PRINT_PAGESIZE(0, pageLength, pageWidth, 'Note'); // 设置纸张大小
				LODOP.ADD_PRINT_PDF(0,0,'100%','100%',url);
				//var fontHtml = '<body><div style="font-family:nimbussannovtcon;font-size: 12px;"><div><span>ÉLÉMENTS  D</span><span style="font-family: 黑体;margin-top: 1px;position: absolute;">'s</span><span style="margin-left: 4px;">EMBALLAGE</span></div><div><span>À SÉPARER ET À DÉPOSER</span></div><div><span>DANS LE BAC DE TRI</span></div></div></body>';
				var imgUrl = window.location.origin + CONTEXT_PATH + "file/pdf/jit/labelCode/huanbao.png";
				LODOP.ADD_PRINT_IMAGE(70,0,253,80,"<img border='0' src='"+imgUrl+"' width=2830 height=800 />");
				LODOP.SET_PRINT_STYLEA(0,"Stretch",2);
				LODOP.SET_PRINT_STYLEA(0,"TransColor","#FFFFFF");
				//LODOP.ADD_PRINT_HTM(92,67,167,59,fontHtml);
				LODOP.ADD_PRINT_TEXT(150,7,200,20,"WARNING");
				LODOP.SET_PRINT_STYLEA(0,"Bold",1);
				LODOP.ADD_PRINT_TEXT(165, 12, 304, 58, "1. To avoid danger of suffocation, keep this plastic bag away from babies and children. Do not use this bag in cribs. beds, carriages or play pens.2. This bag is not a toy.");
				LODOP.SET_PRINT_STYLEA(0,"FontSize",7);
				LODOP.SET_PRINT_STYLEA(0,"Bold",1);
				if (LODOP.SET_PRINTER_INDEX(printerTag)) {
					if (copies === undefined) {
						copies = 1;
					}
					LODOP.SET_PRINT_COPIES(copies); // 打印份数
					//LODOP.PRINT_DESIGN(); // 打印设计
					//LODOP.PREVIEW(); // 打印设计
					LODOP.PRINT(); // 静默打印
				}
			}

			function printTemuTag(id,type,temuTag) {
				var diglog = dialog({
					title: type,
					width: 350,
					url: CONTEXT_PATH + "fba/allocation/toPrintTemuSKU?id=" + id+"&type="+type,
					okValue: '打印',
					ok: function () {
						debugger;
						var printWindow = $(this.iframeNode.contentWindow.document.body);

						debugger;
						var temuPrinterTag = printWindow.find("#temuPrinterTag").val();

						if(!temuPrinterTag){
							layer.alert("请选择打印机", "error");
							result = false;
							return false;
						}

						var body = printWindow.find("#printSku-body");

						var items = body.find(".print-content");
						if(items == null || items.length == 0){
							layer.alert("没有要打印的SKU", "error");
							return false;
						}
						var whFbaAllocation = {};
						var allocationItems= [];

						var result = false;
						items.each(function () {
							debugger;
							var productSku = $(this).find('input[name="productSku"]').val();
							var quantity = $(this).find('input[name="quantity"]').val();
							var allocationItem = {};
							if(quantity == ""){
								layer.alert("打印数量不能为空", "error");
								result = false;
								return false;
							}
							allocationItem.quantity = quantity;
							allocationItem.productSku = productSku;
							allocationItems.push(allocationItem);
							result = true;
						});
						whFbaAllocation.items = allocationItems;
						whFbaAllocation.warehouseCodeStr = printWindow.find('input[name="warehouseCode"]').val();
						if(result){
							$.ajax({
								url: CONTEXT_PATH + "fba/allocation/printTemuTag",
								type: "POST",
								data: {id: id,type:type,allocationStr:JSON.stringify(whFbaAllocation)},
								success: function(response){
									if(response.status == '200'){
										var temuPdfUrl = response.body.temuPdfUrl;
										var tagMap = response.body.tagMap;
										var dzMap = response.body.dzMap;
										$.each(temuPdfUrl,function (k, v){
											if (v!=null && v>0){
												if (type == "条码标签") {
													printTemuLabel(k,temuPrinterTag,v);
													if (tagMap && tagMap[k] && tagMap[k].indexOf("宠物") != -1){
														printPetTag(temuPrinterTag,v);
													}
													var sku = dzMap[k];
													if (sku && printWindow.find("#dz-print_"+sku).length > 0){
														var dzPrintHtml = printWindow.find("#dz-print_"+sku).html();
														printHtmCopies(dzPrintHtml,temuPrinterTag,v);
													}
													setTimeout(function () {
														console.log(k);
													}, 200);
												} else {
													printCopies(k, temuPrinterTag, type, v);
												}
											}

										});
									}else{
										layer.alert(response.message, {closeBtn: 0}, function (index) {
											layer.close(index);
										});
									}
								}
							});
						}
					},
					cancelValue: '取消',
					cancel: function () {}
				});
				diglog.show();
			}

			function printCopies(message,printerTag,type, copies){
				var LODOP = getLodop();
				if (type=="打包标签"){
					LODOP.SET_PRINT_PAGESIZE(0, '100mm', '100mm', 'Note'); // 设置纸张大小
					LODOP.ADD_PRINT_PDF(0, 0, '100mm', '100mm', message);
				}
				if (type=="sku标签"){
					LODOP.SET_PRINT_PAGESIZE(0, '70mm', '30mm', 'Note'); // 设置纸张大小
					LODOP.ADD_PRINT_PDF(0, 0, '70mm', '30mm', message);
					LODOP.SET_PRINT_STYLEA(0,"PDFScalMode",2);
				}
				debugger
				if(type == 'SMT条码标签'){
					LODOP.SET_PRINT_PAGESIZE(0, '60mm', '60mm', 'Note'); // 设置纸张大小
					LODOP.ADD_PRINT_PDF(0, 0, '60mm', '60mm', message);
					LODOP.SET_PRINT_STYLEA(0,"PDFScalMode",2);
				}
				if (LODOP.SET_PRINTER_INDEX(printerTag)) {
					if(copies === undefined){
						copies = 1;
					}
					LODOP.SET_PRINT_COPIES(copies); // 打印份数
					LODOP.PRINT(); // 静默打印
					// LODOP.PREVIEW();
				}
			}


			//重货标签
			function heavyTag(id) {
				window.open(CONTEXT_PATH + "fba/allocation/printHeavyTag?id=" + id);
			}

			$('#printSkus').on('click',function () {
				dialog({
					title: '打印系统SKU标签',
					content: $('#dialog-form'),
					width: 400,
					height: 180,
					top: 0,
				}).showModal();
			});

			function affirm() {
				debugger;
				var printNum = $('#printNum').val();
				var fnskuName = $('#fnskuName').val();
				var hint=$('#hint').val();
				var oldFnskuName = $('#oldFnskuName').val();
				if (!fnskuName || !printNum) {
					layer.alert("打印的FNSKU或打印数量不能为空", 'error');
					return ;
				}
				if ((hint != '' || hint!=null) && fnskuName==oldFnskuName && oldFnskuName) {
					layer.alert("当前sku没有已拣返架库存，不能打印系统sku标签", 'error');
					return ;
				}
				$.ajax({
					url: CONTEXT_PATH+"fba/allocation/toPrintFNSKU",
					type: "POST",
					data: {fnskuName: fnskuName,printNum:printNum},
					success: function(date){
						debugger;
						if (date.status=='200') {
							printFNSKU(date.message,printNum);
						}else if (date.status == '401'){
							$('#hint').html("当前sku没有已拣返架库存，不能打印系统sku标签");
							$('#oldFnskuName').val(fnskuName);
						}else{
							$('#hint').html("");
							customizeLayer(date.message, 'error');
						}
					},
					error:function(){
						layer.alert('系统错误','error');
					}
				});
			}

			function printFNSKU(outIds,printNum){
				debugger;
				$.ajax({
					url: CONTEXT_PATH+"fba/allocation/printFNSKU",
					type: "POST",
					data: {outIds: outIds,printNum:printNum},
					success: function(data){
						var printWindow=window.open();
						printWindow.document.write(data);
						printWindow.focus();
						window.location.reload();
					},
					error:function(){
						layer.alert('系统错误','error');
					}
				});

			}

			function closePopup(){
				$('#hint').html("");
				window.location.reload();
			}

			function checkPercent(obj){
				var $this = $(obj);
				if($this.val() == ''){
					$this.val("");
					return;
				}
				var reg = /^(\+)?\d+(\.\d+)?$/;
				if (!reg.test($this.val())) {
					layer.alert("只能输入正实数", 'error');
					$this.val("");
					return;
				}
				var reg = /^\+?[1-9][0-9]*$/;
				if(!reg.test($this.val())){
					layer.alert("请输入正确的正整数", 'error');
					$('#printNum').val('');
					return false;
				}
			}

			function pushOverseasUpQuantityToTms() {
				debugger
				var ids = getCheckedIds();
				if (ids.length == 0){
					layer.alert("请勾选要推送的数据！", 'error');
					return false;
				}
				var checkIds = "";
				for (var i = 0; i < ids.length; i++) {
					var outId = ids[i].value;
					if (i == 0) {
						checkIds += outId;
					} else {
						checkIds += "," + outId;
					}
				}
				$.ajax({
					url: CONTEXT_PATH + "fba/allocation/pushOverseasUpQuantityToTms",
					type: "POST",
					data: {ids: checkIds},
					success: function (data) {
						if (data.status == '200') {
							alert("成功");
						} else {
							customizeLayer(data.message, 'error');
						}
						setTimeout(function () {
							location.reload();
						}, 1000);
					},
					error: function () {
						layer.alert('系统错误', 'error');
					}
				});
			}

			function showPdf(url){
			    if (url){
                    setTimeout(function() {
                        var newWindow;
                        newWindow = window.open(url);
                    }, 500);
                }
            }

			// 创建揽收
			function createPickupOrder() {
				var ids = getCheckedApvs();
				var param = ids.serialize();
				$.ajax({
					type:'get',
					async:false,
					url:CONTEXT_PATH+"fba/allocation/createPickupOrder?"+param,
					success:function (data) {
						if (data.status == 200) {
							debugger;
							alert("成功");
							window.location.reload();
						}else{
							customizeLayer(data.message);
						}
					},error:function (data) {
						customizeLayer(data.message);
					}
				});
			}


			function printSMTSKU(id,type) {
                var diglog = dialog({
                    title: "条码标签",
                    width: 350,
                    url: CONTEXT_PATH + "fba/allocation/toPrintTemuSKU?id=" + id+"&type=条码标签",
                    okValue: '打印',
                    ok: function () {
                        debugger;
                        var printWindow = $(this.iframeNode.contentWindow.document.body);

                        debugger;
                        var temuPrinterTag = printWindow.find("#temuPrinterTag").val();

                        if(!temuPrinterTag){
                            layer.alert("请选择打印机", "error");
                            result = false;
                            return false;
                        }

                        var body = printWindow.find("#printSku-body");

                        var items = body.find(".print-content");
                        if(items == null || items.length == 0){
                            layer.alert("没有要打印的SKU", "error");
                            return false;
                        }
                        var allocationItems= [];

                        var result = false;
                        items.each(function () {
                            debugger;
                            var productSku = $(this).find('input[name="productSku"]').val();
                            var temuCodeUrl = $(this).find('input[name="temuCodeUrl"]').val();
                            var quantity = $(this).find('input[name="quantity"]').val();
                            var allocationItem = {};
                            if(quantity == ""){
                                layer.alert("打印数量不能为空", "error");
                                result = false;
                                return false;
                            }
							if (temuCodeUrl.indexOf("http") == -1 && temuCodeUrl.indexOf("pdf/jit/labelCode") != -1) {
								temuCodeUrl = window.location.origin + CONTEXT_PATH + temuCodeUrl;
							}
                            allocationItem.quantity = quantity;
                            allocationItem.productSku = productSku;
                            allocationItem.temuCodeUrl = temuCodeUrl;
                            allocationItems.push(allocationItem);
                            result = true;
                        });
                        if(result){
                            $.each(allocationItems,function(index, obj){
								if (obj.quantity && parseInt(obj.quantity) >0){
                                	printCopies(obj.temuCodeUrl, temuPrinterTag, type, obj.quantity);
								}
                            });
                        }else{
                            layer.alert("打印失败", "error");
                        }
                    },
                    cancelValue: '取消',
                    cancel: function () {}
                });
                diglog.show();
            }

			function printAsnLanShou(id) {
				var url = CONTEXT_PATH+"fba/allocation/printAsnLanShou?id=" + id;
				$.get(url, function(data){
					if (data.status == 200) {
						$('#printFrame').attr('src', data.message);
						window.open(data.message);
					} else {
						customizeLayer(data.message);
					}
				});
			}
		</script>

		<!-- 海外仓合单配置弹窗 -->
		<div class="modal fade" id="overseasMergeModal" tabindex="-1" role="dialog" aria-labelledby="overseasMergeModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title" id="overseasMergeModalLabel">
							<i class="fa fa-cogs"></i>
							海外仓出库单拣货任务配置
						</h4>
					</div>
					<div class="modal-body">
						<form id="overseasMergeForm">
							<!-- 单据类型选择 -->
							<div class="form-group">
								<label>
									<i class="fa fa-list-alt"></i>
									单据类型
								</label>
								<div class="selection-type-container">
									<div class="radio">
										<label>
											<input type="radio" name="selectionType" value="ALL_SINGLE" onchange="handleSelectionTypeChange()">
											<span class="radio-custom"></span>
											所有单品（SS/SM类型）
										</label>
									</div>
									<div class="radio">
										<label>
											<input type="radio" name="selectionType" value="ALL_MULTIPLE" onchange="handleSelectionTypeChange()">
											<span class="radio-custom"></span>
											所有多品（MM类型）
										</label>
									</div>
									<div class="radio">
										<label>
											<input type="radio" name="selectionType" value="SELECTED" onchange="handleSelectionTypeChange()">
											<span class="radio-custom"></span>
											当前勾选（需同类型校验）
										</label>
									</div>
								</div>
							</div>
							
							<!-- 任务限制参数 -->
							<div class="form-group">
								<label for="locationLimit">
									<i class="fa fa-map-marker"></i>
									货位数上限
								</label>
								<div class="input-container">
									<input type="number" class="form-control" id="locationLimit" name="locationLimit" 
										   value="50" min="1" placeholder="请输入大于0的正整数"
										   oninput="handleLocationLimitInput()" onblur="handleLocationLimitInput()">
									<span class="input-icon">位</span>
								</div>
								<small class="help-block">
									<i class="fa fa-info-circle"></i>
									默认值：50，请输入大于0的正整数
								</small>
								<div class="text-danger" id="locationLimitError" style="display: none;"></div>
							</div>
							
							<div class="form-group">
								<label for="pcsLimit">
									<i class="fa fa-cubes"></i>
									PCS上限
								</label>
								<div class="input-container">
									<input type="number" class="form-control" id="pcsLimit" name="pcsLimit" 
										   value="1000" min="1" placeholder="请输入大于0的正整数"
										   oninput="handlePcsLimitInput()" onblur="handlePcsLimitInput()">
									<span class="input-icon">个</span>
								</div>
								<small class="help-block">
									<i class="fa fa-info-circle"></i>
									默认值：1000，请输入大于0的正整数
								</small>
								<div class="text-danger" id="pcsLimitError" style="display: none;"></div>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">
							<i class="fa fa-times"></i>
							取消
						</button>
						<button type="button" class="btn btn-primary" id="confirmOverseasMerge" onclick="handleConfirmOverseasMerge()">
							<i class="fa fa-check"></i>
							确定
						</button>
					</div>
				</div>
			</div>
		</div>

		<script>
		// 海外仓合单相关JavaScript代码

		// 打开海外仓合单配置弹窗
		function openOverseasMergeModal() {
			// 重置表单
			resetOverseasMergeForm();
			// 显示弹窗
			$('#overseasMergeModal').modal('show');
		}
		
		// 监听弹窗关闭事件，重置表单状态
		$('#overseasMergeModal').on('hidden.bs.modal', function () {
			resetOverseasMergeForm();
		});

		// 重置表单
		function resetOverseasMergeForm() {
			$('#overseasMergeForm')[0].reset();
			$('input[name="selectionType"]').prop('checked', false);
			$('#locationLimit').val(50).removeClass('error');
			$('#pcsLimit').val(1000).removeClass('error');
			$('#locationLimitError, #pcsLimitError').hide();
		}

		// 单据类型选择变化时的处理函数
		function handleSelectionTypeChange() {
			// 如果选择"当前勾选"，检查是否有选中的记录
			if (this.value === 'SELECTED') {
				var checkedIds = getCheckedApvs();
				if (checkedIds.length === 0) {
					alert('请先选择要生成任务的发货单！');
					this.checked = false;
				}
			}
		}

		// 数字输入框校验函数
		function handleLocationLimitInput() {
			validatePositiveInteger($(this), 'locationLimitError', '请输入大于0的正整数');
		}

		function handlePcsLimitInput() {
			validatePositiveInteger($(this), 'pcsLimitError', '请输入大于0的正整数');
		}

		// 正整数校验函数
		function validatePositiveInteger($input, errorId, errorMessage) {
			var value = parseInt($input.val());
			var $error = $('#' + errorId);
			
			if (isNaN(value) || value <= 0 || !Number.isInteger(Number($input.val()))) {
				$input.addClass('error').css('border-color', '#d9534f');
				$error.text(errorMessage).show();
			} else {
				$input.removeClass('error').css('border-color', '');
				$error.hide();
			}
		}

		// 确定按钮点击事件函数
		function handleConfirmOverseasMerge() {
			// 提交时校验参数
			if (!validateBeforeSubmit()) {
				return;
			}
			
			executeOverseasMerge();
		}
		
		// 提交前参数校验
		function validateBeforeSubmit() {
			// 检查是否选择了单据类型
			if (!$('input[name="selectionType"]:checked').length) {
				layer.alert('请选择单据类型！', {
					icon: 0,
					title: '提示'
				});
				return false;
			}
			
			// 检查选中数据（当选择"当前勾选"时）
			var selectionType = $('input[name="selectionType"]:checked').val();
			if (selectionType === 'SELECTED') {
				var checkedIds = getCheckedApvs();
				if (checkedIds.length === 0) {
					layer.alert('请先选择要生成任务的发货单！', {
						icon: 0,
						title: '提示'
					});
					return false;
				}
			}
			
			// 检查货位数上限
			var locationLimit = parseInt($('#locationLimit').val());
			if (isNaN(locationLimit) || locationLimit <= 0) {
				layer.alert('请输入正确的货位数上限（大于0的正整数）！', {
					icon: 0,
					title: '提示'
				});
				$('#locationLimit').focus();
				return false;
			}
			
			// 检查PCS上限
			var pcsLimit = parseInt($('#pcsLimit').val());
			if (isNaN(pcsLimit) || pcsLimit <= 0) {
				layer.alert('请输入正确的PCS上限（大于0的正整数）！', {
					icon: 0,
					title: '提示'
				});
				$('#pcsLimit').focus();
				return false;
			}
			
			return true;
		}

		// 执行海外仓合单
		function executeOverseasMerge() {
			var selectionType = $('input[name="selectionType"]:checked').val();
			var locationLimit = parseInt($('#locationLimit').val());
			var pcsLimit = parseInt($('#pcsLimit').val());
			var selectedIds = [];
			
			if (selectionType === 'SELECTED') {
				var checkedApvs = getCheckedApvs();
				for (var i = 0; i < checkedApvs.length; i++) {
					selectedIds.push(parseInt(checkedApvs[i].value));
				}
			}
			// 对于 ALL_SINGLE 和 ALL_MULTIPLE 类型，传递空数组
			
			// 显示加载状态
			$('#confirmOverseasMerge').addClass('loading').html('<i class="fa fa-spinner fa-spin"></i> 生成中...');
			
			// 构造请求数据
			var requestData = {
				orderType: '${domain.orderType!""}',
				selectionType: selectionType,
				locationLimit: locationLimit,
				pcsLimit: pcsLimit
			};
			
			// 只有在选择具体记录时才传递ids参数
			if (selectionType === 'SELECTED' && selectedIds.length > 0) {
				requestData.ids = selectedIds;
			}
			
			$.ajax({
				url: CONTEXT_PATH + 'fba/allocation/createPickingTaskAdvanced',
				type: 'POST',
				data: requestData,
				traditional: true,
				success: function(response) {
					// 关闭弹窗
					$('#overseasMergeModal').modal('hide');
					// 显示操作结果提示
					if (response.status === 200) {
						customizeLayer(response.message);
						window.location.reload();
					} else {
						customizeLayer(response.message);
					}
				},
				error: function(xhr, status, error) {
					// 关闭弹窗
					$('#overseasMergeModal').modal('hide');
					
					// 显示错误提示
					layer.alert('请求失败：' + error, {
						icon: 2,
						title: '网络错误',
						btn: ['确定'],
						btnAlign: 'c'
					});
				},
				complete: function() {
					// 恢复按钮状态
					$('#confirmOverseasMerge').removeClass('loading').html('<i class="fa fa-check"></i> 确定');
				}
			});
		}

		// 优化后的CSS样式
		$('<style>')
		.prop('type', 'text/css')
		.html(
		/* 模态框整体样式 */
		'#overseasMergeModal .modal-dialog {' +
			'width: 600px;' +
			'margin: 30px auto;' +
		'}' +

		'#overseasMergeModal .modal-content {' +
			'border-radius: 12px;' +
			'border: none;' +
			'box-shadow: 0 15px 35px rgba(0,0,0,0.15);' +
			'overflow: hidden;' +
		'}' +

		/* 模态框头部 */
		'#overseasMergeModal .modal-header {' +
			'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);' +
			'color: white;' +
			'border-bottom: none;' +
			'padding: 20px 25px;' +
		'}' +

		'#overseasMergeModal .modal-title {' +
			'font-size: 18px;' +
			'font-weight: 600;' +
			'margin: 0;' +
		'}' +

		'#overseasMergeModal .close {' +
			'color: white;' +
			'opacity: 0.8;' +
			'font-size: 28px;' +
			'text-shadow: none;' +
		'}' +

		'#overseasMergeModal .close:hover {' +
			'color: white;' +
			'opacity: 1;' +
		'}' +

		/* 模态框主体 */
		'#overseasMergeModal .modal-body {' +
			'padding: 30px 25px;' +
			'background-color: #ffffff;' +
		'}' +

		/* 表单组样式 */
		'#overseasMergeModal .form-group {' +
			'margin-bottom: 25px;' +
		'}' +

		'#overseasMergeModal .form-group:last-child {' +
			'margin-bottom: 0;' +
		'}' +

		'#overseasMergeModal .form-group > label {' +
			'font-weight: 600;' +
			'color: #333;' +
			'margin-bottom: 12px;' +
			'display: block;' +
			'font-size: 14px;' +
		'}' +

		'#overseasMergeModal .form-group > label i {' +
			'margin-right: 8px;' +
			'color: #667eea;' +
		'}' +

		/* 选择类型容器样式 */
		'#overseasMergeModal .selection-type-container {' +
			'background-color: #f8f9fa;' +
			'border-radius: 8px;' +
			'padding: 15px;' +
			'border: 1px solid #e9ecef;' +
		'}' +

		/* 输入框容器样式 */
		'#overseasMergeModal .input-container {' +
			'position: relative;' +
		'}' +

		'#overseasMergeModal .input-icon {' +
			'position: absolute;' +
			'right: 12px;' +
			'top: 50%;' +
			'transform: translateY(-50%);' +
			'color: #6c757d;' +
			'font-size: 13px;' +
			'pointer-events: none;' +
		'}' +

		/* 单选按钮组样式 */
		'#overseasMergeModal .radio {' +
			'margin-bottom: 12px;' +
			'position: relative;' +
		'}' +

		'#overseasMergeModal .radio label {' +
			'font-weight: normal !important;' +
			'color: #666 !important;' +
			'cursor: pointer;' +
			'padding-left: 35px;' +
			'position: relative;' +
			'display: block;' +
			'margin-bottom: 0 !important;' +
			'line-height: 1.6;' +
			'padding: 8px 15px 8px 35px;' +
			'border-radius: 6px;' +
			'transition: all 0.3s ease;' +
		'}' +

		'#overseasMergeModal .radio label:hover {' +
			'background-color: #f1f3f4;' +
		'}' +

		'#overseasMergeModal .radio input[type="radio"] {' +
			'position: absolute;' +
			'opacity: 0;' +
			'cursor: pointer;' +
		'}' +

		'#overseasMergeModal .radio .radio-custom {' +
			'position: absolute;' +
			'left: 10px;' +
			'top: 50%;' +
			'transform: translateY(-50%);' +
			'width: 18px;' +
			'height: 18px;' +
			'border: 2px solid #ddd;' +
			'border-radius: 50%;' +
			'background-color: white;' +
			'transition: all 0.3s ease;' +
		'}' +

		'#overseasMergeModal .radio .radio-custom::after {' +
			'content: "";' +
			'position: absolute;' +
			'top: 50%;' +
			'left: 50%;' +
			'transform: translate(-50%, -50%) scale(0);' +
			'width: 8px;' +
			'height: 8px;' +
			'border-radius: 50%;' +
			'background-color: #667eea;' +
			'transition: transform 0.2s ease;' +
		'}' +

		'#overseasMergeModal .radio input[type="radio"]:checked + .radio-custom {' +
			'border-color: #667eea;' +
		'}' +

		'#overseasMergeModal .radio input[type="radio"]:checked + .radio-custom::after {' +
			'transform: translate(-50%, -50%) scale(1);' +
		'}' +

		'#overseasMergeModal .radio input[type="radio"]:checked ~ label,' +
		'#overseasMergeModal .radio input[type="radio"]:checked + .radio-custom + label {' +
			'color: #667eea !important;' +
			'font-weight: 500 !important;' +
			'background-color: rgba(102, 126, 234, 0.05);' +
		'}' +

		/* 输入框样式 */
		'#overseasMergeModal .form-control {' +
			'border: 2px solid #e1e5e9;' +
			'border-radius: 8px;' +
			'padding: 12px 16px;' +
			'font-size: 14px;' +
			'transition: all 0.3s ease;' +
			'background-color: #ffffff;' +
		'}' +

		'#overseasMergeModal .form-control:focus {' +
			'border-color: #667eea;' +
			'box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);' +
			'outline: none;' +
		'}' +

		'#overseasMergeModal .form-control.error {' +
			'border-color: #dc3545 !important;' +
			'box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;' +
		'}' +

		/* 帮助文本样式 */
		'#overseasMergeModal .help-block {' +
			'color: #6c757d;' +
			'font-size: 13px;' +
			'margin-top: 8px;' +
			'line-height: 1.4;' +
			'padding: 8px 12px;' +
			'background-color: #f8f9fa;' +
			'border-radius: 6px;' +
			'border-left: 3px solid #17a2b8;' +
		'}' +

		'#overseasMergeModal .help-block i {' +
			'margin-right: 6px;' +
			'color: #17a2b8;' +
		'}' +

		/* 错误信息样式 */
		'#overseasMergeModal .text-danger {' +
			'color: #dc3545;' +
			'font-size: 13px;' +
			'margin-top: 8px;' +
			'font-weight: 500;' +
			'padding: 8px 12px;' +
			'background-color: #f8d7da;' +
			'border-radius: 6px;' +
			'border-left: 3px solid #dc3545;' +
		'}' +

		'#overseasMergeModal .text-danger::before {' +
			'content: "\\f071";' +
			'font-family: "FontAwesome";' +
			'margin-right: 6px;' +
		'}' +

		/* 模态框底部 */
		'#overseasMergeModal .modal-footer {' +
			'background-color: #f8f9fa;' +
			'border-top: 1px solid #e9ecef;' +
			'padding: 20px 25px;' +
			'text-align: right;' +
		'}' +

		/* 按钮样式 */
		'#overseasMergeModal .btn {' +
			'padding: 10px 20px;' +
			'border-radius: 6px;' +
			'font-weight: 500;' +
			'border: none;' +
			'transition: all 0.3s ease;' +
			'min-width: 80px;' +
		'}' +

		'#overseasMergeModal .btn-default {' +
			'background-color: #6c757d;' +
			'color: white;' +
		'}' +

		'#overseasMergeModal .btn-default:hover {' +
			'background-color: #545b62;' +
			'color: white;' +
		'}' +

		'#overseasMergeModal .btn-primary {' +
			'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);' +
			'color: white;' +
			'margin-left: 10px;' +
		'}' +

		'#overseasMergeModal .btn-primary:hover {' +
			'background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);' +
			'color: white;' +
			'transform: translateY(-1px);' +
			'box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);' +
		'}' +

		/* 按钮加载状态样式 */
		'#confirmOverseasMerge.loading {' +
			'pointer-events: none;' +
		'}' +

		/* 加载图标动画 */
		'.fa-spin {' +
			'animation: fa-spin 1s infinite linear;' +
		'}' +

		'@keyframes fa-spin {' +
			'0% { transform: rotate(0deg); }' +
			'100% { transform: rotate(360deg); }' +
		'}' +

		/* 响应式设计 */
		'@media (max-width: 768px) {' +
			'#overseasMergeModal .modal-dialog {' +
				'width: 95%;' +
				'margin: 10px auto;' +
			'}' +
			'#overseasMergeModal .modal-body {' +
				'padding: 20px 15px;' +
			'}' +
		'}'
		)
		.appendTo('head');
		</script>
	</body>

</html>