package com.estone.picking.enums;


import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @Description:
 * 
 * @ClassName: PickingTaskType
 * @Author: qinyangkai
 * @Date: 2018年8月18日
 * @Version: 0.0.1
 */
public enum PickingTaskType {
    SINGLESINGLE("单品单件", "1", "green"),
    SINGLEMULTIPLE("单品多件", "2", "yellow"),
    SINGLEVERIETY("单品拣货", "15", "pink"),
    MULTIPLEMULTIPLE("多品多件", "3", "blue"),
   // BZYC("播种异常", "4", "purple"),
    BZYC("二次配货", "4", "purple"),
    RXSINGLESINGLE("热销单品单件", "5", "red"),
    RXSINGLEMULTIPLE("热销单品两件", "6", "red"),
    EXPRESSSINGLESINGLE("快递单品单件", "7", "green"),
    EXPRESSSINGLEMULTIPLE("快递单品多件", "8", "yellow"),
    EXPRESSMULTIPLEMULTIPLE("快递多品多件", "9", "blue"),
    FBASINGLESINGLE("FBA单品单件", "10", "green"),
    FBASINGLEMULTIPLE("FBA单品多件", "11", "yellow"),
    FBAMULTIPLEMULTIPLE("FBA多品多件", "12", "blue"),
    AISLESINGLESINGLE("拣货区单品单件", "13", "green"),
    AISLESINGLEMULTIPLE("拣货区单品多件", "14", "yellow"),
    BZCY("播种差异", "16", "red"),
    TASK_TYPE_ASN("海外仓拣货", "19","green"),
    ASN_PREPARE("FBA头程", "20","green"),
    ASN_FIRST("海外仓头程单", "21","green"),
    ASN_FIRST_SINGLE("海外仓单品", "23","green"),
    ASN_FIRST_MULTIPLE("海外仓多品", "24","blue"),
    ASN_TRANSFER("中转仓发货单", "22","green"),
    OPTIMAL("优选仓", "30","green"),

    OPTIMAL_RU_SS("集包单件", "311", "green"),
    OPTIMAL_RU_SM("集包多件", "312", "yellow"),
    OPTIMAL_RU_SV("集包单品", "313", "pink"),
    OPTIMAL_RU_MM("集包多品", "314", "blue"),

    OVERSIZE_SINGLE("超体积单件", "31", "green"),
    OVERSIZE_MULTIPLE("超体积多件", "32", "yellow"),
    OVERSIZE_SINGLE_PRODUCT("超体积单品", "33", "pink"),
    OVERSIZE_MULTIPLE_PRODUCT("超体积多品", "34", "blue"),

    PRESTORAGE_TRANSFER("存货迁移","411","ice-blue"),

    TRANSFER_SINGLESINGLE("中转仓单品单件", "51", "green"),
    TRANSFER_SINGLEMULTIPLE("中转仓单品多件", "52", "yellow"),
    TRANSFER_MULTIPLEMULTIPLE("中转仓多品多件", "53", "blue"),
    // BZYC("播种异常", "4", "purple"),
    TRANSFER_BZYC("中转仓二次配货", "54", "purple"),
    TRANSFER_BZCY("中转仓播种差异", "55", "red"),

    TEMU_MULTIPLEMULTIPLE("拼多多备货", "56", "blue"),
    TEMU_BZYC("拼多多二次配货", "57", "purple"),
    TEMU_BZCY("拼多多播种差异", "58", "red"),

    JIT_SINGLESINGLE("JIT单品单件", "59", "green"),
    JIT_SINGLEMULTIPLE("JIT单品多件", "60", "yellow"),
    JIT_MULTIPLEMULTIPLE("JIT多品多件", "61", "blue"),
    JIT_BZYC("JIT二次配货", "62", "purple"),
    JIT_BZCY("JIT播种差异", "63", "red"),

    JIT_ASN_SINGLESINGLE("仓发单品", "64", "green"),
    JIT_ASN_MULTIPLE("仓发多品", "65", "blue"),
    JIT_ASN_BZYC("仓发二次配货", "66", "purple"),
    JIT_ASN_BZCY("仓发播种差异", "67", "red"),
    ;

    private String code;

    private String name;

    private String color;

    private PickingTaskType(String name, String code, String color) {
        this.name = name;
        this.code = code;
        this.setColor(color);
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public static String getNameByCode(String code) {
        PickingTaskType[] values = values();
        for (PickingTaskType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static String getDisplayByCode(String code) {
        PickingTaskType[] values = values();
        for (PickingTaskType type : values) {
            if (type.code.equals(code)) {
                return type.getColor();
            }
        }
        return null;
    }

    //优选仓code
    public static List<Integer> getOptimalIntCode() {
        List<Integer> pickingTaskTypes = new ArrayList<Integer>();
        pickingTaskTypes.add(PickingTaskType.OPTIMAL.intCode());
        pickingTaskTypes.add(PickingTaskType.OPTIMAL_RU_SS.intCode());
        pickingTaskTypes.add(PickingTaskType.OPTIMAL_RU_SM.intCode());
        pickingTaskTypes.add(PickingTaskType.OPTIMAL_RU_SV.intCode());
        pickingTaskTypes.add(PickingTaskType.OPTIMAL_RU_MM.intCode());
        return pickingTaskTypes;
    }

    //中转仓
    public static List<Integer> getTransferIntCode() {
        List<Integer> pickingTaskTypes = new ArrayList<Integer>();
        pickingTaskTypes.add(PickingTaskType.ASN_PREPARE.intCode());
        pickingTaskTypes.add(PickingTaskType.ASN_FIRST.intCode());
        pickingTaskTypes.add(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        pickingTaskTypes.add(PickingTaskType.ASN_FIRST_MULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.JIT_ASN_SINGLESINGLE.intCode());
        pickingTaskTypes.add(PickingTaskType.JIT_ASN_MULTIPLE.intCode());
        return pickingTaskTypes;
    }

    public static List<Integer> getJITIntCode() {
        List<Integer> pickingTaskTypes = new ArrayList<Integer>();
        pickingTaskTypes.add(PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_BZCY.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_BZYC.intCode());
        return pickingTaskTypes;
    }

    //  标签查询使用
    public static List<Integer> getTransferTagIntCode() {
        List<Integer> pickingTaskTypes = new ArrayList<Integer>();
        pickingTaskTypes.add(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_BZCY.intCode());
        pickingTaskTypes.add(PickingTaskType.TRANSFER_BZYC.intCode());
        return pickingTaskTypes;
    }

    public static List<Integer> getJitAsnIntCode() {
        List<Integer> pickingTaskTypes = new ArrayList<Integer>();
        pickingTaskTypes.add(PickingTaskType.JIT_ASN_MULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.JIT_ASN_SINGLESINGLE.intCode());
        pickingTaskTypes.add(PickingTaskType.JIT_ASN_BZYC.intCode());
        pickingTaskTypes.add(PickingTaskType.JIT_ASN_BZCY.intCode());
        return pickingTaskTypes;
    }
    public static List<Integer> getPddIntCode() {
        List<Integer> pickingTaskTypes = new ArrayList<Integer>();
        pickingTaskTypes.add(PickingTaskType.TEMU_MULTIPLEMULTIPLE.intCode());
        pickingTaskTypes.add(PickingTaskType.TEMU_BZYC.intCode());
        pickingTaskTypes.add(PickingTaskType.TEMU_BZCY.intCode());
        return pickingTaskTypes;
    }

    public static PickingTaskType build(String code) {
        PickingTaskType[] values = values();

        for (PickingTaskType type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }
}
