package com.estone.picking.dao.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.bean.WhPickingTaskSku;
import com.estone.picking.dao.WhPickingTaskDao;
import com.estone.picking.dao.mapper.*;
import com.estone.picking.enums.PdaPickingType;
import com.estone.picking.enums.PickingTaskStatus;
import com.estone.picking.enums.PickingTaskType;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import com.estone.common.util.SqlerTemplate;

@Repository("whPickingTaskDao")
public class WhPickingTaskDaoImpl implements WhPickingTaskDao {

    private void setQueryCondition(SqlerRequest request, WhPickingTaskQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, query.getId());

        request.addDataParam(WhPickingTaskDBField.TASK_TYPE, DataType.INT, query.getTaskType());

        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, query.getTaskStatus());

        request.addDataParam(WhPickingTaskDBField.TASK_NO, DataType.STRING, query.getTaskNo());

        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, query.getReceivePerson());

        request.addDataParam(WhPickingTaskDBField.WAREHOUSE_TYPE, DataType.INT, query.getWarehouseType());

        request.addDataParam(WhPickingTaskDBField.WAYBILL_TYPE, DataType.INT, query.getWaybillType());

        request.addDataParam(WhPickingTaskDBField.TASK_LEVEL, DataType.INT, query.getTaskLevel());

        request.addDataParam(WhPickingTaskDBField.IS_PRINTING, DataType.INT, query.getIsPrinting());

        request.addDataParam(WhPickingTaskDBField.IS_ASN, DataType.INT, query.getIsAsn());

        request.addDataParam(WhPickingTaskDBField.CROSS_FLOOR, DataType.BOOLEAN, query.getCrossFloor());
        if (StringUtils.isNotBlank(query.getYcBoxNo()))
            request.addDataParam("yc_box_no", DataType.STRING, query.getYcBoxNo());
        if (CollectionUtils.isNotEmpty(query.getTaskTypeList()))
            request.addDataParam("task_type_list", DataType.INT, query.getTaskTypeList());
        if (CollectionUtils.isNotEmpty(query.getIsAsnList())){
            request.addDataParam("is_asn_list", DataType.INT, query.getIsAsnList());
            request.addDataParam(WhPickingTaskDBField.IS_ASN, DataType.INT, null);
        }

        // 创建时间查询
        request.addDataParam("from_created_date", DataType.STRING, query.getFromCreatedDate());
        request.addDataParam("to_created_date", DataType.STRING, query.getToCreatedDate());

        // 领取时间查询
        request.addDataParam("from_receive_date", DataType.STRING, query.getFromReceiveDate());
        request.addDataParam("to_receive_date", DataType.STRING, query.getToReceiveDate());

        // sku拣货时间查询
        request.addDataParam("from_pick_date", DataType.STRING, query.getFromPickDate());
        request.addDataParam("to_pick_date", DataType.STRING, query.getToPickDate());

        // SKU查询
        if (StringUtils.isNotBlank(query.getSku())) {
            request.addSqlDataParam("LEFT_SKU", " LEFT JOIN wh_picking_task_sku wpts on wpts.task_id = wpt.id ");
            request.addSqlDataParam("WHERE_SKU", " AND wpts.sku= '" + query.getSku() + "'");
        }

        // 是否播种差异
        if (query.getSowDifferQuantity() != null && query.getSowDifferQuantity() != 0) {
            request.addSqlDataParam("IS_SOW_DIFFER", " AND wpt.sow_differ_quantity != 0");
        }
        else if (query.getSowDifferQuantity() != null) {
            request.addSqlDataParam("IS_SOW_DIFFER",
                    " AND (wpt.sow_differ_quantity = 0 OR wpt.sow_differ_quantity IS NULL)");
        }

        // 周转框查询
        if (StringUtils.isNotBlank(query.getBoxNo())) {
            request.addSqlDataParam("LEFT_BOXNO", " LEFT JOIN wh_box wb ON wb.relation_no = wpt.id ");
            if (StringUtils.contains(query.getBoxNo(), ",")) {
                request.addSqlDataParam("WHERE_BOXNO", " AND wb.box_no IN ('"
                        + StringUtils.join(StringUtils.split(query.getBoxNo().replaceAll(" ",""), ","), "','") + "')");
            }
            else {
                request.addSqlDataParam("WHERE_BOXNO", " AND wb.box_no = '" + query.getBoxNo() + "'");
            }
        }

        // 发货单号查询
        if (StringUtils.isNoneBlank(query.getApvNo())) {
            request.addSqlDataParam("LEFT_APVNO", " LEFT JOIN wh_picking_task_item wpti on wpti.task_id = wpt.id ");
            if (StringUtils.contains(query.getApvNo(), ",")) {
                request.addSqlDataParam("WHERE_APVNO", " AND wpti.apv_no IN ('"
                        + StringUtils.join(StringUtils.split(query.getApvNo().replaceAll(" ",""), ","), "','") + "')");
            }
            else {
                request.addSqlDataParam("WHERE_APVNO", " AND wpti.apv_no= '" + query.getApvNo() + "'");
            }
        }

        // 如果是导出
        if (query.isDownload()) {

            request.addSqlDataParam("QUERY_SKU", ", wpts.sku, wpts.quantity, wpts.pick_quantity, sku.location_number ");
            request.addSqlDataParam("QUERY_BOX_SQL", ", (SELECT wb.box_no FROM wh_box wb WHERE wb.relation_no = wpt.id AND wb.type IN (2, 3, 6, 11) LIMIT 1) AS box_no ");
            request.addSqlDataParam("LEFT_SKU",
                    " LEFT JOIN wh_picking_task_sku wpts on wpts.task_id = wpt.id LEFT JOIN wh_sku sku on wpts.sku = sku.sku ");
        }
        else {
            /*request.addSqlDataParam("COUNT_SQL",
                    ", (SELECT wb.box_no FROM wh_box wb WHERE wb.relation_no = wpt.id AND wb.type IN (2, 3, 6, 11, 15,16,17) LIMIT 1) AS box_no, (SELECT COUNT(DISTINCT ws.stock_id) FROM wh_picking_task_item wpts LEFT JOIN wh_apv_out_stock_chain ws ON ws.relevant_no = wpts.apv_no WHERE wpts.task_id = wpt.id) as locationQuantity, (select COUNT(wpts.sku) from wh_picking_task_sku wpts where wpts.task_id = wpt.id) as skuQuantity, (select SUM(wpts.quantity) from wh_picking_task_sku wpts where wpts.task_id = wpt.id) as pieceQuantity, (select COUNT(wpti.apv_id) from wh_picking_task_item wpti where wpti.task_id = wpt.id) as orderQuantity, (select SUM(wpts.pick_quantity) from wh_picking_task_sku wpts where wpts.task_id = wpt.id) as pickQuantity");*/
            // 统计库位数
            request.addSqlDataParam("COUNT_SQL",",(SELECT wb.box_no FROM wh_box wb WHERE wb.relation_no = wpt.id AND wb.type IN (2, 3, 6, 11, 15,16,17,18,19,20,21,22,23,24,25,26,27) LIMIT 1) AS box_no, " +
                    "CASE " +
                    "WHEN wpt.task_type=4 THEN (" +
                    "SELECT COUNT(DISTINCT ws.stock_id) " +
                    "FROM wh_picking_task_item wpts " +
                    "LEFT JOIN wh_picking_task_sku wts ON wpts.task_id = wts.task_id " +
                    "LEFT JOIN wh_apv_out_stock_chain ws " +
                    "ON ws.relevant_no = wpts.apv_no " +
                    "AND wts.sku = ws.sku " +
                    "WHERE wpts.task_id = wpt.id " +
                    ") " +
                    "WHEN wpt.task_type IN (56,57,58) THEN (SELECT COUNT(DISTINCT s.location_number) " +
                    "FROM wh_picking_task_item wpts " +
                    "LEFT JOIN temu_prepare_order_item ti ON ti.id = wpts.apv_id " +
                    "LEFT JOIN temu_prepare_order tpo ON tpo.id = ti.prepare_order_id " +
                    "LEFT JOIN wh_apv_out_stock_chain ws ON ws.relevant_no = tpo.prepare_order_no " +
                    "INNER JOIN wh_transfer_stock whs ON whs.id = ws.stock_id " +
                    "JOIN wh_stock s ON s.id = whs.stock_id " +
                    "WHERE wpts.task_id = wpt.id " +
                    ")"+
                    "WHEN wpt.task_type=54 THEN ( " +
                    "SELECT COUNT(DISTINCT whs.stock_id) " +
                    "FROM wh_picking_task_item wpts " +
                    "LEFT JOIN wh_picking_task_sku wts ON wpts.task_id = wts.task_id " +
                    "LEFT JOIN wh_apv_out_stock_chain ws ON ws.relevant_no = wpts.apv_no AND wts.sku = ws.sku " +
                    "inner join wh_transfer_stock whs on whs.id = ws.stock_id " +
                    "WHERE wpts.task_id = wpt.id " +
                    ") " +
                    "WHEN  wpt.task_type=51 OR wpt.task_type=52 THEN ( " +
                    "SELECT COUNT(DISTINCT IFNULL(s.location_number,s1.location_number))" +
                    " FROM wh_picking_task_item wpts " +
                    " LEFT JOIN wh_apv_out_stock_chain ws ON ws.relevant_no = wpts.apv_no" +
                    " LEFT JOIN wh_transfer_stock whs ON whs.id = ws.stock_id" +
                    " LEFT JOIN wh_stock s ON s.id = whs.stock_id" +
                    " LEFT JOIN temu_prepare_order_item ti ON ti.id = wpts.apv_id" +
                    " LEFT JOIN temu_prepare_order tpo ON tpo.id = ti.prepare_order_id" +
                    " LEFT JOIN wh_apv_out_stock_chain ws1 ON ws1.relevant_no = tpo.prepare_order_no" +
                    " LEFT JOIN wh_transfer_stock whs1 ON whs1.id = ws1.stock_id " +
                    " LEFT JOIN wh_stock s1 ON s1.id = whs1.stock_id" +
                    " WHERE wpts.task_id = wpt.id" +
                    ") " +
                    "WHEN wpt.task_type=20 OR wpt.task_type=21 OR wpt.task_type=53 THEN ( " +
                    "SELECT COUNT(DISTINCT whs.stock_id) " +
                    "FROM wh_picking_task_item wpts " +
                    "LEFT JOIN wh_apv_out_stock_chain ws ON ws.relevant_no = wpts.apv_no  " +
                    "inner join wh_transfer_stock whs on whs.id = ws.stock_id " +
                    "WHERE wpts.task_id = wpt.id " +
                    ") " +
                    "ELSE ( " +
                    "SELECT COUNT(DISTINCT ws.stock_id) " +
                    "FROM wh_picking_task_item wpts " +
                    "LEFT JOIN wh_apv_out_stock_chain ws ON ws.relevant_no = wpts.apv_no  " +
                    "WHERE wpts.task_id = wpt.id " +
                    ") " +
                    "END AS locationQuantity, (select COUNT(wpts.sku) from wh_picking_task_sku wpts where wpts.task_id = wpt.id) as skuQuantity, (select SUM(wpts.quantity) from wh_picking_task_sku wpts where wpts.task_id = wpt.id) as pieceQuantity, (select COUNT(wpti.apv_id) from wh_picking_task_item wpti where wpti.task_id = wpt.id) as orderQuantity, (select SUM(wpts.pick_quantity) from wh_picking_task_sku wpts where wpts.task_id = wpt.id) as pickQuantity " );
        }

        if (CollectionUtils.isNotEmpty(query.getTaskIds())) {
            request.addDataParam("taskIds", DataType.INT, query.getTaskIds());
        }

        if (CollectionUtils.isNotEmpty(query.getTaskNos())) {
            request.addDataParam("taskNos", DataType.STRING, query.getTaskNos());
        }

        // 0代表所有 1代表正常的任务 2代表快递业务任务 3代表FBA任务
        if (query.getPickingTaskType() == 0) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,23,24,311,312,313,314,31,32,33,34,41,42,43,44,45,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67) ");
        }
        else if (query.getPickingTaskType() == 1) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (3) ");// 普通多品
        }
        else if (query.getPickingTaskType() == 2) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (3) ");// 普通多品跨仓
        }
        else if (query.getPickingTaskType() == 3) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (1,2,15) ");// 普通非多品
        }
        else if (query.getPickingTaskType() == 4) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (7,8,9) ");// 快递业务
        }
        else if (query.getPickingTaskType() == 5) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (7,8,9) ");// 快递业务跨仓
        }
        else if (query.getPickingTaskType() == 6) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (10,11,12) ");// FBA
        }
        else if (query.getPickingTaskType() == 7) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (10,11,12) ");// FBA跨仓
        }
        else if (query.getPickingTaskType() == 8) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (13,14) ");// 通道拣货任务
        }
        else if (query.getPickingTaskType() == 9) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (20) ");// 海外仓备货
        }
        else if (query.getPickingTaskType() == 15) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (21) ");// 海外仓头程单
        }
        else if (query.getPickingTaskType() == PdaPickingType.PAC_RU_MULTISPECIES.intCode()) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (314) ");// 集包多品
        }
        else if (query.getPickingTaskType() == PdaPickingType.PAC_RU_NOT_MULTISPECIES.intCode()) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (311,312,313) ");// 集包非多品
        }
        else if (query.getPickingTaskType() == -20) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type != 20 ");// 海外仓备货
        }
        else if (PdaPickingType.OVERSIZE_MULTISPECIES.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (34) ");// 超体积多品
        }
        else if (PdaPickingType.OVERSIZE_NOT_MULTISPECIES.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (31,32,33) ");// 超体积非多品
        }
        else if (PdaPickingType.TRANSFER_SINGLEMULTIPLE.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (51,52) ");//中转仓非多品
        }
        else if (PdaPickingType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (53) ");//中转仓多品
        }
        else if (PdaPickingType.TEMU_MULTIPLEMULTIPLE.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (56) ");//拼多多备货
        }
        else if (PdaPickingType.JIT_MULTIPLEMULTIPLE.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (61) ");//JIT多品
        }
        else if (PdaPickingType.JIT_NOTMULTIPLEMULTIPLE.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (59,60) ");// JIT非多品
        }
        else if (PdaPickingType.JIT_ASN_MULTIPLEMULTIPLE.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (65) ");//仓发多品
        }
        else if (PdaPickingType.JIT_ASN_NOTMULTIPLEMULTIPLE.intCode().equals(query.getPickingTaskType())) {
            request.addSqlDataParam("NORMAL_TASK", " AND wpt.task_type in (64) ");// 仓发单品
        }

        if (CollectionUtils.isNotEmpty(query.getTaskTypeNotIN())){
            request.addSqlDataParam("TASK_TYPE_NOT_IN", " AND wpt.task_type not in ("+ StringUtils.join(query.getTaskTypeNotIN(),",")+") ");// 通道拣货任务
        }

        // 拣货任务状态列表
        if (CollectionUtils.isNotEmpty(query.getTaskStatuss())) {
            request.addDataParam("taskStatuss", DataType.INT, query.getTaskStatuss());
        }
        // 拣货任务状态列表
        if (CollectionUtils.isNotEmpty(query.getGridStatusList())) {
            request.addDataParam("gridStatusList", DataType.INT, query.getGridStatusList());
        }

        request.addDataParam("grid_status", DataType.INT, query.getGridStatus());
        // 拣货任务扩展表
        if (query.isQueryGridExpand()){
            request.addSqlDataParam("QUERY_GRID_EXPAND_START", "SELECT * FROM ( ");
            request.addSqlDataParam("QUERY_GRID_EXPAND_END", " ) wpt LEFT JOIN pick_task_expand pte ON wpt.id = pte.task_id");
        }

        request.addDataParam("freeze_status", DataType.INT, query.getNotFreezeStatus());

        //不是优选仓跟中转仓
        if (!query.isPacStatus()) {
            request.addSqlDataParam("IS_ASN_STATUS", " AND (wpt.is_asn not in(20,30,31) or wpt.is_asn is null)");
        }
        else if (CollectionUtils.isNotEmpty(query.getOptimalIntCodes())) {
            request.addSqlDataParam("IS_ASN_STATUS", " AND wpt.is_asn in(30,31)");
        }

        if (query.getReadonly() != null && query.getReadonly()){
            request.setReadOnly(true);
        }
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public int queryWhPickingTaskCount(WhPickingTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPickingTaskCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public List<WhPickingTask> queryWhPickingTaskList() {
        SqlerRequest request = new SqlerRequest("queryWhPickingTaskList");
        return SqlerTemplate.query(request, new WhPickingTaskBoxMapper());
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public List<WhPickingTask> queryWhPickingTaskList(WhPickingTaskQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhPickingTaskList");
        setQueryCondition(request, query);
        // 时间顺序
        request.addSqlDataParam("ORDER_BY", "ORDER BY wpt.task_level DESC , wpt.id");
        if (pager != null) {
            //request.addFetch(pager.getPageNo(), pager.getPageSize());
            SQLDialect dial = DialectFactory.createDialect(null);
            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();
            long end = start + pager.getPageSize() - 1L;
            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }

        return SqlerTemplate.query(request, new WhPickingTaskBoxMapper(query));
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public WhPickingTask queryWhPickingTask(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhPickingTaskByPrimaryKey");
        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhPickingTaskMapper());
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public WhPickingTask queryWhPickingTask(WhPickingTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPickingTask");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhPickingTaskMapper());
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void createWhPickingTask(WhPickingTask entity) {
        SqlerRequest request = new SqlerRequest("createWhPickingTask");
        request.addDataParam(WhPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(WhPickingTaskDBField.TASK_TYPE, DataType.INT, entity.getTaskType());
        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
        request.addDataParam(WhPickingTaskDBField.CREATED_BY, DataType.INT,
                entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(WhPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPickingTaskDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
        request.addDataParam(WhPickingTaskDBField.IS_ASN, DataType.INT, entity.getIsAsn());
        request.addDataParam(WhPickingTaskDBField.CROSS_FLOOR, DataType.BOOLEAN, entity.getCrossFloor());
        request.addDataParam(WhPickingTaskDBField.WAYBILL_TYPE, DataType.INT, entity.getWaybillType());
        request.addDataParam(WhPickingTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void updateWhPickingTask(WhPickingTask entity) {
        SqlerRequest request = new SqlerRequest("updateWhPickingTaskByPrimaryKey");
        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(WhPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(WhPickingTaskDBField.TASK_TYPE, DataType.INT, entity.getTaskType());
        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());

        request.addDataParam(WhPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(WhPickingTaskDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
        request.addDataParam(WhPickingTaskDBField.WAYBILL_TYPE, DataType.INT, entity.getWaybillType());
        request.addDataParam(WhPickingTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
        request.addDataParam(WhPickingTaskDBField.CROSS_FLOOR, DataType.BOOLEAN, entity.getCrossFloor());
        request.addDataParam(WhPickingTaskDBField.SOW_DIFFER_QUANTITY, DataType.INT, entity.getSowDifferQuantity());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
        request.addDataParam(WhPickingTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
        request.addDataParam(WhPickingTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
        request.addDataParam(WhPickingTaskDBField.CHECK_QUANTITY, DataType.INT, entity.getCheckQuantity());
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void batchCreateWhPickingTask(List<WhPickingTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhPickingTask");
            for (WhPickingTask entity : entityList) {
                request.addBatchDataParam(WhPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(WhPickingTaskDBField.TASK_TYPE, DataType.INT, entity.getTaskType());
                request.addBatchDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
                request.addBatchDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
                request.addBatchDataParam(WhPickingTaskDBField.CREATED_BY, DataType.INT, entity.getCreatedBy());
                request.addBatchDataParam(WhPickingTaskDBField.IS_ASN, DataType.INT, entity.getIsAsn());
                request.addBatchDataParam(WhPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate());
                request.addBatchDataParam(WhPickingTaskDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
                request.addBatchDataParam(WhPickingTaskDBField.WAYBILL_TYPE, DataType.INT, entity.getWaybillType());
                request.addBatchDataParam(WhPickingTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
                request.addBatchDataParam(WhPickingTaskDBField.CROSS_FLOOR, DataType.BOOLEAN, entity.getCrossFloor());
                request.addBatchDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                        entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void batchUpdateWhPickingTask(List<WhPickingTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhPickingTaskByPrimaryKey");
            for (WhPickingTask entity : entityList) {
                request.addBatchDataParam(WhPickingTaskDBField.ID, DataType.INT, entity.getId());
                request.addBatchDataParam(WhPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(WhPickingTaskDBField.TASK_TYPE, DataType.INT, entity.getTaskType());
                request.addBatchDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
                request.addBatchDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
                request.addBatchDataParam(WhPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate());
                request.addBatchDataParam(WhPickingTaskDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
                request.addBatchDataParam(WhPickingTaskDBField.WAYBILL_TYPE, DataType.INT, entity.getWaybillType());
                request.addBatchDataParam(WhPickingTaskDBField.CROSS_FLOOR, DataType.BOOLEAN, entity.getCrossFloor());
                request.addBatchDataParam(WhPickingTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
                request.addBatchDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                        entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void deleteWhPickingTask(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhPickingTaskByPrimaryKey");
        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    /**
     * 领取拣货任务
     */
    public int receivePickingTask(WhPickingTask whPickingTask) {
        SqlerRequest request = new SqlerRequest("receivePickingTask");

        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, whPickingTask.getId());
        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, PickingTaskStatus.RECEIVED.intCode());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, whPickingTask.getReceivePerson());
        request.addBatchDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                whPickingTask.getLastUpdateBy() == null ? DataContextHolder.getUserId()
                        : whPickingTask.getLastUpdateBy());
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        if (whPickingTask.getTaskStatus() != null
                && PickingTaskStatus.RECEIVED.intCode().equals(whPickingTask.getTaskStatus())
                && whPickingTask.getTaskType() != null
                && (PickingTaskType.MULTIPLEMULTIPLE.intCode().equals(whPickingTask.getTaskType())
                        || PickingTaskType.OPTIMAL_RU_MM.intCode().equals(whPickingTask.getTaskType()))) {
            request.addDataParam("beforeStatus", DataType.INT, PickingTaskStatus.RECEIVED.intCode());
        }
        else {
            request.addDataParam("beforeStatus", DataType.INT, PickingTaskStatus.UNRECEIVED.intCode());
        }
        return SqlerTemplate.execute(request);
    }

    /**
     * 领取并打印拣货任务
     */
    public int receiveAndPrintingPickingTask(WhPickingTask whPickingTask) {
        SqlerRequest request = new SqlerRequest("receiveAndPrintingPickingTask");

        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, whPickingTask.getId());
        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, whPickingTask.getTaskStatus());
        request.addDataParam(WhPickingTaskDBField.IS_PRINTING, DataType.INT, whPickingTask.getIsPrinting());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, whPickingTask.getReceivePerson());

        request.addDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, whPickingTask.getReceiveDate());
        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                whPickingTask.getLastUpdateBy() == null ? DataContextHolder.getUserId()
                        : whPickingTask.getLastUpdateBy());

        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));

        request.addDataParam(WhPickingTaskDBField.PRINT_USER, DataType.INT,
                whPickingTask.getPrintUser() == null ? DataContextHolder.getUserId() : whPickingTask.getPrintUser());
        request.addDataParam(WhPickingTaskDBField.PRINT_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));

        request.addDataParam("beforeStatus", DataType.INT, PickingTaskStatus.UNRECEIVED.intCode());
        return SqlerTemplate.execute(request);
    }

    /**
     * 
     * @Description: 查询任务等级最大值
     *
     * @return
     * @return: int
     * @Author: qinyangkai
     * @Date: 2018/09/18
     * @Version: 0.0.1
     */
    public int queryWhPickingTaskMax() {
        SqlerRequest request = new SqlerRequest("queryWhPickingTaskMax");
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * 
     */
    public int setTopList(List<Integer> taskIds, Integer taskLevel) {
        SqlerRequest request = new SqlerRequest("setTopList");

        request.addDataParam(WhPickingTaskDBField.TASK_LEVEL, DataType.INT, taskLevel);

        request.addDataParam("taskIds", DataType.INT, taskIds);

        return SqlerTemplate.execute(request);
    }

    /**
     * 
     */
    public List<WhPickingTaskSku> queryPrintingWhPickingTask(Integer id) {
        SqlerRequest request = new SqlerRequest("queryPrintingWhPickingTask");
        request.addDataParam("id", DataType.INT, id);
        return SqlerTemplate.query(request, new WhPickingTaskPrintingMapper());
    }

    /**
     * 
     */
    public WhPickingTask queryPickingQuantity(String taskNo, Integer taskType) {
        SqlerRequest request = new SqlerRequest("queryPickingQuantity");
        request.addDataParam("taskNo", DataType.STRING, taskNo);
        request.addDataParam("taskType", DataType.INT, taskType);
        return SqlerTemplate.queryForObject(request, new WhPickingTaskCheckMapper());
    }

    /**
     * 修改拣货任务状态
     */
    @Override
    public int updatePickingTaskStatus(WhPickingTask whPickingTask, Integer beforeTaskStatus,
            List<Integer> beforeTaskStatuss) {
        SqlerRequest request = new SqlerRequest("updatePickingTaskStatus");

        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, whPickingTask.getId());
        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, whPickingTask.getTaskStatus());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, whPickingTask.getReceivePerson());
        request.addBatchDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                whPickingTask.getReceiveDate());
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                whPickingTask.getLastUpdateBy() == null ? DataContextHolder.getUserId()
                        : whPickingTask.getLastUpdateBy());
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPickingTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP,
                whPickingTask.getPickingEndDate());
        request.addDataParam(WhPickingTaskDBField.IS_PRINTING, DataType.INT, whPickingTask.getIsPrinting());
        request.addDataParam(WhPickingTaskDBField.CROSS_FLOOR, DataType.BOOLEAN, whPickingTask.getCrossFloor());
        request.addDataParam(WhPickingTaskDBField.CHECK_QUANTITY, DataType.INT, whPickingTask.getCheckQuantity());

        request.addDataParam("beforeTaskStatuss", DataType.INT, beforeTaskStatuss);
        request.addDataParam("beforeTaskStatus", DataType.INT, beforeTaskStatus);
        return SqlerTemplate.execute(request);

    }

    /**
     * 查询拣货任务里面的拣货总数
     */
    @Override
    public int queryPickQuantity(WhPickingTask whPickingTask) {
        SqlerRequest request = new SqlerRequest("queryPickQuantity");
        request.addDataParam(WhPickingTaskDBField.TASK_NO, DataType.STRING, whPickingTask.getTaskNo());
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * 解绑任务
     */
    @Override
    public int untiedPickingTask(WhPickingTask whPickingTask) {
        SqlerRequest request = new SqlerRequest("untiedPickingTask");
        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, whPickingTask.getId());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, whPickingTask.getReceivePerson());
        request.addDataParam(WhPickingTaskDBField.TASK_TYPE, DataType.INT, whPickingTask.getTaskType());
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                whPickingTask.getLastUpdateBy() == null ? DataContextHolder.getUserId()
                        : whPickingTask.getLastUpdateBy());
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        return SqlerTemplate.execute(request);
    }

    /**
     * PDA专用方法：修改状态，从部分拣货完成状态改为已领取
     */
    @Override
    public int updatePickingTaskForPartToReceived(WhPickingTask whPickingTask) {
        SqlerRequest request = new SqlerRequest("updatePickingTaskForPartToReceived");

        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, whPickingTask.getId());
        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT,
                PickingTaskStatus.STRIDERECEIVED.intCode());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, whPickingTask.getReceivePerson());
        request.addBatchDataParam(WhPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                whPickingTask.getLastUpdateBy() == null ? DataContextHolder.getUserId()
                        : whPickingTask.getLastUpdateBy());
        request.addBatchDataParam(WhPickingTaskDBField.CROSS_FLOOR, DataType.BOOLEAN, whPickingTask.getCrossFloor());
        request.addBatchDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addSqlDataParam("beforeStatus", " AND task_status in (" + PickingTaskStatus.NEWPARTCOMPLETED.intCode()
                + "," + PickingTaskStatus.OLDPARTCOMPLETED.intCode() + ")");
        return SqlerTemplate.execute(request);
    }

    @Override
    public int updatePickingTaskUser(WhPickingTask updateTask) {
        SqlerRequest request = new SqlerRequest("updatePickingTaskUser");
        request.addDataParam(WhPickingTaskDBField.ID, DataType.INT, updateTask.getId());
        request.addDataParam(WhPickingTaskDBField.TASK_STATUS, DataType.INT, updateTask.getTaskStatus());
        request.addDataParam(WhPickingTaskDBField.RECEIVE_PERSON, DataType.INT, updateTask.getReceivePerson());
        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_BY, DataType.INT,
                updateTask.getLastUpdateBy() == null ? DataContextHolder.getUserId()
                        : updateTask.getLastUpdateBy());
        request.addDataParam(WhPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        return SqlerTemplate.execute(request);
    }

    @Override
    public void deleteByIds(List<Integer> taskId) {
        if (CollectionUtils.isEmpty(taskId)) return;
        SqlerRequest request = new SqlerRequest("deletePickingTaskByIds");
        request.addSqlDataParam("Ids","("+ org.apache.commons.lang.StringUtils.join(taskId,",")+")");
        SqlerTemplate.execute(request);
    }

    @Override
    public List<WhPickingTaskSku> querySecondaryDistributionSku(String taskNo,List<Integer> taskType) {
        if (StringUtils.isBlank(taskNo) || taskType==null) {
            return new ArrayList<>();
        }
        SqlerRequest request = new SqlerRequest("querySecondaryDistributionSku");
        request.addDataParam(WhPickingTaskDBField.TASK_NO,DataType.STRING,taskNo);
        request.addDataParam("taskTypeList",DataType.INT,taskType);
        return SqlerTemplate.query(request, new RowMapper<WhPickingTaskSku>() {
            @Override
            public WhPickingTaskSku mapRow(ResultSet rs, int i) throws SQLException {
                WhPickingTaskSku whPickingTaskSku=new WhPickingTaskSku();
                whPickingTaskSku.setPickQuantity(rs.getObject("ts.pick_quantity") == null ? null
                        : rs.getInt("ts.pick_quantity"));
                whPickingTaskSku.setSku(rs.getString("ts.sku"));
                return whPickingTaskSku;
            }
        });
    }
}