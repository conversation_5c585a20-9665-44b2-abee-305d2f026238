package com.estone.transfer.bean;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class WhFbaAllocationQueryCondition extends WhFbaAllocation {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private String consignOrderNoStr;//发货单号

    private Boolean queryAsnPickBoxNumber;

    private Integer number;//分拣箱号

    private String pickBoxNumberStr;//分拣箱号

    private String warehouseCode;//到仓ID

    private String startTime;// 创建时间

    private String endTime;// 创建时间

    private String startPickTime;// 拣货完成时间

    private String endPickTime;// 拣货完成时间

    private String startSowTime;// 播种完成时间

    private String endSowTime;// 播种完成时间

    private String startPackTime;// 包装完成时间

    private String endPackTime;// 包装完成时间

    private String receiveStartTime;// 接单时间

    private String receiveEndTime;// 接单时间

    private String cancelStartTime;//取消时间

    private String cancelEndTime;//取消时间

    private String boxPushStartTime;//装箱推送时间

    private String boxPushEndTime;//装箱推送时间

    private String loadStartTime;//订单对应结袋卡装车时间

    private String loadEndTime;//订单对应结袋卡装车时间

    private String upDiff; // 上架差异 null:不限 diff: 差异 finish:上架完成

    private List<Integer> statusList = new ArrayList<>();

    private String sku;

    private String fnSku;

    private List<Integer> ids;

    private String splitShipmentId;//TODO pc端货件单号查询，区分上架时的货件单号查询参数shipmentId

    private List<String> shipmentIdList;

    private String upStartTime;// 海外仓上架时间

    private String upEndTime;// 海外仓上架时间

    private Boolean isAmazonFba;

    // 状态，多个之间用逗号分隔
    private String statusStr;

    /**
     * 是否中转仓订单
     */
    private Boolean isTransferOrder;

    private Boolean queryWhAsnExtra;//查询地址

    private String upStartDeliverTime;// 交运时间

    private String upEndDeliverTime;// 交运时间

    private Boolean pickOut;

    /**
     * 加工类型
     */
    private Integer processType;

    /**
     * 重新加工
     */
    private Boolean reProcess;

    /**
     * 海外仓发货单类型
     */
    private Integer packageMethod;

    private List<Integer> packageMethodList;

    /**
     * 合单时间
     */
    private String fromMergeTime;
    private String toMergeTime;

    /**
     * sku分配的库位
     */
    private String allotLocationNumbers;

    private String rightLikeTrackingNumber;

    /**
     * 揽收单号
     */
    private String pickupOrderId;

    /**
     * 通道
     */
    private String access;

    /**
     * 区域
     */
    private String area;

    /**
     * 起始剩余时间
     */
    private Integer fromRemainTime;

    /**
     * 截止剩余时间
     */
    private Integer toRemainTime;

    /**
     * 是否超时
     */
    private Boolean overTime;

    /**
     * 是否可打印smt和temu的标签和箱唛
     */
    private Boolean canPrint;

    //是否退仓
    private Boolean isReturn;

    private String fromConfirmTime;
    private String toConfirmTime;

    private String orderType;

    private String queryOrderType;

    private String queryOrderTypeVal;
    // 追踪号不为空的项
    private Boolean shippingOrderNoNotNull;

    private Boolean packCheckSplit;

    //仓发LBX号
    private String lbxNo;

    private Integer loadBy;
    private boolean isLoaded;


    /** 是否欧洲站点 */
    private Boolean isEurope;

    private boolean queryShipmentIds = false;

    private Boolean queryAsnFirst;

    public Boolean lbxNotNull;
    // 包装优先级 仓发->Shein->temu
    private Boolean packPriority;

    private String gpsrMessage;

    // 揽收方式 CollectMethodEnum
    private Integer collectMethod;

    /**
     * APV类型列表，用于支持多个APV类型的查询
     */
    private List<String> apvTypeList;
}