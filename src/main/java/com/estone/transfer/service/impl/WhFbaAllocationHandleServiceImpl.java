package com.estone.transfer.service.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.apv.enums.ApvTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.android.PdaExceptionCode;
import com.estone.android.domain.AndroidProductDo;
import com.estone.apv.bean.WhApvBatch;
import com.estone.apv.bean.WhApvBatchDetail;
import com.estone.apv.bean.WhApvBatchItem;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.common.ApvBatchItemStatus;
import com.estone.apv.common.ApvBatchStatus;
import com.estone.apv.common.ApvBatchType;
import com.estone.apv.dao.WhApvBatchDao;
import com.estone.apv.dao.WhApvBatchDetailDao;
import com.estone.apv.dao.WhApvBatchItemDao;
import com.estone.apv.enums.WhApvOutStockChainStatusEnum;
import com.estone.apv.service.AliExpressCallService;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.asn.service.WhAsnExtraService;
import com.estone.common.SaleChannel;
import com.estone.common.enums.LogModule;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiPrintSkuResult;
import com.estone.common.util.model.ApiResult;
import com.estone.picking.bean.*;
import com.estone.picking.enums.*;
import com.estone.picking.service.PickTaskExpandService;
import com.estone.picking.service.WhPickingTaskItemService;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.picking.service.WhPickingTaskSkuService;
import com.estone.scan.deliver.bean.WhWarehouseShipment;
import com.estone.scan.deliver.bean.WhWarehouseShipmentQueryCondition;
import com.estone.scan.deliver.service.WhWarehouseShipmentService;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.transfer.bean.*;
import com.estone.transfer.domain.BarnSkuPrintRequestBody;
import com.estone.transfer.domain.BarnSkuPrintVo;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.enums.FBAChangeDeliveryTypeEnum;
import com.estone.transfer.enums.FBAChangeOrderItemStatusEnum;
import com.estone.transfer.enums.TmsSendMsgType;
import com.estone.transfer.service.*;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.estone.warehouse.service.WhBoxService;
import com.global.iop.util.ApiException;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: Yimeil
 * @Date: 2021/6/29 17:52
 * @Version: 1.0.0
 */
@Service
@Slf4j
public class WhFbaAllocationHandleServiceImpl implements WhFbaAllocationHandleService {

    private static final String STATIC_FILE_PATH = "/usr/local/erp/static";
    private static final String SKU_TAG_FILE_PATH = "/fba/asn/SkuTagPrint/";

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhFbaAllocationItemService whFbaAllocationItemService;

    @Resource
    private WhFbaAllocationStockService whFbaAllocationStockService;

    @Resource
    private WhPickingTaskService whPickingTaskService;

    @Resource
    private WhPickingTaskItemService whPickingTaskItemService;

    @Resource
    private WhPickingTaskSkuService whPickingTaskSkuService;

    @Resource
    private PickTaskExpandService pickTaskExpandService;

    @Resource
    private WhApvBatchItemDao whApvBatchItemDao;

    @Resource
    private WhApvBatchDetailDao whApvBatchDetailDao;

    @Resource
    private WhApvBatchDao whApvBatchDao;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhFbaChangeService whFbaChangeService;


    @Resource
    private WhFbaAllocationHandleService whFbaAllocationHandleService;

    @Resource
    private AliExpressCallService aliExpressCallService;

    @Resource
    private WhAsnExtraService whAsnExtraService;

    @Resource
    private WhWarehouseShipmentService whWarehouseShipmentService;

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(1);

    final static SystemLogUtils SCANSHIPMENTLOG = SystemLogUtils.create(LogModule.SCANSHIPMENT.getCode());

    @Override
    public ResponseJson doBox(WhFbaAllocation pAllocation, WhFbaAllocation dbAllocation) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        List<WhFbaAllocationItem> items = pAllocation.getItems();
        Collections.sort(dbAllocation.getItems());
        List<String> oldMes = dbAllocation.getItems().stream().map(item -> item.getLoadDataMes())
                .collect(Collectors.toList());
        String error = checkBoxAndBuildFBA(items, dbAllocation);
        if (error != null) {
            response.setMessage(error);
            return response;
        }

        Map<String, List<WhFbaAllocationItem>> dbMap = dbAllocation.getItems().stream()
                .collect(Collectors.groupingBy(i -> i.getFnSku() + (i.getBoxNo() == null ? 1 : i.getBoxNo())));

        Map<String, List<WhFbaAllocationItem>> pMap = pAllocation.getItems().stream()
                .collect(Collectors.groupingBy(i -> i.getFnSku() + (i.getBoxNo() == null ? 1 : i.getBoxNo())));

        List<WhFbaAllocationItem> updateItems = pAllocation.getItems().stream()
                .filter(i -> CollectionUtils.isNotEmpty(dbMap.get(i.getFnSku() + i.getBoxNo())))
                .collect(Collectors.toList());

        List<WhFbaAllocationItem> createItems = pAllocation.getItems().stream()
                .filter(i -> CollectionUtils.isEmpty(dbMap.get(i.getFnSku() + i.getBoxNo())))
                .collect(Collectors.toList());

        List<WhFbaAllocationItem> deleteItems = dbAllocation.getItems().stream().filter(
                i -> CollectionUtils.isEmpty(pMap.get(i.getFnSku() + (i.getBoxNo() == null ? 1 : i.getBoxNo()))))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(updateItems)) {
            // 更新装车数量和长宽高重量
            updateItems.forEach(item -> {
                dbMap.get(item.getFnSku() + item.getBoxNo()).forEach(i -> {
                    WhFbaAllocationItem update = new WhFbaAllocationItem();
                    update.setId(i.getId());
                    update.setBoxNo(item.getBoxNo());
                    update.setProductWeight(item.getProductWeight());
                    update.setProductLength(item.getProductLength());
                    update.setProductHeight(item.getProductHeight());
                    update.setProductWidth(item.getProductWidth());
                    update.setLoadNum(item.getLoadNum());
                    // 套装
                    if (i.getSuitFlag() != null && i.getSuitFlag().equals(1)) {
                        update.setLoadingQuantity(
                                item.getLoadNum() * (i.getSkuSuitNum() == null ? 0 : i.getSkuSuitNum()));
                        if (dbAllocation.getIsAsn() != null && dbAllocation.getIsAsn()) {
                            update.setLoadingQuantity(
                                    (int) Math.floor((double) (i.getPickQuantity() == null ? 0 : i.getPickQuantity())
                                            / (item.getLoadNum() == null || item.getLoadNum() == 0 ? 1
                                                    : item.getLoadNum())));
                        }
                    }
                    else {
                        update.setLoadingQuantity(item.getLoadNum());
                    }
                    whFbaAllocationItemService.updateWhFbaAllocationItem(update);
                });

            });

        }

        if (CollectionUtils.isNotEmpty(createItems)) {

            // 根据fnSku分组
            Map<String, List<WhFbaAllocationItem>> suitFnSkuMap = dbAllocation.getItems().stream().collect(
                    Collectors.groupingBy(i -> i.getFnSku(), Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<WhFbaAllocationItem>(Comparator.comparing(o -> o.getProductSku()))),
                            ArrayList::new)));

            createItems.forEach(item -> {
                // 复制一个sku明细，更新装车数量和长宽高重量，新增一条箱子明细到数据库
                List<WhFbaAllocationItem> existList = suitFnSkuMap.get(item.getFnSku());
                existList.forEach(f -> {
                    WhFbaAllocationItem create = new WhFbaAllocationItem();
                    BeanUtils.copyProperties(f, create);
                    create.setBoxNo(item.getBoxNo());
                    create.setProductWeight(item.getProductWeight());
                    create.setProductLength(item.getProductLength());
                    create.setProductHeight(item.getProductHeight());
                    create.setProductWidth(item.getProductWidth());
                    create.setLoadNum(item.getLoadNum());
                    // 套装
                    if (f.getSuitFlag() != null && f.getSuitFlag().equals(1)) {
                        create.setLoadingQuantity(
                                item.getLoadNum() * (f.getSkuSuitNum() == null ? 0 : f.getSkuSuitNum()));
                        if (dbAllocation.getIsAsn() != null && dbAllocation.getIsAsn()) {
                            create.setLoadingQuantity(
                                    (int) Math.floor((double) (f.getPickQuantity() == null ? 0 : f.getPickQuantity())
                                            / (item.getLoadNum() == null || item.getLoadNum() == 0 ? 1
                                                    : item.getLoadNum())));
                        }
                    }
                    else {
                        create.setLoadingQuantity(item.getLoadNum());
                    }
                    whFbaAllocationItemService.createWhFbaAllocationItem(create);
                });

            });
        }
        if (CollectionUtils.isNotEmpty(deleteItems)) {
            deleteItems.forEach(item -> whFbaAllocationItemService.deleteWhFbaAllocationItem(item.getId()));
        }

        // 重置箱数
        List<WhFbaAllocationItem> newItem = new ArrayList<>();
        newItem.addAll(updateItems);
        newItem.addAll(createItems);
        Collections.sort(newItem);
        List<String> newMes = newItem.stream().map(item -> item.getLoadDataMes()).collect(Collectors.toList());
        whFbaAllocationService.updateWhFbaAllocation(pAllocation);
        if (dbAllocation.getIsAsn() != null && dbAllocation.getIsAsn()) {
            try {
                WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
                query.setId(dbAllocation.getId());
                List<WhFbaAllocation> allocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
                if (CollectionUtils.isNotEmpty(allocations))
                    whFbaAllocationHandleService.doBoxCheck(allocations.get(0));
            }
            catch (Exception e) {
                log.error("确认装箱失败" + e.getMessage(), e);
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        }
        if (!dbAllocation.isFba())
            //解绑拣货任务周转筐
            this.boxFinishUnbindBoxNo(dbAllocation);

        SystemLogUtils.FBAALLOCATIONLOG.log(pAllocation.getId(), "海外仓出库单装箱",
                new String[][] { { "修改前", JSON.toJSONString(oldMes) }, { "修改后", JSON.toJSONString(newMes) } });
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 装箱计算退回数量
     *
     * @param dbAllocation
     * @param itemList
     * @return
     */
    private List<WhFbaAllocationItem> checkBoxReturn(WhFbaAllocation dbAllocation, List<WhFbaAllocationItem> itemList) {
        Map<String, List<WhFbaAllocationItem>> dbMap = dbAllocation.getItems().stream()
                .collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku));
        Map<String, List<WhFbaAllocationItem>> updateMap = itemList.stream()
                .collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku));

        List<WhFbaAllocationItem> updateList = new ArrayList<>();
        dbMap.forEach((k, v) -> {
            Integer dQuantity = v.stream().mapToInt(i -> i.getLoadingQuantity() == null ? 0 : i.getLoadingQuantity())
                    .sum();
            if (updateMap == null || CollectionUtils.isNotEmpty(updateMap.get(k))) {
                return;
            }
            Integer uQuantity = updateMap.get(k).stream()
                    .mapToInt(u -> u.getLoadingQuantity() == null ? 0 : u.getLoadingQuantity()).sum();
            if (dQuantity > uQuantity) {
                WhFbaAllocationItem item = v.get(0);
                item.setLoadingQuantity(dQuantity - uQuantity);
                updateList.add(item);
            }
        });

        return updateList;

    }

    /**
     * 校验装箱参数
     *
     * @param items
     * @param dbAllocation
     * @return
     */
    private String checkBoxAndBuildFBA(List<WhFbaAllocationItem> items, WhFbaAllocation dbAllocation) {
        if (CollectionUtils.isEmpty(items)) {
            return "装箱条目不能为空！";
        }
        List<String> dbSkuList = dbAllocation.getItems().stream().map(WhFbaAllocationItem::getFnSku).distinct()
                .collect(Collectors.toList());
        List<String> pSkuList = items.stream().map(WhFbaAllocationItem::getFnSku).distinct()
                .collect(Collectors.toList());
        // 对比修改前后是否缺少SKU
        if (pSkuList.size() != dbSkuList.size()) {
            return "SKU缺失，如果没有装箱数量请用0填充";
        }
        Map<Integer, WhFbaAllocationItem> itemMap = items.stream().filter(
                i -> i.getProductHeight() != null && i.getProductLength() != null && i.getProductWidth() != null)
                .collect(Collectors.toMap(WhFbaAllocationItem::getBoxNo, o -> o, (o1, o2) -> o1));
        // 移除装箱数量为0的数据
        items.removeIf(i -> (i.getBoxNo() != 1) && (i.getLoadNum() == null || i.getLoadNum() == 0));

        // 分组合计装箱数量
        Map<String, Integer> loadNumMap = items.stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku,
                Collectors.summingInt(i -> i.getLoadNum() == null ? 0 : i.getLoadNum())));

        Map<String, Integer> gridMap = dbAllocation.getItems().stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(i -> i.getFnSku() + i.getProductSku()))),
                        ArrayList::new))
                .stream()
                .collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku,
                        Collectors.summingInt(i -> Math.round((float) (i.getPickQuantity() == null ? 0 : i.getPickQuantity())
                                / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))));

        AtomicReference<String> errorMsg = new AtomicReference<>();
        items.forEach(item -> {
            if (dbAllocation.isFba()) {
                if (loadNumMap.get(
                        item.getFnSku()) > (gridMap.get(item.getFnSku()) == null ? 0 : gridMap.get(item.getFnSku()))) {
                    errorMsg.set("装箱数量不能大于播种数量！");
                    return;
                }
            }
            else if (loadNumMap.get(item.getFnSku()) > item.getPickQuantity()) {
                errorMsg.set("装箱数量不能大于拣货数量！");
                return;
            }
            if (itemMap != null && itemMap.get(item.getBoxNo()) != null) {
                item.setProductWidth(itemMap.get(item.getBoxNo()).getProductWidth());
                item.setProductHeight(itemMap.get(item.getBoxNo()).getProductHeight());
                item.setProductLength(itemMap.get(item.getBoxNo()).getProductLength());
                item.setProductWeight(itemMap.get(item.getBoxNo()).getProductWeight());
            }
        });
        return errorMsg.get();
    }

    @Override
    @StockServicelock
    @Transactional(value = "txManager")
    public void exeAllot(WhFbaAllocation allocation) throws Exception {
        List<WhFbaAllocationItem> items = allocation.buildGroupItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new Exception("FBA调拨发货单无明细数据!");
        }
        if (!AsnPrepareStatus.WAITING_ALLOT.intCode().equals(allocation.getStatus())
                && !AsnPrepareStatus.ALLOTING.intCode().equals(allocation.getStatus())) {
            throw new Exception(String.format("FBA调拨发货单状态非待分配或分配中!"));
        }

        // 执行分配
        String allotError = whFbaAllocationService.doGenerateFbaAllocation(allocation); //doAllot(allocation, null);
        if (StringUtils.isNotBlank(allotError))
            throw new RuntimeException(allotError);

    }

    @Override
    public String doAllot(WhFbaAllocation allocation, Map<String, TransferStock> stockMap) {
        // 判断是否全部分配完毕
        boolean bool = allocation.getItems().stream()
                .anyMatch(item -> item.getAllotQuantity() == null || item.getAllotQuantity() < item.getSkuQuantity());
        String errorMsg = null;
        // 分配
        if (allocation.getItems() != null && bool) {
            // TODO 更新库存
            try {
                errorMsg = whFbaAllocationStockService.allot(allocation, stockMap);
                if (StringUtils.isNotBlank(errorMsg) && !errorMsg.equals("true"))
                    return errorMsg;
            }
            catch (BusinessException e) {
                log.info(e.getMessage());
                return e.getMessage();
            }
        }
        bool = allocation.getItems().stream()
                .anyMatch(item -> item.getAllotQuantity() == null || item.getAllotQuantity() < item.getSkuQuantity());
        if (!bool) {
            allocation.setStatus(AsnPrepareStatus.WAITING_GEN.intCode());
            allocation.setLocalAllot(StringUtils.isNotBlank(errorMsg) && errorMsg.equals("true"));
            // 分配成功推送分配时间到OMS
            whFbaAllocationService.sendMsg(allocation);
        }
        return null;
    }

    @Override
    public void updateWhAsnAndItem(WhFbaAllocation allocation, String content) {
        List<WhFbaAllocationItem> items = allocation.getItems();
        int updateInt = whFbaAllocationService.updateWhFbaAllocation(allocation);
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(items);
        if (updateInt > 0)
            SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), content);
    }

    @Override
    public int createPickingTask(List<WhFbaAllocation> allocationList) throws Exception {
        int createTaskNum = 0;
        Integer maxGridCount = 1;
        if (!allocationList.get(0).isFba()) {
            maxGridCount = 20;// 海外仓头程单只支持单个生成
        }
        // 每maxGridCount个一个批次
        int total = allocationList.size();
        int pageSize = maxGridCount;
        int totalPage = (total + pageSize - 1) / pageSize;
        for (int pageNo = 1; pageNo <= totalPage; pageNo++) {
            List<WhFbaAllocation> paging = PagingUtils.paging(allocationList, pageNo, pageSize);
            if (CollectionUtils.isEmpty(paging)) {
                continue;
            }
            mainCreateTask(paging);
            createTaskNum++;
        }
        return createTaskNum;
    }

    // 公用的生成拣货任务方法
    @Override
    public void mainCreateTask(List<WhFbaAllocation> paging) {
        boolean isFba = paging.get(0).isFba();
        // 生成拣货任务
        WhPickingTask whPickingTask = new WhPickingTask();
        whPickingTask.setTaskNo(CreateTaskNoUtils.createTaskNo());
        if (isFba) {
            whPickingTask.setTaskType(PickingTaskType.ASN_PREPARE.intCode());
        }
        else {
            // 海外仓头程单根据APV类型区分单品和多品
            Integer taskType = determineAsnFirstTaskType(paging);
            whPickingTask.setTaskType(taskType);
        }
        whPickingTask.setIsPrinting(PickingTaskIsPrinting.UNPRINTING.intCode());
        whPickingTask.setTaskStatus(PickingTaskStatus.UNRECEIVED.intCode());
        whPickingTask.setWarehouseType(1);
        whPickingTask.setWaybillType(0);
        whPickingTask.setCreatedBy(DataContextHolder.getUserId());
        whPickingTask.setCreatedDate(new Timestamp(new Date().getTime()));
        whPickingTask.setTaskLevel(0);
        whPickingTask.setIsAsn(whPickingTask.getTaskType());
        whPickingTaskService.createWhPickingTask(whPickingTask);
        // 创建拣货任务播种记录
        pickTaskExpandService.createPickTaskExpandByTask(whPickingTask);

        // 拣货任务SKU信息的集合
        Map<String, Integer> skuMap = new HashMap<String, Integer>();

        // 拣货任务详情的集合
        List<WhPickingTaskItem> items = new ArrayList<>();

        List<WhFbaAllocation> updateAllocationList = new ArrayList<>();
        // 算出仓库数
        List<Integer> whApvIds = new ArrayList<Integer>();

        for (WhFbaAllocation tAllocation : paging) {
            whApvIds.add(tAllocation.getId());

            WhPickingTaskItem whPickingTaskItem = new WhPickingTaskItem();
            whPickingTaskItem.setTaskId(whPickingTask.getId());
            whPickingTaskItem.setApvId(tAllocation.getId());
            whPickingTaskItem.setApvNo(tAllocation.getFbaNo());
            whPickingTaskItem.setStatus(PickingTaskItemStatus.UNCOMPLETED.intCode());
            whPickingTaskItem.setCreateBy(DataContextHolder.getUserId());
            whPickingTaskItem.setCreatedDate(new Timestamp(new Date().getTime()));
            items.add(whPickingTaskItem);

            // 组装sku表数据
            List<WhFbaAllocationItem> allocationItemList = tAllocation.getItems();
            for (WhFbaAllocationItem allocationItem : allocationItemList) {
                String sku = allocationItem.getProductSku();
                Integer quantity = allocationItem.getSkuQuantity();
                Integer integer = skuMap.get(sku);
                if (integer == null) {
                    integer = 0;
                }
                skuMap.put(sku, integer + quantity);
            }

            WhFbaAllocation updateAllocation = new WhFbaAllocation();
            updateAllocation.setId(tAllocation.getId());
            updateAllocation.setTaskNo(whPickingTask.getTaskNo());
            updateAllocation.setStatus(AsnPrepareStatus.WAITING_PICK.intCode());
            updateAllocation.setMergeTime(new Timestamp(new Date().getTime()));
            updateAllocationList.add(updateAllocation);
            tAllocation.setStatus(AsnPrepareStatus.WAITING_PICK.intCode());
        }
        if (CollectionUtils.isNotEmpty(updateAllocationList)) {
            updateAllocationList.forEach(allocation -> {
                // 修改订单状态
                whFbaAllocationService.updateWhFbaAllocation(allocation);
                String taskTypeName = PickingTaskType.getNameByCode(whPickingTask.getTaskType()+"");
                SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(),
                        "生成" + taskTypeName + "拣货任务，任务号：" + whPickingTask.getTaskNo());
            });
        }
        // 批量添加任务item详情
        whPickingTaskItemService.batchCreateWhPickingTaskItem(items);

        List<WhPickingTaskSku> skus = new ArrayList<>();
        for (String key : skuMap.keySet()) {
            WhPickingTaskSku whPickingTaskSku = new WhPickingTaskSku();
            whPickingTaskSku.setTaskId(whPickingTask.getId());
            whPickingTaskSku.setSku(key);
            whPickingTaskSku.setQuantity(skuMap.get(key));
            whPickingTaskSku.setPickQuantity(0);
            whPickingTaskSku.setCreationDate(new Timestamp(new Date().getTime()));
            whPickingTaskSku.setLastUpdateDate(new Timestamp(new Date().getTime()));
            whPickingTaskSku.setStatus(PickingTaskSkuStatus.UNCOMPLETED.intCode());
            skus.add(whPickingTaskSku);
        }
        // 批量添加任务SKU详情
        whPickingTaskSkuService.batchCreateWhPickingTaskSku(skus);

        // 创建批次
        WhApvBatch whApvBatch = new WhApvBatch();
        whApvBatch.setBatchType(ApvBatchType.SINGLE_WAREHOUSE.intCode());

        // 单仓库默认完成
        whApvBatch.setBatchStatus(ApvBatchStatus.COMPLETED.intCode());

        whApvBatch.setSerialNumber(whPickingTask.getTaskNo());
        whApvBatchDao.createWhApvBatch(whApvBatch);

        // 批次内的仓库
        List<WhApvBatchItem> whApvBatchItems = new ArrayList<WhApvBatchItem>();

        WhApvBatchItem whApvBatchItem = new WhApvBatchItem();
        whApvBatchItem.setBatchId(whApvBatch.getId());
        whApvBatchItem.setWarehouseId(paging.get(0).getItems().get(0).getWarehouseId());

        // 单仓库默认完成
        whApvBatchItem.setItemStatus(ApvBatchItemStatus.COMPLETED.intCode());

        whApvBatchItems.add(whApvBatchItem);

        whApvBatchItemDao.batchCreateWhApvBatchItem(whApvBatchItems);

        // 批次内的订单
        List<WhApvBatchDetail> whApvBatchDetails = new ArrayList<WhApvBatchDetail>();
        for (Integer apvId : whApvIds) {
            WhApvBatchDetail whApvBatchDetail = new WhApvBatchDetail();
            whApvBatchDetail.setBatchId(whApvBatch.getId());
            whApvBatchDetail.setApvId(apvId);

            whApvBatchDetails.add(whApvBatchDetail);
        }
        whApvBatchDetailDao.batchCreateWhApvBatchDetail(whApvBatchDetails);

        // 更新出库关联表状态
        whApvOutStockChainService.updateWhApvOutStockChainStatus(paging.stream().map(p -> p.getFbaNo()).collect(Collectors.toList()),
                WhApvOutStockChainStatusEnum.ALLOT, WhApvOutStockChainStatusEnum.TOUCHING );
    }

    /**
     * 根据发货单列表确定海外仓头程单的任务类型
     * 
     * @param paging 发货单列表
     * @return 任务类型：海外仓单品(23)或海外仓多品(24)
     */
    private Integer determineAsnFirstTaskType(List<WhFbaAllocation> paging) {
        // 获取所有发货单的APV类型
        Set<String> apvTypes = paging.stream()
                .map(WhFbaAllocation::getApvType)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        
        // 检查是否包含多品类型(MM)
        boolean hasMultipleType = apvTypes.stream().anyMatch(type -> ApvTypeEnum.MM.getCode().equals(type));
        
        if (hasMultipleType) {
            // 包含多品类型，设置为海外仓多品
            return PickingTaskType.ASN_FIRST_MULTIPLE.intCode();
        } else {
            // 只包含单品类型(SS/SM)，设置为海外仓单品
            return PickingTaskType.ASN_FIRST_SINGLE.intCode();
        }
    }

    /**
     * 拣货
     *
     * @param domain
     * @param whPickingTask
     * @return
     */
    @Override
    public ResponseJson multiplePick(AndroidProductDo domain, WhPickingTask whPickingTask, List<WhApvOutStockChain> whApvOutStockChainAllList) throws Exception {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        List<WhFbaAllocationItem> updateItems = new ArrayList<>();
        List<WhApvOutStockChain> updateWhApvOutStockChainList = new ArrayList<>();
        Map<Integer,Integer> stockIdMap = new HashMap<>();

        List<WhFbaAllocation> allocationList = whFbaAllocationService
                .queryAllocationByPickingTaskId(domain.getTaskId());
        long count = 0;
        int sumPickQty = 0;
        if (CollectionUtils.isNotEmpty(allocationList)) {
            // 按拣货差异数量排序
            whApvOutStockChainAllList.sort(Comparator.comparing(s -> (s.getQuantity() == null ? 0 : s.getQuantity())
                    - (s.getPickQuantity() == null ? 0 : s.getPickQuantity())));
            // 根据发货单号分组
            Map<String, List<WhApvOutStockChain>> map = whApvOutStockChainAllList.stream().filter(w -> domain.getStockIds().contains(w.getStockId())).
                    collect(Collectors.groupingBy(w -> w.getRelevantNo()));

            String sku = domain.getSku();
            // 拣货数量待扣除参数
            int tempQuantity = domain.getPickQuantity() == null ? 0 : domain.getPickQuantity();

            int need = domain.getNeedQuantity() == null ? 0 : domain.getNeedQuantity();

            if (tempQuantity != 0 && need != 0 && tempQuantity > need) {
                ResponseJson responseJson = new ResponseJson();
                responseJson.setStatus(StatusCode.FAIL);
                responseJson.setMessage("当前sku部分数量被取消！ 请退回数量并更正需要拣货的数量! 当前应拣数量为：" + need);
                return responseJson;
            }

            // 获取拣货数量为null的库位
            count = whApvOutStockChainAllList.stream().filter(w -> Integer.valueOf(WhApvOutStockChainStatusEnum.TOUCHING.intCode()).equals(w.getStatus()))
                    .map(w -> w.getStockId()).distinct().count();
            // 获取sku总的已捡数量
            sumPickQty = whApvOutStockChainAllList.stream().mapToInt(w -> w.getPickQuantity() == null ? 0 : w.getPickQuantity()).sum();
            int tempQty = domain.getPickQuantity() == null ? 0 : domain.getPickQuantity();
            for (WhFbaAllocation allocation : allocationList) {

                if (allocation.getStatus().equals(AsnPrepareStatus.CANCEL.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.PICK_STOCK_OUT.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.DELIVER.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.ALLOTING.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.WAITING_ALLOT.intCode())) {
                    continue;
                }

                List<WhFbaAllocationItem> allocationItems = allocation.getItems();
                for (WhFbaAllocationItem allocationItem : allocationItems) {
                    if (sku.equals(allocationItem.getProductSku())) {
                        if (tempQty == 0) {
                            break;
                        }
                        // 如果拣货数量不等-->>拣货异常-缺货情况
                        if (map.get(allocation.getFbaNo()).get(0) == null
                                || map.get(allocation.getFbaNo()).get(0).getQuantity() == null) {
                            response.setMessage("sku在对应库存id：" + domain.getLocation() + "上无分配记录！");
                            return response;
                        }
                        int fnQty = allocationItem.getAllotQuantity() == null ? 0 : allocationItem.getAllotQuantity();
                        int fnPickQty = allocationItem.getPickQuantity() == null ? 0 : allocationItem.getPickQuantity();
                        WhApvOutStockChain whApvOutStockChain = map.get(allocation.getFbaNo()).stream()
                                .filter(w -> w.getPickQuantity() == null || w.getPickQuantity() < w.getQuantity())
                                .findFirst().orElse(null);
                        if (whApvOutStockChain == null && fnPickQty >= fnQty)
                            continue;
                        int quantity = whApvOutStockChain == null || whApvOutStockChain.getQuantity() == null ? 0
                                : whApvOutStockChain.getQuantity();
                        int pickQuantity = whApvOutStockChain == null || whApvOutStockChain.getPickQuantity() == null
                                ? 0
                                : whApvOutStockChain.getPickQuantity();
                        int needQuantity = quantity - pickQuantity;
                        if (pickQuantity >= quantity && fnPickQty >= fnQty)
                            continue;
                        int fnNeedQty = fnQty - fnPickQty;
                        int curPickQty = 0;
                        if (tempQuantity >= 0) {
                            log.warn("多品多件扣除已匹配数量 " + tempQuantity + " - " + needQuantity);
                            if (tempQuantity >= fnNeedQty) {
                                allocationItem.setPickQuantity(allocationItem.getPickQuantity() + fnNeedQty);
                                tempQuantity -= fnNeedQty;
                            }
                            else {
                                allocationItem.setPickQuantity(allocationItem.getPickQuantity() + tempQuantity);
                                tempQuantity = 0;
                            }
                            if (whApvOutStockChain != null) {
                                if (tempQty >= needQuantity) {
                                    if (fnNeedQty > needQuantity) {
                                        curPickQty = needQuantity;
                                        tempQty -= needQuantity;
                                    }
                                    else {
                                        curPickQty = fnNeedQty;
                                        tempQty -= fnNeedQty;
                                    }
                                }
                                else {
                                    curPickQty = tempQty;
                                    tempQty = 0;
                                }
                                sumPickQty += curPickQty;
                                whApvOutStockChain.setPickQuantity((whApvOutStockChain.getPickQuantity()==null?0:whApvOutStockChain.getPickQuantity())+curPickQty);
                                updateWhApvOutStockChainList.add(WhApvOutStockChain.builder().id(whApvOutStockChain.getId()).pickQuantity(
                                        whApvOutStockChain.getPickQuantity()).status(WhApvOutStockChainStatusEnum.PICKED.intCode()).build());
                                Integer existPickQty;
                                if ((existPickQty = stockIdMap.putIfAbsent(whApvOutStockChain.getStockId(),curPickQty)) != null){
                                    stockIdMap.put(whApvOutStockChain.getStockId(),existPickQty+curPickQty);
                                }
                            }
                        }
                        else {
                            allocationItem.setPickQuantity(0);
                        }
                        WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
                        updateItem.setId(allocationItem.getId());
                        updateItem.setPickQuantity(allocationItem.getPickQuantity());
                        updateItem.setProductSku(allocationItem.getProductSku());
                        updateItem.setStore(allocationItem.getStore());
                        updateItem.setSite(allocationItem.getSite());

                        updateItems.add(updateItem);
                    }
                }
            }

            if (tempQuantity > 0) {
                ResponseJson responseJson = new ResponseJson();
                responseJson.setStatus(StatusCode.FAIL);
                responseJson.setMessage("请把该SKU退" + tempQuantity + "个回到原货位！");
                return responseJson;
            }

        }

        try {
            response = this.updateMultiplePicking(updateItems, domain, whPickingTask, updateWhApvOutStockChainList, count, sumPickQty, stockIdMap);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("拣货下一步失败：" + e.getMessage());
        }
        return response;
    }

    /**
     * 拣货
     *
     * @param domain
     * @param whPickingTask
     * @return
     */
    @Override
    public ResponseJson multipleBZYCPick(AndroidProductDo domain, WhPickingTask whPickingTask, List<WhApvOutStockChain> whApvOutStockChainAllList) throws Exception {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        List<WhFbaAllocationItem> updateItems = new ArrayList<>();
        List<WhApvOutStockChain> updateWhApvOutStockChainList = new ArrayList<>();
        Map<Integer,Integer> stockIdMap = new HashMap<>();

        List<WhFbaAllocation> allocationList = whFbaAllocationService
                .queryAllocationByPickingTaskId(domain.getTaskId());
        long count = 0;
        int sumPickQty = 0;
        List<WhPickingTaskSkuLocation> tempList = null;
        if (CollectionUtils.isNotEmpty(allocationList)) {
            String o = StringRedisUtils.get(RedisConstant.BZYC_KEY + whPickingTask.getTaskNo());
            if (org.apache.commons.lang3.StringUtils.isBlank(o)){
                response.setMessage("当前二配任务没有领取！");
                return response;
            }
            tempList = JSONObject.parseArray(o, WhPickingTaskSkuLocation.class);
            // 按拣货差异数量排序
            whApvOutStockChainAllList.sort(Comparator.comparing(s -> (s.getQuantity() == null ? 0 : s.getQuantity())
                    - (s.getPickQuantity() == null ? 0 : s.getPickQuantity())));
            // 根据发货单号分组
            Map<String, List<WhApvOutStockChain>> map = whApvOutStockChainAllList.stream().filter(w -> domain.getStockIds().contains(w.getStockId())).
                    collect(Collectors.groupingBy(w -> w.getRelevantNo()));

            String sku = domain.getSku();
            // 拣货数量待扣除参数
            int tempQuantity = domain.getPickQuantity() == null ? 0 : domain.getPickQuantity();

            int need = domain.getNeedQuantity() == null ? 0 : domain.getNeedQuantity();

            if (tempQuantity != 0 && need != 0 && tempQuantity > need) {
                ResponseJson responseJson = new ResponseJson();
                responseJson.setStatus(StatusCode.FAIL);
                responseJson.setMessage("当前sku部分数量被取消！ 请退回数量并更正需要拣货的数量! 当前应拣数量为：" + need);
                return responseJson;
            }

            // 获取拣货数量为null的库位
            count =  tempList.stream().filter(w -> StringUtils.equalsIgnoreCase(domain.getSku(),w.getSku()) &&
                    PickingTaskSkuStatus.UNCOMPLETED.intCode().equals(w.getStatus())).map(w -> w.getLocation()).distinct().count();
            // 获取sku总的已捡数量
            sumPickQty = tempList.stream().filter(w -> StringUtils.equalsIgnoreCase(domain.getSku(), w.getSku()))
                    .mapToInt(w -> w.getPickQuantity() == null ? 0 : w.getPickQuantity()).sum();
            // 获取当前拣货sku
            WhPickingTaskSkuLocation whPickingTaskSkuLocation = tempList.stream().filter(w -> StringUtils.equalsIgnoreCase(domain.getSku(), w.getSku())
                    && w.getStockIds().contains(domain.getStockIds().get(0))).findFirst().orElse(null);

            int tempQty = domain.getPickQuantity() == null ? 0 : domain.getPickQuantity();

            for (WhFbaAllocation allocation : allocationList) {

                if (allocation.getStatus().equals(AsnPrepareStatus.CANCEL.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.PICK_STOCK_OUT.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.DELIVER.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.ALLOTING.intCode())
                        || allocation.getStatus().equals(AsnPrepareStatus.WAITING_ALLOT.intCode())) {
                    continue;
                }

                List<WhFbaAllocationItem> allocationItems = allocation.getItems();
                for (WhFbaAllocationItem allocationItem : allocationItems) {
                    Integer oldQuantity = Optional.ofNullable(allocationItem.getQuantity()).orElse(0);
                    Integer oldPickQuantity = Optional.ofNullable(allocationItem.getPickQuantity()).orElse(0);
                    if (sku.equals(allocationItem.getProductSku())) {
                        if (tempQty == 0 || oldQuantity-oldPickQuantity <= 0) {
                            break;
                        }
                        if (map.get(allocation.getFbaNo()).get(0) == null
                                || map.get(allocation.getFbaNo()).get(0).getQuantity() == null) {
                            response.setMessage("sku在对应库存id：" + domain.getLocation() + "上无分配记录！");
                            return response;
                        }
                        int fnQty = allocationItem.getAllotQuantity() == null ? 0 : allocationItem.getAllotQuantity();
                        int fnPickQty = allocationItem.getPickQuantity() == null ? 0 : allocationItem.getPickQuantity();
                        // 如果拣货数量不等-->>拣货异常-缺货情况
                        WhApvOutStockChain whApvOutStockChain = map.get(allocation.getFbaNo()).stream()
                                .filter(w -> w.getPickQuantity() == null || w.getPickQuantity() < w.getQuantity())
                                .findFirst().orElse(null);
                        if (whApvOutStockChain == null && fnPickQty >= fnQty)
                            continue;
                        int quantity = whApvOutStockChain == null || whApvOutStockChain.getQuantity() == null ? 0
                                : whApvOutStockChain.getQuantity();
                        int pickQuantity = whApvOutStockChain == null || whApvOutStockChain.getPickQuantity() == null
                                ? 0
                                : whApvOutStockChain.getPickQuantity();
                        int needQuantity = quantity - pickQuantity;
                        if (pickQuantity >= quantity && fnPickQty >= fnQty)
                            continue;
                        int fnNeedQty = fnQty - fnPickQty;
                        int curPickQty = 0;
                        if (tempQuantity >= 0) {
                            log.warn("多品多件扣除已匹配数量 " + tempQuantity + " - " + needQuantity);
                            if (tempQuantity >= fnNeedQty) {
                                allocationItem.setPickQuantity(allocationItem.getPickQuantity() + fnNeedQty);
                                tempQuantity -= fnNeedQty;
                            }
                            else {
                                allocationItem.setPickQuantity(allocationItem.getPickQuantity() + tempQuantity);
                                tempQuantity = 0;
                            }
                            if (whApvOutStockChain != null) {
                                if (tempQty >= needQuantity) {
                                    if (fnNeedQty > needQuantity) {
                                        curPickQty = needQuantity;
                                        tempQty -= needQuantity;
                                    }
                                    else {
                                        curPickQty = fnNeedQty;
                                        tempQty -= fnNeedQty;
                                    }
                                }
                                else {
                                    curPickQty = tempQty;
                                    tempQty = 0;
                                }
                                sumPickQty += curPickQty;
                                whApvOutStockChain.setPickQuantity((whApvOutStockChain.getPickQuantity() == null ? 0
                                        : whApvOutStockChain.getPickQuantity()) + curPickQty);
                                updateWhApvOutStockChainList
                                        .add(WhApvOutStockChain.builder().id(whApvOutStockChain.getId())
                                                .pickQuantity(whApvOutStockChain.getPickQuantity())
                                                .status(WhApvOutStockChainStatusEnum.PICKED.intCode()).build());
                                Integer existPickQty;
                                if ((existPickQty = stockIdMap.putIfAbsent(whApvOutStockChain.getStockId(),
                                        curPickQty)) != null) {
                                    stockIdMap.put(whApvOutStockChain.getStockId(), existPickQty + curPickQty);
                                }
                            }
                        }
                        else {
                            allocationItem.setPickQuantity(0);
                        }
                        whPickingTaskSkuLocation.setPickQuantity(whPickingTaskSkuLocation.getPickQuantity()+curPickQty);
                        whPickingTaskSkuLocation.setStatus(PickingTaskSkuStatus.COMPLETED.intCode());
                        WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
                        updateItem.setId(allocationItem.getId());
                        updateItem.setPickQuantity(allocationItem.getPickQuantity());
                        updateItem.setProductSku(allocationItem.getProductSku());
                        updateItem.setStore(allocationItem.getStore());
                        updateItem.setSite(allocationItem.getSite());

                        updateItems.add(updateItem);
                    }
                }
            }

            if (tempQuantity > 0) {
                ResponseJson responseJson = new ResponseJson();
                responseJson.setStatus(StatusCode.FAIL);
                responseJson.setMessage("请把该SKU退" + tempQuantity + "个回到原货位！");
                return responseJson;
            }

        }
        try {
            response = this.updateMultiplePicking(updateItems, domain, whPickingTask, updateWhApvOutStockChainList, count, sumPickQty, stockIdMap);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("拣货下一步失败：" + e.getMessage());
        }
        if (CollectionUtils.isNotEmpty(tempList))
            StringRedisUtils.set(RedisConstant.BZYC_KEY+whPickingTask.getTaskNo(), JSONObject.toJSONString(tempList), 3*24*3600l);
        return response;
    }

    /**
     * 多品多件拣货修改库存
     *
     * @param updateItems
     * @param domain
     * @param whPickingTask
     * @return
     */
    @Override
    @StockServicelock
    public ResponseJson updateMultiplePicking(List<WhFbaAllocationItem> updateItems, AndroidProductDo domain,
            WhPickingTask whPickingTask, List<WhApvOutStockChain> updateWhApvOutStockChainList, long count, Integer sumPickQty, Map<Integer,Integer> stockIdMap) throws Exception {
        log.warn("updateMultiplePicking: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + domain.getSku() + ";location=" + domain.getLocation()
                + ";pickQuantity=" + domain.getPickQuantity());
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        if (domain.getPickQuantity() > 0 && CollectionUtils.isNotEmpty(updateItems)) {
            boolean updateLine = whFbaAllocationStockService.multiplePick(whPickingTask.getTaskNo(),
                    domain, whPickingTask.getTaskType(), stockIdMap);
            log.warn("拣货移库存: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + domain.getSku() +  ";location=" + domain.getLocation() + ";pickQuantity="
                    + domain.getPickQuantity() + ";updateLine=" + updateLine);
            if (!updateLine) {
                throw new RuntimeException("拣货下一步改库存失败!");
            }
        }

        // 捡最后一个库位
        if (count == 1) {
            // 修改拣货任务Sku的状态-->>已完成
            WhPickingTaskSku whPickingTaskSku = new WhPickingTaskSku();
            // 完成拣货
            whPickingTaskSku.setStatus(PickingTaskSkuStatus.COMPLETED.intCode());
            whPickingTaskSku.setId(domain.getTaskSkuId());
            // （实际拣货数量 ）=（客户输入的数量-实际APV用到的数量）
            // 因为在缺货的情况下需要客户自动把拣多的数量放回到原库位中
            whPickingTaskSku.setPickQuantity(sumPickQty);
            whPickingTaskSku.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            whPickingTaskSkuService.updateWhPickingTaskSku(whPickingTaskSku);
        }

        if (CollectionUtils.isNotEmpty(domain.getUuidList())) {
            for (String uuid : domain.getUuidList()) {
                whUniqueSkuService.pickingUniqueSKuLog(uuid, UniqueSkuStep.PICKING.intCode(),
                        whPickingTask.getTaskNo());
            }
        }
        //更新单据
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateItems);
        // 更新出库关联表拣货数量
        whApvOutStockChainService.batchUpdateWhApvOutStockChain(updateWhApvOutStockChainList);

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 推送
     *
     * @param allocation
     * @throws Exception
     */
    @Override
    public void doBoxCheck(WhFbaAllocation allocation) throws Exception {
        WhFbaAllocation updateAllocation = new WhFbaAllocation();
        updateAllocation.setId(allocation.getId());
        boolean jitAsn = allocation.getIsAsn() != null&& allocation.getIsAsn();
        if (allocation.isFba()) {
            // fba单需要销售确认后才能发货
            updateAllocation.setStatus(AsnPrepareStatus.WAITING_CONFIRM.intCode());
        }
        else if (!jitAsn) {
            // 非仓发的才改状态
            updateAllocation.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
        }
        updateAllocation.setBoxPushBy(DataContextHolder.getUserId());
        updateAllocation.setBoxPushTime(new Timestamp(System.currentTimeMillis()));
        whFbaAllocationService.updateWhFbaAllocation(updateAllocation);

        List<WhFbaAllocationItem> allocationItems = allocation.getItems();
        // 套装的装车数量小于拣货数量，退已捡库存到已捡返架
        List<WhFbaAllocationItem> updateStockList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allocation.buildGroupItems())) {
            allocation.buildGroupItems().forEach(i -> {
                // 播种完成后有部分取消的，用拣货数量比较
                if (allocation.isFba() && i.getSuitFlag() != null && i.getSuitFlag().equals(1)) {
                    Integer compareQuantity = i.getGridQuantity() > i.getPickQuantity() ? i.getPickQuantity()
                            : i.getGridQuantity();
                    if (i.getLoadingQuantity() < compareQuantity)
                        updateStockList.add(i);
                }
                else if (!allocation.isFba() && i.getSuitFlag() != null && i.getSuitFlag().equals(1)
                        && i.getLoadingQuantity() < i.getPickQuantity()) {
                    updateStockList.add(i);
                }
                else if (jitAsn && i.getLoadingQuantity() < i.getPickQuantity()) {
                    i.setGridQuantity(i.getPickQuantity());
                    updateStockList.add(i);
                }
            });
        }
        // TODO 退已捡库存到已捡返架
        if (CollectionUtils.isNotEmpty(updateStockList)) {
            allocation.setItems(updateStockList);
            whFbaAllocationStockService.box(allocation);
        }
        if (jitAsn){
            return;
        }
        // 审核后，推送给OMS
        if (allocation.isFba()) {
            allocation.setStatus(AsnPrepareStatus.WAITING_CONFIRM.intCode());
            if ("FBA".equals(allocation.getPurposeHouse())) {
                allocation.setPlanNo(allocation.getShipmentId());
            }
        } else {
            allocation.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
            /** 推送 店铺、站点、货件单号、FNSKU、系统SKU、数量、发货方式、装箱信息到 TMS */
            whFbaAllocationService.sendMsgToTms(allocation, TmsSendMsgType.SEND_BOX_INFO.intCode());
            SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "推送TMS成功");
        }
        allocation.setItems(allocationItems);
        whFbaAllocationService.sendMsg(allocation);
        SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "推送OMS成功");
    }

    /**
     * 打印箱唛
     *
     * @param allocation
     * @return
     * @throws Exception
     */
    @Override
    public String printXiangmaiFBA(WhFbaAllocation allocation) throws Exception {
        Integer packageMethod = Optional.ofNullable(allocation.getWhAsnExtra()).orElse(new WhAsnExtra())
                .getPackageMethod();
        boolean isBarn = Objects.equals(AsnPackageMethodEnum.BARN.getCode(), packageMethod);
        if (!allocation.isFba()){
            isBarn=true;
        }
        String url = this.getPrintXiangmaiURL(isBarn);
        // shein平台的订单地址是另外的
        if (Objects.equals(SaleChannel.CHANNEL_SHEIN,allocation.getPurposeHouse())){
            url = this.getPrintSheinXiangmaiURL();
        }
        if (StringUtils.isEmpty(url)) {
            log.error("销售系统调用url未定义");
            throw new Exception("销售系统调用url未定义");
        }
        String pdfBase64 = null;
        // url = "http://************/amazonFBA/getPackageLabels/";
        // url="http://*************/amazonFBA//getPackageLabelsNew/" ;
        ApiResult apiResult = HttpExtendUtils.get(url + allocation.getShipmentId(), HttpUtils.ACCESS_TOKEN,
                ApiResult.class, 300000, 300000);
        if (apiResult.isSuccess() && apiResult.getResult() != null) {
            pdfBase64 = apiResult.getResult().toString();
        }
        else {
            throw new Exception("订单系统未返回正确的面单，" + apiResult.getErrorMsg());
        }
        if (StringUtils.isBlank(pdfBase64)) {
            throw new Exception("接口返回base64内容为空");
        }
        return pdfBase64;
    }

    /**
     * 用于根据是否是谷仓单获取箱唛打印地址
     *
     * @param isBarn 用于判断是否是谷仓海外头程单
     * @return 用于获取打印箱唛的url地址
     */
    private String getPrintXiangmaiURL(boolean isBarn) {
        if (isBarn) {
            log.info("获取谷仓打印箱唛地址");
            return CacheUtils.SystemParamGet("OMS_PARAM.GET_XIANGMAI_FBA_ASN_URL").getParamValue();
        }
        return CacheUtils.SystemParamGet("OMS_PARAM.GET_XIANGMAI_FBA_URL").getParamValue();
    }

    /**
     * 获取箱唛shein平台的箱唛打印获取地址
     * @return 获取地址
     */
    private String getPrintSheinXiangmaiURL(){
        return CacheUtils.SystemParamGet("OMS_PARAM.GET_SHEIN_XIANGMAI_URL").getParamValue();
    }

    /**
     * 扫描单号装箱
     *
     * @param fbaNo
     * @return
     */
    @Override
    public ResponseJson scanFbaNoToBox(String fbaNo, String fnSku) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isEmpty(fbaNo)) {
            response.setMessage("发货单号不能为空！");
            return response;
        }
        WhFbaChangeQueryCondition queryCondition = new WhFbaChangeQueryCondition();
        queryCondition.setFbaNo(fbaNo);
        queryCondition.setDeliveryType(FBAChangeDeliveryTypeEnum.FBA_OUT_STOCK.getCode());
        queryCondition.setOrderItemStatus(FBAChangeOrderItemStatusEnum.WAITING_DEAL.getCode());
        List<WhFbaChange> whFbaChanges = whFbaChangeService.queryWhFbaChanges(queryCondition, null);
        if (CollectionUtils.isNotEmpty(whFbaChanges)) {
            response.setLocation(JSON.toJSONString(whFbaChanges));
            response.setMessage("存在部分取消SKU需要返架，请将实物拿给主管生成返架单后再继续操作！");
            response.setStatus(StatusCode.FAIL);
            return response;
        }


        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(fbaNo);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            response.setMessage("扫描单号不存在！");
            return response;
        }

        WhFbaAllocation allocation = allocationList.get(0);
        if (!AsnPrepareStatus.WAITING_BOX.intCode().equals(allocation.getStatus())
                && !AsnPrepareStatus.PICK_STOCK_OUT.intCode().equals(allocation.getStatus())) {
            response.setMessage("状态不是待装箱或拣货缺货！");
            return response;
        }

        List<WhFbaAllocationItem> itemList = allocation.getItems();
        if (CollectionUtils.isEmpty(itemList)) {
            response.setMessage("扫描单号明细不存在！");
            return response;
        }

        if (StringUtils.isNotEmpty(fnSku)) {
            itemList = itemList.stream().filter(i -> fnSku.equals(i.getFnSku())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(itemList)) {
            response.setMessage("扫描fnSku[" + fnSku + "]明细不存在！");
            return response;
        }

        Map<String, Object> body = assemblePdaBoxData(itemList,fbaNo);
        allocation.setItems(new ArrayList());
        body.put("allocation", allocation);
        response.setBody(body);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 组装PDA装箱数据
     *
     * @param itemList
     * @return
     */
    public Map<String, Object> assemblePdaBoxData(List<WhFbaAllocationItem> itemList,String fbaNo) {
        if (CollectionUtils.isEmpty(itemList))
            return new HashMap<>();
        Map<String, WhFbaAllocationItem> totalItemMap = new HashMap<>();
        Map<String, WhFbaAllocationItem> boxMap = new HashMap<>();
        List<String> skuList=new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemList)) {
            skuList=itemList.stream().map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());

            // 根据fnSku分组
            Map<String, List<WhFbaAllocationItem>> fnSkuMap = itemList.stream()
                    .collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku));
            for (Map.Entry<String, List<WhFbaAllocationItem>> entry : fnSkuMap.entrySet()) {
                if (CollectionUtils.isEmpty(entry.getValue()) || entry.getValue() == null)
                    continue;
                List<WhFbaAllocationItem> items = entry.getValue();
                for (WhFbaAllocationItem item : items) {
                    String boxNo = item.getBoxNo() == null ? "1" : String.valueOf(item.getBoxNo());
                    Integer loadNum = item.getLoadNum() == null ? 0 : item.getLoadNum();
                    Integer quantity = item.getQuantity() == null ? 0 : item.getQuantity();
                    Integer pickQuantity = item.getPickQuantity() == null ? 0 : item.getPickQuantity();
                    Integer gridQuantity = item.getGridQuantity() == null ? 0 : item.getGridQuantity();
                    // 套装
                    if (item.getSuitFlag() != null && item.getSuitFlag().equals(1)) {
                        pickQuantity = items.stream()
                                .map(i -> Math.round((float) (i.getPickQuantity() == null ? 0 : i.getPickQuantity())
                                        / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                                .sorted().collect(Collectors.toList()).get(0);
                        gridQuantity = items.stream()
                                .map(i -> Math.round((float) (i.getGridQuantity() == null ? 0 : i.getGridQuantity())
                                        / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                                .sorted().collect(Collectors.toList()).get(0);
                    }
                    if (gridQuantity>pickQuantity){
                        gridQuantity=pickQuantity;
                    }

                    WhFbaAllocationItem totalItem = totalItemMap.get(item.getFnSku());
                    List<Integer> boxNoList =new ArrayList<>();
                    if (totalItem == null) {
                        totalItem = new WhFbaAllocationItem();
                        totalItem.setBoxNo(Integer.valueOf(boxNo));
                        totalItem.setFnSku(item.getFnSku());
                        totalItem.setLoadNum(loadNum);
                        totalItem.setQuantity(quantity);
                        totalItem.setPickQuantity(pickQuantity);
                        totalItem.setGridQuantity(gridQuantity);
                        if (loadNum>0) {
                            boxNoList.add(Integer.valueOf(boxNo));
                        }
                        totalItem.setBoxNoList(boxNoList);
                    }
                    else if (!totalItem.getBoxNo().equals(Integer.valueOf(boxNo))) {
                        if (CollectionUtils.isNotEmpty(totalItem.getBoxNoList())) {
                            boxNoList = new ArrayList<>(totalItem.getBoxNoList());
                        }
                        if (loadNum>0) {
                            boxNoList.add(Integer.valueOf(boxNo));
                        }
                        totalItem.setBoxNo(Integer.valueOf(boxNo));
                        totalItem.setLoadNum(totalItem.getLoadNum() + loadNum);
                        totalItem.setBoxNoList(boxNoList);
                    }
                    totalItem.setProductSku(item.getProductSku());
                    totalItemMap.put(item.getFnSku(), totalItem);
                    Double weight = item.getProductWeight() == null ? 0 : item.getProductWeight();
                    WhFbaAllocationItem boxItem = boxMap.get(boxNo);
                    if (boxItem == null) {
                        boxItem = new WhFbaAllocationItem();
                        boxItem.setFnSku(item.getFnSku());
                        boxItem.setProductWeight(weight);
                        boxItem.setProductLength(item.getProductLength());
                        boxItem.setProductWidth(item.getProductWidth());
                        boxItem.setProductHeight(item.getProductHeight());
                    }
                    boxItem.setLoadNum(loadNum);
                    boxMap.put(boxNo, boxItem);
                }
            }
        }

        Map<String, Object> body = new HashMap<>();
        body.put("boxMap", boxMap);
        body.put("fnSkuMap", totalItemMap);


        List<WhFbaAllocationItem> fbaAllocationList = new ArrayList<>();
        totalItemMap.forEach((k,v)->{
            if (StringUtils.isBlank(k) || v==null) {
                return;
            }
            Integer gridQuantity = Optional.ofNullable(v.getGridQuantity()).orElse(0);
            Integer loadNum = Optional.ofNullable(v.getLoadNum()).orElse(0);
            v.setUnboxed(gridQuantity-loadNum);
            fbaAllocationList.add(v);
        });
        //按照未装箱数量排序
        fbaAllocationList.sort((o1, o2) -> o2.getUnboxed().compareTo(o1.getUnboxed()));
        body.put("fnSkuList",fbaAllocationList);

        List<WhFbaAllocationItem> whFbaAllocationItems=new ArrayList<>();
        List<WhFbaAllocationItem> finalWhFbaAllocationItems = whFbaAllocationItems;
        boxMap.forEach((k, v)->{
            if (StringUtils.isBlank(k) || v==null) {
                return;
            }
            v.setBoxNo(Integer.valueOf(k));
            finalWhFbaAllocationItems.add(v);
        });

        WhFbaAllocationItem skuInfo = new WhFbaAllocationItem();
        //SKU信息
        if (CollectionUtils.isNotEmpty(skuList)) {
            WhSkuQueryCondition queryCondition = new WhSkuQueryCondition();
            queryCondition.setSkus(skuList);
            List<WhSku> whSkus = whSkuService.queryWhSkus(queryCondition, null);
            if (CollectionUtils.isEmpty(whSkus)) {
                return body;
            }
            Map<String, Double> skuMap =  whSkus.stream().collect(Collectors.toMap(WhSku::getSku, WhSku::getWeight));
            for (WhFbaAllocationItem whFbaAllocationItem : fbaAllocationList) {
                int gridQuantity = Optional.ofNullable(whFbaAllocationItem.getGridQuantity()).orElse(0);
                int unboxed = Optional.ofNullable(whFbaAllocationItem.getUnboxed()).orElse(0);
                double weight = Optional.ofNullable(skuMap.get(whFbaAllocationItem.getProductSku())).orElse(0d)/1000;

                double unboxedWeight = Math.round(weight * unboxed * 1000.0) / 1000.0;
                double boxedWeight = Math.round(weight * gridQuantity * 1000.0) / 1000.0;
                skuInfo.setUnboxedTotalWeight(Optional.ofNullable(skuInfo.getUnboxedTotalWeight()).orElse(0d)+unboxedWeight);
                skuInfo.setSkuTotalWeight(Optional.ofNullable(skuInfo.getSkuTotalWeight()).orElse(0d)+boxedWeight);
            }
            skuInfo.setBoxedTotalWeight(Optional.ofNullable(skuInfo.getSkuTotalWeight()).orElse(0d)-Optional.ofNullable(skuInfo.getUnboxedTotalWeight()).orElse(0d));
        }
        body.put("skuInfo",skuInfo);
        String boxNoStr = StringRedisUtils.get(RedisConstant.PDA_FBA_PACKING + fbaNo);
        if (StringUtils.isNotBlank(boxNoStr)) {
            whFbaAllocationItems = JSONArray.parseArray(boxNoStr, WhFbaAllocationItem.class);
            body.put("boxList",whFbaAllocationItems);
            return body;
        }
        body.put("boxList",whFbaAllocationItems);
        StringRedisUtils.set(RedisConstant.PDA_FBA_PACKING+fbaNo,JSONArray.toJSONString(whFbaAllocationItems),60*60*24L);
        return body;
    }

    /**
     * FBA调拨单上传图片
     *
     * @param fbaId
     * @param fnSku
     * @param imageStr
     * @return
     * @throws Exception
     */
    @Override
    public ResponseJson uploadImage(Integer fbaId, String fnSku, String imageStr) throws Exception {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        if (StringUtils.isEmpty(fnSku) || StringUtils.isEmpty(imageStr) || fbaId == null) {
            response.setMessage("参数为空！");
            return response;
        }

        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(fbaId);
        List<WhFbaAllocation> fbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(fbaAllocations) || CollectionUtils.isEmpty(fbaAllocations.get(0).getItems())) {
            response.setMessage("没有找到FBA调拨发货单");
            return response;
        }
        List<WhFbaAllocationItem> fnSkuList = fbaAllocations.get(0).getItems().stream()
                .filter(i -> i.getFnSku().equals(fnSku)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fnSkuList)) {
            response.setMessage("发货单没有fnSku：" + fnSku);
            return response;
        }
        String fileName = fnSku + ".jpg";
        String url = SeaWeedFSUtils.URL;
        String dateStr = String.valueOf(new Date().getTime());
        String filePath = "/fbaAllocation/" + dateStr + "/";

        // base64解码并生成图片
        Base64.Decoder decoder = Base64.getMimeDecoder();
        byte[] b = decoder.decode(imageStr);

        String result = SeaWeedFSUtils.uploadFile(url, filePath, fileName, b);
        if (StringUtils.isNotBlank(result)) {
            String skuImg = url + filePath + fileName;
            List<WhFbaAllocationItem> updateList = new ArrayList<>();
            fnSkuList.forEach(i -> {
                WhFbaAllocationItem item = new WhFbaAllocationItem();
                item.setId(i.getId());
                item.setSkuImg(skuImg);
                updateList.add(item);
            });
            whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateList);
            response.setLocation(skuImg);
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseJson fbaTag(String fbaNo, String fnSku, String sku) {
        ResponseJson response = tagCheckFnSku(fbaNo, fnSku);
        if (StringUtils.equalsIgnoreCase(response.getStatus(), StatusCode.FAIL))
            return response;

        if (!AsnPrepareStatus.WAITING_LABEL.intCode().equals(response.getBody().get("status"))) {
            response.setStatus(StatusCode.FAIL);
            response.setBody(null);
            response.setMessage("没有找到FBA调拨发货单，或者该单据不是待贴标状态");
            return response;
        }
        List<WhFbaAllocationItem> existItems = (List<WhFbaAllocationItem>) response.getBody().get("fnSkuList");
        List<WhFbaAllocationItem> fnSkuList = BeanConvertUtils.convertList(existItems, WhFbaAllocationItem.class);

        fnSkuList = Optional.ofNullable(fnSkuList).orElse(new ArrayList<>()).stream()
                .filter(i -> i.getPickQuantity() != null && i.getPickQuantity() > 0 && i.getGridQuantity() != null
                        && i.getGridQuantity() > 0 && (i.getReProcess() == null || i.getReProcess()))
                .collect(Collectors.toList());

        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(fnSkuList)) {
            // 更新状态仓库审核(待复核）
            boolean updated = updateStatusToCheck(fbaNo, existItems,
                    Integer.valueOf(response.getBody().get("status").toString()));
            response.setBody(null);
            response.setMessage("发货单没有需要贴标的fnSku明细！");
            if (updated) {
                response.setMessage("该发货单已贴标完成");
            }
            return response;
        }

        if (StringUtils.isNotEmpty(fnSku))
            fnSkuList = fnSkuList.stream().filter(i -> i.getFnSku().equals(fnSku)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fnSkuList)) {
            response.setBody(null);
            response.setMessage("fnSku没有要贴标的明细！");
            return response;
        }

        if (StringUtils.isEmpty(sku)) {
            Map<String, List<String>> fnSkuMap = fnSkuList.stream().collect(Collectors.groupingBy(i -> i.getFnSku(),
                    Collectors.mapping(WhFbaAllocationItem::getProductSku, Collectors.toList())));
            Map<String, Object> map = new HashMap<>();
            map.put("fbaNo", fbaNo);
            map.put("fbaId", fnSkuList.get(0).getFbaId());
            map.put("fnSkuList", fnSkuMap);
            response.setBody(map);
            if (fnSkuList.stream().anyMatch(WhFbaAllocationItem::isSuit)){
                response.setLocation("套装SKU");
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        }
        boolean noneMatch = fnSkuList.stream().noneMatch(i -> StringUtils.equalsIgnoreCase(i.getProductSku(), sku));
        if (noneMatch) {
            response.setBody(null);
            response.setMessage(sku + "不是当前FNSKU[" + fnSku + "]下的明细");
            return response;
        }
        List<WhFbaAllocationItem> skuList = fnSkuList.stream().filter(i -> i.getProductSku().equals(sku))
                .collect(Collectors.toList());
        boolean isScan = skuList.stream().anyMatch(i -> i.getTagBy() != null);

        if (isScan) {
            response.setBody(null);
            response.setMessage(sku + "已扫描！");
            return response;
        }

        // 记录sku贴标人、贴标时间
        skuList.forEach(item -> {
            WhFbaAllocationItem tagItem = new WhFbaAllocationItem();
            tagItem.setId(item.getId());
            tagItem.setTagBy(DataContextHolder.getUserId());
            tagItem.setTagTime(new Timestamp(System.currentTimeMillis()));
            whFbaAllocationItemService.updateWhFbaAllocationItem(tagItem);
        });
        response.setBody(null);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseJson comfirmTag(String fbaNo, String fnSku, Integer processType) {
        ResponseJson response = tagCheckFnSku(fbaNo, null);
        if (StringUtils.equalsIgnoreCase(response.getStatus(), StatusCode.FAIL))
            return response;

        List<WhFbaAllocationItem> fnSkuList = (List<WhFbaAllocationItem>) response.getBody().get("fnSkuList");

        List<String> notTagSku = fnSkuList.stream()
                .filter(i -> i.getPickQuantity() != null && i.getPickQuantity() > 0 && i.getGridQuantity() != null
                        && i.getGridQuantity() > 0 && StringUtils.equalsIgnoreCase(i.getFnSku(), fnSku)
                        && i.getTagBy() == null)
                .map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notTagSku)) {
            response.setBody(null);
            response.setStatus(StatusCode.FAIL);
            response.setExceptionCode(PdaExceptionCode.SHOW_DIALOG);
            response.setMessage("还有SKU未扫描" + JSONObject.toJSONString(notTagSku) + "！");
            return response;
        }
        // 更新FNSKU加工类型
        WhFbaAllocationItem fnSkuItem = new WhFbaAllocationItem();
        fnSkuItem.setFbaId(fnSkuList.get(0).getFbaId());
        fnSkuItem.setFnSku(fnSku);
        fnSkuItem.setProcessType(processType);
        fnSkuItem.setReProcess(false);
        whFbaAllocationItemService.updateItemByFbaIdAndFnSku(fnSkuItem);
        // 更新状态仓库审核(待复核）
        boolean updated = updateStatusToCheck(fbaNo,fnSkuList, Integer.valueOf(response.getBody().get("status").toString()));
        if (updated) {
            response.setMessage(fbaNo + " 贴标完成！");
        }
        response.setBody(null);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    private ResponseJson tagCheckFnSku(String fbaNo, String fnSku) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isEmpty(fbaNo)) {
            response.setMessage("发货单号为空！");
            return response;
        }

        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(fbaNo);
        List<WhFbaAllocation> fbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(fbaAllocations) || CollectionUtils.isEmpty(fbaAllocations.get(0).getItems())) {
            response.setMessage("没有找到FBA调拨发货单");
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        Map<String, Object> map = new HashMap<>();
        map.put("fnSkuList", fbaAllocations.get(0).getItems());
        map.put("status", fbaAllocations.get(0).getStatus());
        response.setBody(map);
        return response;
    }

    private boolean updateStatusToCheck(String fbaNo, List<WhFbaAllocationItem> items, Integer beforeStatus) {
        if (beforeStatus == null || CollectionUtils.isEmpty(items)
                || !AsnPrepareStatus.WAITING_LABEL.intCode().equals(beforeStatus)) {
            return false;
        }
        boolean allTag = items.stream()
                .allMatch(i -> (i.getGridQuantity() == null || i.getGridQuantity() == 0 || i.getPickQuantity() == null
                        || i.getPickQuantity() == 0) || i.getTagBy() != null && StringUtils.isNotEmpty(i.getSkuImg()));
        if (!allTag)
            return false;
        // 更新状态仓库审核(待复核）
        // 单据从待贴标的判断规则为：单据类所有的FNSKU都已有图片、贴标人和加工类型
        WhFbaAllocation fbaAllocation = new WhFbaAllocation();
        fbaAllocation.setId(items.get(0).getFbaId());
        fbaAllocation.setStatus(AsnPrepareStatus.WAITING_CHECK.intCode());
        whFbaAllocationService.updateWhFbaAllocation(fbaAllocation);
        SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), "PDA贴标",
                new String[][] { { "历史状态", AsnPrepareStatus.WAITING_LABEL.getName() },
                        { "更改状态", AsnPrepareStatus.WAITING_CHECK.getName() } });
        fbaAllocation.setFbaNo(fbaNo);
        whFbaAllocationService.sendMsg(fbaAllocation);
        return true;
    }

    @Override
    public List<BarnSkuPrintVo> generateBarnPrintSkuList(List<WhFbaAllocationItem> items) throws Exception {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }

        Map<String, Integer> skuMap = items.stream()
                .filter(item -> Objects.nonNull(item.getProductSku()) && Objects.nonNull(item.getQuantity())
                        && !Objects.equals(item.getQuantity(), 0))
                .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku, WhFbaAllocationItem::getQuantity,
                        (v1, v2) -> v1 + v2));

        String url = CacheUtils.SystemParamGet("OMS_PARAM.GET_PRINT_SKU_URL").getParamValue();
        if (StringUtils.isEmpty(url)) {
            log.error("调用生成SKU标签url未定义");
            throw new Exception("调用生成SKU标签url未定义");
        }

        String accessToken = CacheUtils.SystemParamGet("OMS_PARAM.GET_PRINT_SKU_ACCESS_TOKEN").getParamValue();
        if (StringUtils.isEmpty(accessToken)) {
            log.error("访问生成SKU标签AccessToken未定义");
            throw new Exception("访问生成SKU标签AccessToken未定义");
        }

        String accessKey = CacheUtils.SystemParamGet("OMS_PARAM.GET_PRINT_SKU_ACCESS_KEY").getParamValue();
        if (StringUtils.isEmpty(accessKey)) {
            log.error("访问生成SKU标签AccessKey未定义");
            throw new Exception("访问生成SKU标签AccessKey未定义");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put("app-token", accessToken);
        headers.put("app-key", accessKey);
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");

        List<BarnSkuPrintVo> result = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : skuMap.entrySet()) {
            BarnSkuPrintRequestBody requestBody = new BarnSkuPrintRequestBody();
            requestBody.addSkuArr(entry.getKey());
            ApiPrintSkuResult apiResult = HttpExtendUtils.post(url, headers, JSON.toJSONString(requestBody),
                    ApiPrintSkuResult.class, 300000, 300000);

            if (!apiResult.isSuccess()) {
                throw new Exception("获取打印sku标签出错" + JSON.toJSONString(apiResult.getError()));
            }
            if (Objects.isNull(apiResult.getData())) {
                throw new Exception("接口返回base64内容为空");
            }

            try {
                ApiPrintSkuResult.DataResult dataResult = this.transferBase64StrToResult(entry.getKey(),
                        apiResult.getData());

                int count = entry.getValue();
                BarnSkuPrintVo vo = new BarnSkuPrintVo(dataResult.getLabel_image(), dataResult.getImage_type());
                if (Objects.isNull(vo.getLabelImage())) {
                    log.info(entry.getKey() + "数据转化存在错误!");
                    continue;
                }
                // 当为pdf时，打印份数的行为由用户自己进行控制
                if (Objects.equals(BarnSkuPrintVo.ImageTypeEnum.PDF.getCode(), vo.getImageType())) {
                    count = 1;
                }
                this.addElement(result, count, vo);
            }
            catch (Exception e) {
                log.error(e.getMessage());
            }
        }

        return result;
    }

    private ApiPrintSkuResult.DataResult transferBase64StrToResult(String sku, ApiPrintSkuResult.DataResult dataResult)
            throws Exception {
        ApiPrintSkuResult.DataResult result = new ApiPrintSkuResult.DataResult();
        if (Objects.isNull(dataResult)) {
            return result;
        }
        if (Objects.equals(BarnSkuPrintVo.ImageTypeEnum.PDF.getCode(), dataResult.getImage_type())) {
            String pdfUrl = PdfUtils.base64StrToFileAndUpload(dataResult.getLabel_image(), SeaWeedFSUtils.URL,
                    SKU_TAG_FILE_PATH, sku + ".pdf");
            if (StringUtils.isEmpty(pdfUrl)) {
                log.info("pdf的base64转化为文件并上传失败!");
                return result;
            }
            result.setLabel_image(pdfUrl);
            result.setImage_type(dataResult.getImage_type());
        }
        else if (Objects.equals(BarnSkuPrintVo.ImageTypeEnum.PNG.getName(), dataResult.getImage_type())) {
            byte[] imageByte = PictureUtils.base64StrToImageBytes(dataResult.getLabel_image());
            if (ArrayUtils.isEmpty(imageByte)) {
                log.info("图片base64转化为图片数组失败！");
                return result;
            }
            String imageUrl = PictureUtils.uploadImages(SeaWeedFSUtils.URL, SKU_TAG_FILE_PATH, sku + ".pdf", imageByte);
            result.setLabel_image(imageUrl);
            result.setImage_type(dataResult.getImage_type());
        }
        return result;
    }

    /**
     * 用于将接口返回的pdf和png图片的base64格式文件对象存储为png图片并上传到文件服务器中
     *
     * @param dataResult 文件对象
     * @param sku sku
     * @return 转化后的文件对象
     */
    private List<ApiPrintSkuResult.DataResult> transferBase64StringToPng(String sku,
            ApiPrintSkuResult.DataResult dataResult) throws Exception {
        List<ApiPrintSkuResult.DataResult> result = new ArrayList<>();
        if (Objects.isNull(dataResult)) {
            return result;
        }

        // 存储base64转化为图片后的图片文件字节数组
        List<byte[]> imageBytes = new ArrayList<>();

        if (Objects.equals(BarnSkuPrintVo.ImageTypeEnum.PDF.getCode(), dataResult.getImage_type())) {
            List<byte[]> imageByteList = PdfUtils.base64StrToPngBytes(dataResult.getLabel_image());
            if (CollectionUtils.isEmpty(imageByteList)) {
                log.info("pdf的base64转化为图片数组失败！");
                return result;
            }
            imageBytes.addAll(imageByteList);
        }
        else if (Objects.equals(BarnSkuPrintVo.ImageTypeEnum.PNG.getName(), dataResult.getImage_type())) {
            byte[] imageByte = PictureUtils.base64StrToImageBytes(dataResult.getLabel_image());
            if (ArrayUtils.isEmpty(imageByte)) {
                log.info("图片base64转化为图片数组失败！");
                return result;
            }
            imageBytes.add(imageByte);
        }

        Map<String, byte[]> pngFileUploadMap = new HashMap<>(imageBytes.size());
        for (int i = 0; i < imageBytes.size(); i++) {
            String pngFileName = sku + "-" + i + ".png";
            pngFileUploadMap.put(pngFileName, imageBytes.get(i));
        }
        List<String> pictureUrls = PictureUtils.uploadImages(pngFileUploadMap, SeaWeedFSUtils.URL, SKU_TAG_FILE_PATH);
        for (String url : pictureUrls) {
            ApiPrintSkuResult.DataResult data = new ApiPrintSkuResult.DataResult(url,
                    BarnSkuPrintVo.ImageTypeEnum.PNG.getCode());
            result.add(data);
        }

        return result;
    }

    @Override
    public String getJitPdfUrl(String fbaNo, Integer billType, Integer orderType,Integer boxQuantity) throws Exception {
        if (StringUtils.isBlank(fbaNo) || billType==null){
            throw new Exception("fbaNo单号或单据类型为空！");
        }

        String url = CacheUtils.SystemParamGet("OMS_PARAM.GET_JIT_PDF_URL").getParamValue();
       // url="http://*************:80/aliexpress/jitShipmentPrepare/getPdfUrl";
        if (StringUtils.isEmpty(url)) {
            log.error("调用OMS系统,获取Jit货品标签、箱唛和揽收面单pdf的url未定义");
            throw new Exception("调用OMS系统,获取Jit货品标签、箱唛和揽收面单pdf的url未定义");
        }
        WhJitShipmentPrepareDTO whJitShipmentPrepareDTO=new WhJitShipmentPrepareDTO();
        ApiResult apiResult=HttpExtendUtils.post(url , HttpUtils.ACCESS_TOKEN,new WhJitShipmentPrepareDTO(fbaNo, billType, orderType, boxQuantity),
                ApiResult.class, 300000, 300000);
        log.info(String.format("请求参数fbaNo：%s,billType：%s,orderType：%s,boxQuantity:%s,返回参数%s",fbaNo,billType,orderType,boxQuantity,JSON.toJSONString(apiResult)));
        if (apiResult.isSuccess() && apiResult.getResult() != null) {
            whJitShipmentPrepareDTO = JSON.parseObject(JSON.toJSONString(apiResult.getResult()), WhJitShipmentPrepareDTO.class);
        }
        else {
            throw new Exception("获取Jit货品标签 或 箱唛 或 揽收面单pdf失败：" + apiResult.getErrorMsg());
        }
        String jitUrl=null;
        String errorMsg=null;
        if (billType==1){
            jitUrl=whJitShipmentPrepareDTO.getProductTagUrl();
            errorMsg=whJitShipmentPrepareDTO.getProductTagUrlFailReason();
        }
        if (billType==2){
            jitUrl=whJitShipmentPrepareDTO.getShippingMarkUrl();
            errorMsg=whJitShipmentPrepareDTO.getShippingMarkUrlFailReason();
        }
        if (billType==3){
            jitUrl=whJitShipmentPrepareDTO.getPickupOrderUrl();
            errorMsg=whJitShipmentPrepareDTO.getPickupOrderUrlFailReason();
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new Exception(errorMsg);
        }
        return jitUrl;
    }

    /**
     * 用于向结果集中添加对应次数的对象
     *
     * @param result 结果集
     * @param count 对象添加次数
     * @param val 要进行添加的对象
     */
    private void addElement(List<BarnSkuPrintVo> result, Integer count, BarnSkuPrintVo val) {
        if (Objects.isNull(result) || Objects.isNull(count) || Objects.isNull(val) || count <= 0) {
            return;
        }
        for (int i = 0; i < count; i++) {
            result.add(val);
        }
    }

    /**
     * 海外仓出库单装箱完成解绑周转筐
     *
     * @param allocation
     */
    @Override
    public void boxFinishUnbindBoxNo(WhFbaAllocation allocation) {
        log.info("================start boxFinishUnbindBoxNo ===============");
        ExecutorUtils.execute(executors, () -> {
            if (allocation == null || StringUtils.isEmpty(allocation.getFbaNo()))
                return;
            WhPickingTaskQueryCondition query = new WhPickingTaskQueryCondition();
            query.setApvNo(allocation.getFbaNo());
            query.setTaskStatus(PickingTaskStatus.COMPLETED.intCode());
            List<WhPickingTask> whPickingTasks = whPickingTaskService.queryWhPickingTasks(query, null);
            if (CollectionUtils.isEmpty(whPickingTasks))
                return;
            whPickingTasks.removeIf(task -> !PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(task.getIsAsn())
                    && !PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(task.getIsAsn())
                    && !PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(task.getTaskType())
                    && !PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode().equals(task.getTaskType())
                    && !PickingTaskType.getJitAsnIntCode().contains(task.getTaskType()));
            if (CollectionUtils.isEmpty(whPickingTasks))
                return;
            int unFinish = 0;
            String msg;
            if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(whPickingTasks.get(0).getIsAsn())
                    || PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(whPickingTasks.get(0).getIsAsn())) {
                msg = "海外仓出库单装箱完成解绑周转筐";
                // 根据任务号捞取未装箱完成的
                unFinish = whFbaAllocationService.unBoxCountInTask(allocation.getFbaNo());
            }
            else {
                msg = "出库单包装完成解绑周转筐";
                unFinish = whFbaAllocationService.unPackCountInTask(allocation.getFbaNo());

            }
            if (unFinish > 0)
                return;
            whPickingTasks.forEach(task->{
                String boxNo = task.getBoxNo();
                if (StringUtils.isEmpty(boxNo))
                    return;
                // 全部包完，解绑周转筐
                WhBoxQueryCondition boxQuery = new WhBoxQueryCondition();
                boxQuery.setBoxNo(boxNo);
                boxQuery.setRelationNo(String.valueOf(task.getId()));
                WhBox whBox = whBoxService.queryWhBox(boxQuery);
                if (whBox != null)
                    whBoxService.updateWhBoxOfUnbinding(boxNo,
                            new String[][] { { msg, String.valueOf(task.getId()) } });
            });
            
        }, "ASN_FIRST_BOX_FINISH_UNBIND_BOX_NO");
    }

    @Override
    public ResponseJson checkWaitSplit(Integer apvId) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (apvId ==null){
            responseJson.setMessage("参数错误");
            return responseJson;
        }
        WhFbaAllocation allocation = whFbaAllocationService.getWhFbaAllocation(apvId);
        if (allocation == null) {
            responseJson.setMessage("没有找到发货单");
            return responseJson;
        }
        if (allocation.getIsAsn() == null || !allocation.getIsAsn()) {
            responseJson.setMessage(allocation.getFbaNo() + "不是仓发发货单");
            return responseJson;
        }
        WhFbaAllocation updateApv = new WhFbaAllocation();
        updateApv.setId(allocation.getId());
        updateApv.setTransitType(1);
        updateApv.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
        whFbaAllocationService.updateWhFbaAllocation(updateApv);
        SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), "包装标记待拆包",
                new String[][] {
                        { "历史状态", AsnPrepareStatus.getNameByCode(allocation.getStatus()+"") },
                        { "更改状态", AsnPrepareStatus.WAITING_BOX.getName()} });
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    @Override
    public void jitBox(Integer boxNum, WhFbaAllocation fbaAllocation) throws ApiException {
        StringRedisUtils.set(RedisConstant.JIT_APV_PACK_KEY+fbaAllocation.getFbaNo(),String.valueOf(boxNum), 15*60*60*24L);
        //创建揽收单
        syncCreatePickupOrderV3(fbaAllocation, 0);

        JSONObject jsonObject = aliExpressCallService.getPickupOrderNo(Collections.singletonList(fbaAllocation));
        // 揽收单号
        String pickupOrderNo = jsonObject.getString("pickup_order_number");
        // 服务商运单号
        String fulfillPickupOrderCode = jsonObject.getString("fulfill_pickup_order_code");


        if (StringUtils.isBlank(pickupOrderNo)) {
            return;
        }

        WhAsnExtra whAsnExtra = new WhAsnExtra();
        whAsnExtra.setId(fbaAllocation.getWhAsnExtra().getId());
        whAsnExtra.setPickupOrderId(pickupOrderNo);
        whAsnExtraService.updateWhAsnExtra(whAsnExtra);

        WhFbaAllocation updateApv = new WhFbaAllocation();
        updateApv.setId(fbaAllocation.getId());
        updateApv.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
        updateApv.setShippingOrderNo(fulfillPickupOrderCode);
        whFbaAllocationService.updateWhFbaAllocation(updateApv);
        SystemLogUtils.FBAALLOCATIONLOG.log(updateApv.getId(), "填写装箱数量:"+boxNum,
                new String[][] {
                        { "历史状态", AsnPrepareStatus.getNameByCode(fbaAllocation.getStatus()+"") },
                        { "更改状态", AsnPrepareStatus.WAITING_DELIVER.getName()} });

    }

    public String syncCreatePickupOrderV3(WhFbaAllocation fbaAllocation,Integer retry) {
        Map<String, String> availableDateMap = null;
        try {
            availableDateMap = aliExpressCallService.queryPickupAvailableDate(Collections.singletonList(fbaAllocation));
        } catch (Exception e) {
            if (retry == 3) {
                log.error(e.getMessage(), e);
                SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), "仓发组包查询揽收时间失败");
                return null;
            } else {
                return syncCreatePickupOrderV3(fbaAllocation, ++retry);
            }
        }
        try {
            String excludeOrderNos = aliExpressCallService.createPickupOrder(availableDateMap, Collections.singletonList(fbaAllocation));
            SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), String.format("创建揽收成功,揽收时间日期：【%s】,时间【%s-%s】，排除单据【%s】",availableDateMap.get("estimated_pickup_date"),
                    availableDateMap.get("startTime"),availableDateMap.get("endTime"),excludeOrderNos));
            StringRedisUtils.set(RedisConstant.SMT_CREATE_PICKUP_ORDER + fbaAllocation.getFbaNo(),
                    fbaAllocation.getFbaNo(), 7 * 24 * 60 * 60L);
        } catch (Exception e) {
            if (retry == 3) {
                log.error(e.getMessage(), e);
                String errorMsg="创建揽收失败"+e.getMessage();
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("店铺上门揽欠费停服")) {
                    errorMsg+="， 欠费店铺："+fbaAllocation.getAccountNumber();
                }
                SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), errorMsg);
                return null;
            } else {
                return syncCreatePickupOrderV3(fbaAllocation, ++retry);
            }
        }
        try {
            TimeUnit.SECONDS.sleep(10);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        return null;
    }



    @Override
    public WhFbaAllocationItem scanFnSkuInFon(String fbaNo, String fnSku,Integer boxNo) throws Exception {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(fbaNo);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            throw new Exception("扫描单号不存在！");
        }

        WhFbaAllocation allocation = allocationList.get(0);
        if (!AsnPrepareStatus.WAITING_BOX.intCode().equals(allocation.getStatus())
                && !AsnPrepareStatus.PICK_STOCK_OUT.intCode().equals(allocation.getStatus())) {
            throw new Exception("状态不是待装箱或拣货缺货！");
        }

        List<WhFbaAllocationItem> itemList = allocation.getItems();
        if (CollectionUtils.isEmpty(itemList)) {
            throw new Exception("扫描单号明细不存在！");
        }

        if (StringUtils.isNotEmpty(fnSku)) {
            itemList = itemList.stream().filter(i -> fnSku.equals(i.getFnSku())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(itemList)) {
            throw new Exception("扫描fnSku[" + fnSku + "]明细不存在！");
        }
        Map<String, Object> map = assemblePdaBoxData(itemList, fbaNo);
        if (map==null || map.get("fnSkuList")==null) {
            throw new Exception("扫描fnSku[" + fnSku + "]明细不存在!！");
        }
        List<WhFbaAllocationItem> allocationItems = (List<WhFbaAllocationItem>) map.get("fnSkuList");
        if (CollectionUtils.isEmpty(allocationItems)) {
            throw new Exception("扫描fnSku[" + fnSku + "]明细不存在！");
        }
        WhFbaAllocationItem whFbaAllocationItem = allocationItems.get(0);
        List<String> skuList = itemList.stream().map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuList)) {
            throw new Exception("扫描fnSku[" + fnSku + "]系统SKU不存在！");
        }
        WhSkuQueryCondition queryCondition = new WhSkuQueryCondition();
        queryCondition.setSkus(skuList);
        List<WhSku> whSkus = whSkuService.queryWhSkus(queryCondition, null);
        if (CollectionUtils.isEmpty(whSkus)) {
            throw new Exception("扫描fnSku[" + fnSku + "]不存在系统SKU！");
        }
        double weight = whSkus.stream().mapToDouble(w -> Optional.ofNullable(w.getWeight()).orElse(0d)).sum()/1000;

        int unboxed = Optional.ofNullable(whFbaAllocationItem.getUnboxed()).orElse(0);
        int gridQuantity = Optional.ofNullable(whFbaAllocationItem.getGridQuantity()).orElse(0);
        whFbaAllocationItem.setUnboxedTotalWeight(Math.round(weight * unboxed * 1000.0) / 1000.0);
        whFbaAllocationItem.setSkuTotalWeight(Math.round(weight * gridQuantity * 1000.0) / 1000.0);
        whFbaAllocationItem.setBoxedTotalWeight(whFbaAllocationItem.getSkuTotalWeight()- whFbaAllocationItem.getUnboxedTotalWeight());

        if (boxNo==null){
           return whFbaAllocationItem;
        }
        Map<String, WhFbaAllocationItem> boxMap = (Map<String, WhFbaAllocationItem>) map.get("boxMap");
        WhFbaAllocationItem fbaAllocationItem = boxMap.get(boxNo + "");
        if (fbaAllocationItem == null){
            whFbaAllocationItem.setLoadNum(0);
            whFbaAllocationItem.setBoxedTotalWeight(0d);
        }else{
            int loadNum = Optional.ofNullable(fbaAllocationItem.getLoadNum()).orElse(0);
            whFbaAllocationItem.setLoadNum(loadNum);
            whFbaAllocationItem.setBoxedTotalWeight(Math.round(weight * loadNum * 1000.0) / 1000.0);
        }
        whFbaAllocationItem.setBoxNo(boxNo);
        return whFbaAllocationItem;
    }

    /**
     * 获取自寄可预约日期
     */
    @Override
    public String genSelfDeliveryDates(List<WhFbaAllocation> fbaAllocations, String bagNo, int retry) {
        try {
            if (StringUtils.isBlank(bagNo) || CollectionUtils.isEmpty(fbaAllocations)) {
                return null;
            }
            JSONArray dateList = aliExpressCallService.queryAppointDate(fbaAllocations);
            String dateStr = dateList.stream().map(Objects::toString).collect(Collectors.joining(","));
            WhWarehouseShipmentQueryCondition queryCondition = new WhWarehouseShipmentQueryCondition();
            queryCondition.setBagNo(bagNo);
            List<WhWarehouseShipment> whWarehouseShipments = whWarehouseShipmentService.queryWhWarehouseShipments(queryCondition, null);
            if (CollectionUtils.isEmpty(whWarehouseShipments)) {
                WhWarehouseShipment whWarehouseShipment = new WhWarehouseShipment();
                whWarehouseShipment.setBagNo(bagNo);
                whWarehouseShipment.setAvailableDeliveryDate(dateStr);
                whWarehouseShipmentService.saveWhWarehouseShipment(whWarehouseShipment);
            } else {
                WhWarehouseShipment whWarehouseShipment = whWarehouseShipments.get(0);
                whWarehouseShipment.setAvailableDeliveryDate(dateStr);
                whWarehouseShipmentService.updateWhWarehouseShipment(whWarehouseShipment);
            }
            return dateStr;
        } catch (Exception e) {
            if (retry == 3) {
                log.error("获取自寄可预约日期失败:{}", e.getMessage(), e);
                return null;
            } else {
                return genSelfDeliveryDates(fbaAllocations, bagNo, ++retry);
            }
        }
    }

    /**
     * 生成自寄单
     * 
     * @param fbaAllocations FBA分配单列表
     * @param whWarehouseShipment 自寄信息
     * @param retry 重试次数
     * @return 自寄单号
     */
    @Override
    public String genSelfDeliveryOrder(List<WhFbaAllocation> fbaAllocations, WhWarehouseShipment whWarehouseShipment, int retry) {
        if (whWarehouseShipment == null || CollectionUtils.isEmpty(fbaAllocations)) {
            return null;
        }
        try {
            String bagNo = whWarehouseShipment.getBagNo();
            String key = RedisConstant.SMT_CREATE_SELF_DELIVERY + whWarehouseShipment.getBagNo();
            if (!StringRedisUtils.exists(key)) {
                // 1. 创建自寄单
                log.info("genSelfDeliveryOrder: 开始创建自寄单, bagNo={}", bagNo);
                aliExpressCallService.createSelfDelivery(fbaAllocations, whWarehouseShipment);
                SCANSHIPMENTLOG.log(whWarehouseShipment.getScanShipmentId(), "创建自寄单成功");
                StringRedisUtils.set(key, whWarehouseShipment.getBagNo(), 7 * 24 * 60 * 60L);
                // 由于创建自寄单是异步的，需要等待一段时间后查询
                try {
                    TimeUnit.SECONDS.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            // 2. 查询自寄单信息获取自寄单号
            JSONObject queryResult = aliExpressCallService.querySelfDelivery(fbaAllocations);
            if (queryResult != null && queryResult.containsKey("appointment_license_plate")) {
                String licensePlateNo = queryResult.getString("appointment_license_plate");

                log.info("genSelfDeliveryOrder: 查询自寄单成功, bagNo={}, 车牌号={}", bagNo, licensePlateNo);
                SCANSHIPMENTLOG.log(whWarehouseShipment.getScanShipmentId(), "查询自寄单号成功:" + licensePlateNo);
                return licensePlateNo;
            } else {
                log.warn("genSelfDeliveryOrder: 查询自寄单信息失败或未返回自寄单号, bagNo={}", bagNo);
                return null;
            }
        } catch (Exception e) {
            if (retry >= 3) {
                log.error("genSelfDeliveryOrder: 生成自寄单失败, bagNo={}, error={}", whWarehouseShipment.getBagNo(), e.getMessage(), e);
                return null;
            } else {
                log.warn("genSelfDeliveryOrder: 第{}次尝试失败, 正在重试, bagNo={}, error={}", retry + 1, whWarehouseShipment, e.getMessage());
                return genSelfDeliveryOrder(fbaAllocations, whWarehouseShipment, ++retry);
            }
        }
    }

}
