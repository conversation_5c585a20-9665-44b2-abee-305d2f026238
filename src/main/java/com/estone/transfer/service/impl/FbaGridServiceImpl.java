package com.estone.transfer.service.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.estone.android.PdaExceptionCode;
import com.estone.apv.bean.ApvTrack;
import com.estone.apv.bean.WhApvGrid;
import com.estone.apv.bean.WhApvGridItem;
import com.estone.apv.common.ApvGridStatus;
import com.estone.apv.enums.ApvTypeEnum;
import com.estone.apv.dao.WhApvBatchDetailDao;
import com.estone.apv.dao.WhApvGridDao;
import com.estone.apv.dao.WhApvGridItemDao;
import com.estone.apv.service.ApvTrackService;
import com.estone.apv.service.WhApvGridService;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.common.util.BeanConvertUtils;
import com.estone.common.util.RedisKeys;
import com.estone.common.util.StringRedisUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskSku;
import com.estone.picking.bean.WhPickingTaskSkuQueryCondition;
import com.estone.picking.enums.PickTaskGridStatus;
import com.estone.picking.enums.PickingTaskType;
import com.estone.picking.service.PickTaskExpandService;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.picking.service.WhPickingTaskSkuService;
import com.estone.sku.bean.WhUniqueApvGrid;
import com.estone.sku.bean.WhUniqueApvGridQueryCondition;
import com.estone.sku.bean.WhUniqueSku;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.WhUniqueApvGridService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.sowstockout.bean.SowStockout;
import com.estone.sowstockout.service.SowStockoutService;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.domain.FbaGridDo;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.service.AsnPrepareGridService;
import com.estone.transfer.service.FbaGridService;
import com.estone.transfer.service.WhFbaAllocationItemService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.transfer.utils.FbaGridUtils;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.enums.LocationWarehouseType;
import com.estone.warehouse.service.ApvGridUpdateStockService;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: Yimeil
 * @Date: 2022/8/26 14:22
 * @Version: 1.0.0
 */
@Slf4j
@Service("fbaGridService")
public class FbaGridServiceImpl implements FbaGridService {
    @Resource
    private WhFbaAllocationItemService whFbaAllocationItemService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhUniqueApvGridService whUniqueApvGridService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private ApvGridUpdateStockService apvGridUpdateStockService;

    @Resource
    private WhPickingTaskService whPickingTaskService;

    @Resource
    private PickTaskExpandService pickTaskExpandService;

    @Resource
    private AsnPrepareGridService asnPrepareGridService;

    @Resource
    private SowStockoutService sowStockoutService;

    @Resource
    private WhApvBatchDetailDao whApvBatchDetailDao;

    @Resource
    private ApvTrackService apvTrackService;

    @Resource
    private WhApvGridDao whApvGridDao;

    @Resource
    private WhApvGridItemDao whApvGridItemDao;

    @Resource
    private WhPickingTaskSkuService whPickingTaskSkuService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChinaService;

    @Resource
    private WhApvGridService whApvGridService;

    @Override
    public String checkAsnGridScanUniqueSku(String uuidSku, String taskNo, Integer step) {
        if (StringUtils.isNotBlank(uuidSku) && !uuidSku.contains("="))
            return null;

        String errorMes = null;
        if (StringUtils.isBlank(uuidSku) || !uuidSku.contains("="))
            errorMes = "请扫描正确的唯一码！";
        WhUniqueSku unique = whUniqueSkuService.getWhUniqueSkuByUuidAndSku(uuidSku);
        if (unique == null)
            errorMes = "唯一码不存在！";
        if (!UniqueSkuStep.GRID.intCode().equals(step))

            return errorMes;
        // 播种环节仍然保留原来的重复校验方式
        WhUniqueApvGridQueryCondition query = new WhUniqueApvGridQueryCondition();
        query.setTaskNo(taskNo);
        query.setUuid(uuidSku);
        WhUniqueApvGrid uniqueApvGrid = whUniqueApvGridService.queryWhUniqueApvGrid(query);
        if (uniqueApvGrid != null) {
            String apvNo = uniqueApvGrid.getApvNo();
            WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
            queryCondition.setFbaNo(apvNo);
            String lastFnSku = StringRedisUtils.get(RedisKeys.getFbaGridSku(taskNo));
            if (StringUtils.isNotEmpty(lastFnSku))
                // 唯一码在拣货任务中被重复扫描时的提示内容，包括唯一码对应的发货单及FNSKU(重复扫描，唯一码对应的发货单***，fnsku:xxxxx)
                errorMes = "重复扫描，唯一码对应的发货单：" + apvNo + "，fnSku：" + StringUtils.split(lastFnSku, "_")[1];
        }
        return errorMes;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ResponseJson scanSkuToGrid(FbaGridDo domain) throws Exception {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        WhPickingTask whPickingTask = domain.getWhPickingTask();
        String sku = domain.getSku();
        String box = domain.getBoxNo();
        String uuid = domain.getUuid();
        String taskNo = whPickingTask.getTaskNo();
        Integer taskType = whPickingTask.getTaskType();
        Integer location = LocationWarehouseType.OLD.intCode();
        List<WhFbaAllocation> fbaAllocations = whFbaAllocationService
                .queryAllocationByPickingTaskId(whPickingTask.getId());
        if (CollectionUtils.isEmpty(fbaAllocations)) {
            rsp.setMessage("扫描单号[" + box + "]没有关联的发货单信息！");
            return rsp;
        }

        boolean canceled = fbaAllocations.stream().anyMatch(f -> AsnPrepareStatus.CANCEL.intCode().equals(f.getStatus()));

        if (canceled) {
            List<String> fbaNoList = fbaAllocations.stream()
                    .filter(f -> AsnPrepareStatus.CANCEL.intCode().equals(f.getStatus())).map(WhFbaAllocation::getFbaNo)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(fbaNoList)) {
                whApvGridService.cancelApvNoUuid(fbaNoList.get(0), uuid);
            }

            rsp.setMessage("扫描单号[" + box + "]关联的发货单已取消，请点击拣货任务播种完成，并将货物放到已拣返架区域！");
            return rsp;
        }

        String finalSku = sku;
        List<WhFbaAllocationItem> fbaAllocationItems = new ArrayList<>();
        List<WhFbaAllocationItem> allItems = new ArrayList<>();
        List<WhFbaAllocationItem> finalFbaAllocationItems = fbaAllocationItems;
        fbaAllocations.forEach(f -> {
            allItems.addAll(f.getItems());
            f.getItems().forEach(i -> {
                if (i.getProductSku().equalsIgnoreCase(finalSku)
                        && (!PickTaskGridStatus.COMPLETED.intCode().equals(i.getGridStatus())
                                && i.getPickQuantity() != null && i.getPickQuantity() > 0
                                || !Objects.equals(i.getPickQuantity(), i.getGridQuantity())))
                    finalFbaAllocationItems.add(i);
            });
        });

        if (!taskType.equals(PickingTaskType.BZCY.intCode()) && !taskType.equals(PickingTaskType.BZYC.intCode())) {
            fbaAllocationItems = Optional.of(fbaAllocationItems).orElse(new ArrayList<>()).stream()
                    .filter(i -> !PickTaskGridStatus.getGridExceptionCode().contains(i.getGridStatus()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(fbaAllocationItems)) {
            rsp.setMessage("扫描唯一码[" + uuid + "]没有关联的发货单SKU信息！");
            return rsp;
        }

        // 查询格子
        List<WhApvGrid> whApvGrids = asnPrepareGridService.queryApvGrid(taskNo, location, taskType);
        if (CollectionUtils.isEmpty(whApvGrids)) {
            if (taskType.equals(PickingTaskType.BZCY.intCode()) || taskType.equals(PickingTaskType.BZYC.intCode())) {
                //二次配货
                List<WhPickingTaskSku> whPickingTaskSkus = whPickingTaskService.querySecondaryDistributionSku(taskNo,
                        Arrays.asList(PickingTaskType.BZYC.intCode(), PickingTaskType.BZCY.intCode()));
                if (CollectionUtils.isEmpty(whPickingTaskSkus)) {
                    rsp.setMessage("二配拣货任务[" + taskNo + "]没有关联的SKU信息！");
                    return rsp;
                }
                List<String> skuList = whPickingTaskSkus.stream().map(WhPickingTaskSku::getSku).collect(Collectors.toList());
                List<WhFbaAllocationItem> initItems = allItems.stream().filter(i -> skuList.contains(i.getProductSku())
                        && (!PickTaskGridStatus.COMPLETED.intCode().equals(i.getGridStatus())
                                || i.getGridQuantity() == null || !i.getGridQuantity().equals(i.getPickQuantity())))
                        .collect(Collectors.toList());
                initWhApvGrid(initItems, whPickingTask.getTaskNo());
            }
            else {
                initWhApvGrid(allItems, whPickingTask.getTaskNo());
            }
            whApvGrids = asnPrepareGridService.queryApvGrid(taskNo, location, taskType);
        }
        if (CollectionUtils.isEmpty(whApvGrids)) {
            asnPrepareGridService.addWhUniqueSkuLog(uuid, null, null, taskNo);
            rsp.setMessage("播种初始化格子失败！");
            return rsp;
        }

        // 上次扫描的fnSku
        List<WhFbaAllocationItem> lastFnSkuItems = getFnSkuItemList(whPickingTask.getTaskNo(), allItems,
                fbaAllocationItems);

        // 是否是当前发货单扫描的第一个产品
        boolean firstScan = lastFnSkuItems.stream()
                .allMatch(item -> item.getGridQuantity() == null || item.getGridQuantity() == 0);

        if (firstScan && (StringUtils.isBlank(uuid) || !uuid.contains("="))) {
            log.error(String.format("sku[%s]匹配的发货单[%s]还未绑定唯一码，请扫描唯一码!", sku, lastFnSkuItems.get(0).getFbaNo()));
            throw new Exception(
                    String.format("sku[%s]匹配的发货单[%s]还未绑定唯一码，请扫描唯一码!", sku, lastFnSkuItems.get(0).getFbaNo()));
        }

        List<WhApvGrid> gridList = whApvGrids.stream()
                .filter(g -> g.getApvId().equals(lastFnSkuItems.get(0).getFbaId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gridList)) {
            rsp.setMessage("sku不属于当前拣货任务");
            return rsp;
        }
        List<WhApvGridItem> lastGridItems = gridList.get(0).getGridItems().stream()
                .filter(i -> i.getItemSku().equalsIgnoreCase(lastFnSkuItems.get(0).getFnSku()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lastGridItems)) {
            rsp.setMessage("sku不属于当前拣货任务");
            return rsp;
        }
        List<WhFbaAllocationItem> skuItemList = lastFnSkuItems.stream().filter(i -> i.getProductSku().equals(sku))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuItemList)) {
            rsp.setMessage("sku不属于当前拣货任务");
            return rsp;
        }

        boolean allNoPick = skuItemList.stream().allMatch(i -> i.getPickQuantity() == null || i.getPickQuantity() == 0);

        if (allNoPick) {
            rsp.setMessage("没有可播种的fnsku,请点击拣货任务播种完成！");
            return rsp;
        }

        boolean skuGirdFinish = skuItemList.stream()
                .allMatch(i -> i.getGridQuantity() != null && i.getPickQuantity() != null && i.getPickQuantity() > 0
                        && i.getGridQuantity() >= i.getPickQuantity());
        if (skuGirdFinish) {
            // 更新播种状态
            skuItemList.forEach(skuItem->{
                WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
                updateItem.setId(skuItem.getId());
                updateItem.setGridQuantity(skuItem.getPickQuantity());
                updateItem.setGridStatus(PickTaskGridStatus.COMPLETED.intCode());
                updateItem.setGridBy(DataContextHolder.getUserId());
                updateItem.setGridTime(new Timestamp(System.currentTimeMillis()));
                whFbaAllocationItemService.updateWhFbaAllocationItem(updateItem);
            });
            rsp.setMessage("FNSKU：" + skuItemList.get(0).getFnSku() + "的sku：" + sku + "播种完成！");
            return rsp;
        }

        WhFbaAllocationItem skuItem = skuItemList.stream()
                .filter(i -> i.getGridQuantity() == null || i.getGridQuantity() < i.getPickQuantity()).findFirst()
                .get();
        Integer pickQuantity = lastFnSkuItems.stream()
                .mapToInt(i -> i.getPickQuantity() == null ? 0 : i.getPickQuantity()).sum();
        Integer allotQuantity = lastFnSkuItems.stream()
                .mapToInt(i -> i.getAllotQuantity() == null ? 0 : i.getAllotQuantity()).sum();
        Integer quantity = lastFnSkuItems.stream().mapToInt(i -> i.getQuantity() == null ? 0 : i.getQuantity()).sum();
        Integer gridQuantity = 1
                + lastFnSkuItems.stream().mapToInt(i -> i.getGridQuantity() == null ? 0 : i.getGridQuantity()).sum();
        // 当解析的FNSKU在当前拣货任务中不存在/播种数量>拣货数量时提示(sku不属于当前拣货任务)

        WhFbaAllocationItem fnSkuItem = BeanConvertUtils.convert(skuItem, WhFbaAllocationItem.class);
        fnSkuItem.setPickQuantity(pickQuantity);
        fnSkuItem.setAllotQuantity(allotQuantity);
        fnSkuItem.setQuantity(quantity);
        fnSkuItem.setGridQuantity(gridQuantity);
        domain.setFnSkuItem(fnSkuItem);
        domain.setLastFnSku(StringRedisUtils.get(RedisKeys.getFbaGridSku(whPickingTask.getTaskNo())));
        asnPrepareGridService.addWhUniqueSkuLog(uuid, fnSkuItem.getFbaId(), null, whPickingTask.getTaskNo());
        if (gridQuantity > pickQuantity) {
            fnSkuItem.setGridQuantity(gridQuantity - 1);
            rsp.setMessage("sku不属于当前拣货任务");
            return rsp;
        }

        Integer gridNum = 1 + (skuItem.getGridQuantity() == null ? 0 : skuItem.getGridQuantity());
        Integer pickNum = skuItem.getPickQuantity() == null ? 0 : skuItem.getPickQuantity();
        Integer allotNum = skuItem.getAllotQuantity() == null ? 0 : skuItem.getAllotQuantity();
        // 更新播种数量
        WhFbaAllocationItem updateItem = new WhFbaAllocationItem();
        updateItem.setId(skuItem.getId());
        if (gridNum <= pickNum)
            updateItem.setGridQuantity(gridNum);
        // 播种数量是否=发货数量（播种完成）
        if (gridNum >= allotNum) {
            updateItem.setGridStatus(PickTaskGridStatus.COMPLETED.intCode());
            updateItem.setGridBy(DataContextHolder.getUserId());
            updateItem.setGridTime(new Timestamp(System.currentTimeMillis()));
        }
        whFbaAllocationItemService.updateWhFbaAllocationItem(updateItem);
        // TODO 记录播种数量到 wh_apv_out_stock_china
        whApvOutStockChinaService.updateApvOutStockChinaGirdStatus(skuItem.getProductSku(), skuItem.getFbaNo(), null,
                AssetOrderType.ASN_ORDER.intCode());

        if (gridNum > pickNum) {
            rsp.setMessage(String.format("FNSKU：s%的sku：s%播种完成！", skuItem.getFnSku(), skuItem.getProductSku()));
            return rsp;
        }
        // 更新格子播种数量
        WhApvGridItem apvGridItem = lastGridItems.get(0);
        WhApvGridItem updateGridItem = new WhApvGridItem();
        updateGridItem.setGridQuantity(Optional.ofNullable(apvGridItem.getGridQuantity()).orElse(0) + 1);
        updateGridItem.setId(apvGridItem.getId());
        whApvGridItemDao.updateWhApvGridItem(updateGridItem);

        // 组装已播数据
        WhPickingTaskSkuQueryCondition query = new WhPickingTaskSkuQueryCondition();
        query.setTaskId(whPickingTask.getId());
        query.setSku(sku);
        WhPickingTaskSku whPickingTaskSku = whPickingTaskSkuService.queryWhPickingTaskSku(query);
        if (whPickingTaskSku != null) {
            whPickingTaskSku.setSowDifferQuantity(Optional.ofNullable(whPickingTaskSku.getSowDifferQuantity())
                    .orElse(whPickingTaskSku.getQuantity()) - 1);
            whPickingTaskSkuService.updateWhPickingTaskSku(whPickingTaskSku);
        }
        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    /**
     * 匹配当前扫描sku对应的FNSKU
     *
     * @param taskNo
     * @param allItems
     * @param fbaAllocationItems
     * @return
     * @throws Exception
     */
    private List<WhFbaAllocationItem> getFnSkuItemList(String taskNo, List<WhFbaAllocationItem> allItems,
            List<WhFbaAllocationItem> fbaAllocationItems) throws Exception {
        List<WhFbaAllocationItem> lastFnSkuItems = new ArrayList<>();
        if (StringUtils.isEmpty(taskNo) || CollectionUtils.isEmpty(allItems)
                || CollectionUtils.isEmpty(fbaAllocationItems))
            return lastFnSkuItems;
        String lastFnSku = StringRedisUtils.get(RedisKeys.getFbaGridSku(taskNo));
        Map<String, List<WhFbaAllocationItem>> allFnSkuMap = allItems.stream()
                .collect(Collectors.groupingBy(i -> i.getFbaId() + "_" + i.getFnSku()));
        if (StringUtils.isEmpty(lastFnSku)) {
            StringRedisUtils.set(RedisKeys.getFbaGridSku(taskNo),
                    fbaAllocationItems.get(0).getFbaId() + "_" + fbaAllocationItems.get(0).getFnSku());

            return allFnSkuMap.get(fbaAllocationItems.get(0).getFbaId() + "_" + fbaAllocationItems.get(0).getFnSku());
        }
        // 上次扫描的fnSku
        lastFnSkuItems = allFnSkuMap.get(lastFnSku);

        boolean allGridCompleted = Optional.ofNullable(lastFnSkuItems).orElse(new ArrayList<>()).stream()
                .allMatch(i -> i.getPickQuantity() == null || i.getPickQuantity() == 0
                        || PickTaskGridStatus.COMPLETED.intCode().equals(i.getGridStatus())
                        || (i.getGridQuantity() != null && i.getPickQuantity().equals(i.getGridQuantity()))
                        || PickTaskGridStatus.getGridExceptionCode().contains(i.getGridStatus()));

        // 本次扫描获取的fnSku
        Map<String, List<WhFbaAllocationItem>> fnSkuMap = fbaAllocationItems.stream()
                .collect(Collectors.groupingBy(i -> i.getFbaId() + "_" + i.getFnSku()));

        // 播种第一个唯一码后解析为FNSKU，直到该FNSKU确认播种完成前，不能播种其他FNSKU
        if (CollectionUtils.isNotEmpty(lastFnSkuItems) && !allGridCompleted
                && CollectionUtils.isEmpty(fnSkuMap.get(lastFnSku))) {
            throw new Exception("唯一码不属于当前FNSKU：" + StringUtils.split(lastFnSku, "_")[1] + "，请扫描当前播种的sku唯一码！");
        }

        if (allGridCompleted && CollectionUtils.isEmpty(fnSkuMap.get(lastFnSku))) {
            lastFnSkuItems = fnSkuMap.values().stream().collect(Collectors.toList()).get(0);
            StringRedisUtils.set(RedisKeys.getFbaGridSku(taskNo),
                    lastFnSkuItems.get(0).getFbaId() + "_" + lastFnSkuItems.get(0).getFnSku());
        }
        return lastFnSkuItems;
    }

    /**
     * 第一次播种扫描初始化所有的格子
     *
     * @param itemList
     * @param taskNo
     * @return
     */
    public boolean initWhApvGrid(List<WhFbaAllocationItem> itemList, String taskNo) {
        if (CollectionUtils.isNotEmpty(itemList) && StringUtils.isNotEmpty(taskNo)) {
            Map<Integer, Map<String, List<WhFbaAllocationItem>>> groupMap = itemList.stream().collect(Collectors
                    .groupingBy(item -> item.getFbaId(), Collectors.groupingBy(WhFbaAllocationItem::getFnSku)));
            int availableGridNumber = 1;

            for (Map.Entry<Integer, Map<String, List<WhFbaAllocationItem>>> entry : groupMap.entrySet()) {
                Integer fbaId = entry.getKey();
                // 创建格子
                WhApvGrid whApvGrid = new WhApvGrid();
                whApvGrid.setLocation(LocationWarehouseType.OLD.intCode());
                whApvGrid.setSerialNumber(taskNo);
                whApvGrid.setNumber(availableGridNumber++);
                whApvGrid.setApvId(fbaId);
                whApvGrid.setStatus(ApvGridStatus.PENDING.intCode());// 待处理
                whApvGridDao.createWhApvGrid(whApvGrid);

                List<WhApvGridItem> gridItems = whApvGrid.getGridItems();

                for (Map.Entry<String, List<WhFbaAllocationItem>> fnSkuEntry : entry.getValue().entrySet()) {
                    String fnSku = fnSkuEntry.getKey();
                    List<WhFbaAllocationItem> fnSkuList = fnSkuEntry.getValue();
                    Integer pickQuantity = Optional.ofNullable(fnSkuList).orElse(new ArrayList<>()).stream()
                            .mapToInt(i -> Optional.ofNullable(i.getPickQuantity()).orElse(0)).sum();
                    Integer skuQuantity = Optional.ofNullable(fnSkuList).orElse(new ArrayList<>()).stream()
                            .mapToInt(i -> Optional.ofNullable(i.getSkuQuantity()).orElse(0)).sum();
                    WhApvGridItem gridItem = new WhApvGridItem();
                    gridItem.setItemSku(fnSku);
                    gridItem.setItemQuantity(skuQuantity);
                    gridItem.setPickQuantity(pickQuantity);// 初始化拣货数量
                    gridItem.setGridQuantity(0);
                    gridItem.setGridId(whApvGrid.getId());
                    gridItems.add(gridItem);
                }
                whApvGridItemDao.batchCreateWhApvGridItem(gridItems);
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ResponseJson normalGridComplete(FbaGridDo fbaGridDo) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        List<WhFbaAllocation> allocationList = fbaGridDo.getWhFbaAllocationList();
        // 发货单关联的任务全部播种完
        boolean fnSkuFinish = StringUtils.isNotEmpty(fbaGridDo.getFnSku())
                && StringUtils.isNotEmpty(fbaGridDo.getFbaNo());
        if (fnSkuFinish) {
            rsp.setStatus(StatusCode.SUCCESS);
            return rsp;
        }
        boolean fbaAllGrid = FbaGridUtils.checkTaskAllGrid(fbaGridDo);
        fbaGridDo.setFbaAllGrid(fbaAllGrid);
        this.updateWhFbaAllocationItems(fbaGridDo);

        WhPickingTask whPickingTask = fbaGridDo.getWhPickingTask();
        boolean ycFinish = PickingTaskType.BZYC.intCode().equals(whPickingTask.getTaskType());

        // 判断是否为海外仓头程单任务
        boolean isAsnFirstTask = PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(whPickingTask.getTaskType())
                || PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(whPickingTask.getTaskType());

        // 记录播种完成的单据,保存完成后，再推送，减小事务影响
        List<WhFbaAllocation> whFbaAllocationList = new ArrayList<>();
        allocationList.forEach(f -> {
            if (!f.orderGrid() && !ycFinish)
                return;

            // 海外仓头程单根据APV类型进行额外的播种验证
            if (isAsnFirstTask && !validateAsnFirstGridByApvType(f, whPickingTask)) {
                return;
            }

            WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
            whFbaAllocation.setId(f.getId());
            whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_LABEL.intCode());
            whFbaAllocationService.updateWhFbaAllocation(whFbaAllocation);
            // 播种完成
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(f.getFbaNo());
            apvTrack.setSowUser(DataContextHolder.getUserId());
            apvTrack.setSowFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTrackService.updateApvTrackByApvNo(apvTrack);

            SystemLogUtils.FBAALLOCATIONLOG.log(f.getId(), "订单状态变更，播种",
                    new String[][] { { "历史状态", AsnPrepareStatus.getNameByCode(f.getStatus().toString()) },
                            { "更改状态", AsnPrepareStatus.getNameByCode(AsnPrepareStatus.WAITING_LABEL.getCode()) } });
            whFbaAllocationList.add(f);

        });
        // fba播种完成，发送消息
        for (WhFbaAllocation whFbaAllocation: whFbaAllocationList) {
            whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_LABEL.intCode());
            whFbaAllocationService.sendMsg(whFbaAllocation);
        }
        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    /**
     * 验证海外仓头程单根据APV类型的播种逻辑
     *
     * @param allocation 发货单
     * @param whPickingTask 拣货任务
     * @return 是否通过验证
     */
    private boolean validateAsnFirstGridByApvType(WhFbaAllocation allocation, WhPickingTask whPickingTask) {
        String apvType = allocation.getApvType();
        if (StringUtils.isBlank(apvType)) {
            return true; // 如果APV类型为空，默认通过验证
        }

        // 海外仓单品任务：只允许SS和SM类型
        if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(whPickingTask.getTaskType())) {
            return ApvTypeEnum.SS.getCode().equals(apvType) || ApvTypeEnum.SM.getCode().equals(apvType);
        }

        // 海外仓多品任务：只允许MM类型
        if (PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(whPickingTask.getTaskType())) {
            return ApvTypeEnum.MM.getCode().equals(apvType);
        }

        return true; // 其他情况默认通过验证
    }

    @Override
    @StockServicelock
    @Transactional(propagation = Propagation.REQUIRED)
    public ResponseJson updateLessPickAndLessGridFinish(List<String> skus, FbaGridDo fbaGridDo,
            List<WhFbaAllocationItem> lessItemList) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        boolean fnSkuFinish = StringUtils.isNotEmpty(fbaGridDo.getFnSku())
                && StringUtils.isNotEmpty(fbaGridDo.getFbaNo());

        WhPickingTask whPickingTask = fbaGridDo.getWhPickingTask();
        boolean exceptionFinish = PickingTaskType.BZCY.intCode().equals(whPickingTask.getTaskType())
                || PickingTaskType.BZYC.intCode().equals(whPickingTask.getTaskType());

        if (fnSkuFinish) {
            if (!exceptionFinish && CollectionUtils.isNotEmpty(fbaGridDo.getLessGridItemList())) {
                rsp.setMessage("播种数量少于拣货数量，请绑定播种差异周转筐！");
                rsp.setExceptionCode(PdaExceptionCode.LESS_GRID);
                return rsp;
            }
            if (!exceptionFinish && CollectionUtils.isNotEmpty(fbaGridDo.getLessPickItemList())) {
                rsp.setMessage("拣货数量少于已分配数量，请绑定播种异常周转筐！");
                rsp.setExceptionCode(PdaExceptionCode.LESS_PICK);
                return rsp;
            }
            if (PickingTaskType.BZCY.intCode().equals(whPickingTask.getTaskType())
                    && CollectionUtils.isNotEmpty(fbaGridDo.getLessPickItemList())) {
                rsp.setMessage("拣货数量少于已分配数量，请绑定播种异常周转筐！");
                rsp.setExceptionCode(PdaExceptionCode.LESS_PICK);
                return rsp;
            }

            rsp.setStatus(StatusCode.SUCCESS);
            return rsp;
        }

        boolean fbaAllGrid = fbaGridDo.isFbaAllGrid();

        // 第一次播种有少拣的，生成二次配货；少播的生成播种异常
        if (exceptionFinish && fbaAllGrid) {
            this.updateStockAndCreatePickInventoryDemand(skus, fbaGridDo, lessItemList);
            // 播种完成的推送状态
            fbaGridDo.getWhFbaAllocationList().stream()
                    .filter(f -> AsnPrepareStatus.WAITING_LABEL.intCode().equals(f.getStatus())).forEach(f -> {
                whFbaAllocationService.sendMsg(f);
            });
            rsp.setStatus(StatusCode.SUCCESS);
            return rsp;
        }

        if (!exceptionFinish && CollectionUtils.isNotEmpty(fbaGridDo.getLessGridItemList())) {
            rsp.setMessage("播种数量少于拣货数量，请绑定播种差异周转筐！");
            rsp.setExceptionCode(PdaExceptionCode.LESS_GRID);
            return rsp;
        }

        if (!PickingTaskType.BZYC.intCode().equals(whPickingTask.getTaskType())
                && CollectionUtils.isNotEmpty(fbaGridDo.getLessPickItemList())) {
            rsp.setMessage("拣货数量少于已分配数量，请绑定播种异常周转筐！");
            rsp.setExceptionCode(PdaExceptionCode.LESS_PICK);
            return rsp;
        }

        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    /**
     * 异常播种完成退库存，生成拣货缺货盘点需求
     *
     * @param skus
     * @param fbaGridDo
     * @param lessItemList
     */
    private void updateStockAndCreatePickInventoryDemand(List<String> skus, FbaGridDo fbaGridDo,
            List<WhFbaAllocationItem> lessItemList) {
        WhPickingTask whPickingTask = fbaGridDo.getWhPickingTask();
        List<WhFbaAllocation> allocationList = fbaGridDo.getWhFbaAllocationList();

        Map<String, Integer> pickMap = new HashMap<>();
        Map<String, Integer> allotMap = new HashMap<>();
        Map<String, Integer> gridMap = new HashMap<>();
        Map<String, Integer> needPickMap = new HashMap<>();
        for (WhFbaAllocationItem item : lessItemList) {
            Integer pickedQuantity = item.getPickQuantity() == null ? 0 : item.getPickQuantity();
            Integer saleQuantity = item.getAllotQuantity() == null ? 0 : item.getAllotQuantity();
            Integer gridQuantity = item.getGridQuantity() == null ? 0 : item.getGridQuantity();

            Integer pickQuantity = pickMap.get(item.getProductSku()) == null ? 0 : pickMap.get(item.getProductSku());
            pickMap.put(item.getProductSku(), pickQuantity + pickedQuantity - gridQuantity);
            Integer allotQuantity = allotMap.get(item.getProductSku()) == null ? 0 : allotMap.get(item.getProductSku());
            // 缺货SKU未播种到的数量移回到待分配
            allotMap.put(item.getProductSku(), allotQuantity + (saleQuantity - pickedQuantity));

            Integer needPick = needPickMap.get(item.getProductSku()) == null ? 0
                    : needPickMap.get(item.getProductSku());
            Integer grid = gridMap.get(item.getProductSku()) == null ? 0 : gridMap.get(item.getProductSku());
            needPickMap.put(item.getProductSku(), needPick + saleQuantity);
            gridMap.put(item.getProductSku(), grid + gridQuantity);
        }
        // TODO 退库存 taskNo
        if (!apvGridUpdateStockService.fbaPickReturn(skus, pickMap, allotMap, whPickingTask.getTaskNo(),
                allocationList)) {
            throw new RuntimeException("播种完成操作库存失败!");
        }

        // 发货单变待贴标，标记拣货缺货
        this.updateStockOut(allocationList);
        this.updateWhFbaAllocationItems(fbaGridDo);
    }

    /**
     * 修改明细播种状态
     *
     * @param fbaGridDo
     */
    private void updateWhFbaAllocationItems(FbaGridDo fbaGridDo) {
        List<WhFbaAllocationItem> fnSkuItemList = fbaGridDo.getTaskItemList();
        fnSkuItemList.forEach(i -> {
            if (i.getGridQuantity() != null && i.getGridQuantity().equals(i.getAllotQuantity())) {
                WhFbaAllocationItem fnSkuItem = new WhFbaAllocationItem();
                fnSkuItem.setId(i.getId());
                fnSkuItem.setGridStatus(PickTaskGridStatus.COMPLETED.intCode());
                fnSkuItem.setGridBy(DataContextHolder.getUserId());
                fnSkuItem.setGridTime(new Timestamp(System.currentTimeMillis()));
                whFbaAllocationItemService.updateWhFbaAllocationItem(fnSkuItem);
            }
        });
        // 播种完成=批量绑定唯一码
        this.doBindUuidAndUpdateTaskStatus(fbaGridDo, fbaGridDo.getWhPickingTask(), fbaGridDo.getWhFbaAllocationList(),
                fbaGridDo.getTaskItemList());
    }

    /**
     * 播种完成,批量绑定唯一码
     *
     * @param fbaGridDo
     * @param whPickingTask
     * @param allocationList
     * @param fnSkuItemList
     */
    @Override
    public void doBindUuidAndUpdateTaskStatus(FbaGridDo fbaGridDo, WhPickingTask whPickingTask,
            List<WhFbaAllocation> allocationList, List<WhFbaAllocationItem> fnSkuItemList) {
        boolean fbaAllGrid = fbaGridDo.isFbaAllGrid();
        allocationList.forEach(f -> {
            if (fbaAllGrid) {
                asnPrepareGridService.gridAndBindingApv(allocationList.get(0).getFbaNo(), whPickingTask.getTaskNo());
            }
            // 格子已完成
            WhApvGrid whApvGrid = new WhApvGrid();
            whApvGrid.setStatus(ApvGridStatus.COMPLETED.intCode());
            whApvGrid.setSerialNumber(whPickingTask.getTaskNo());
            whApvGrid.setApvId(f.getId());
            whApvGridDao.updateStatusByFbaIdAndTaskNo(whApvGrid);
        });

        // 组装已播数据
        List<SowStockout> gridSkuSowStockouts = FbaGridUtils.buildHasGridSkuCountData(fnSkuItemList);
        whPickingTaskService.updatePickingTaskDiffer(gridSkuSowStockouts, whPickingTask);

        // 更新播种完成状态
        pickTaskExpandService.updatePickTaskExpandStatusByTaskNo(whPickingTask.getTaskNo(),
                PickTaskGridStatus.COMPLETED.intCode());
        // TODO 解绑周转筐
        whBoxService.updateWhBoxOfUnbinding(fbaGridDo.getBoxNo(),
                new String[][] { { "解绑周转筐", String.valueOf(whPickingTask.getId()) } });
    }

    @Override
    public void updateStockOut(List<WhFbaAllocation> stockoutWhApvs) {
        if (CollectionUtils.isEmpty(stockoutWhApvs))
            return;
        // 缺货apv id 主键
        List<Integer> stockIds = new ArrayList<Integer>();
        List<WhFbaAllocation> updateWhApvs = new ArrayList<WhFbaAllocation>();
        for (WhFbaAllocation whApv : stockoutWhApvs) {
            stockIds.add(whApv.getId());
            WhFbaAllocation updateWhApv = new WhFbaAllocation();
            updateWhApv.setId(whApv.getId());
            updateWhApv.setStatus(AsnPrepareStatus.WAITING_LABEL.intCode());
            updateWhApv.setPickOut(true);
            updateWhApvs.add(updateWhApv);
            whApv.setStatus(AsnPrepareStatus.WAITING_LABEL.intCode());
        }

        // 删除批次详情
        whApvBatchDetailDao.batchDeleteWhApvBatchDetailByApvIds(stockIds);

        // 更新状态为拣货缺货
        whFbaAllocationService.batchUpdateWhFbaAllocation(updateWhApvs);

        for (Integer id : stockIds) {
            SystemLogUtils.FBAALLOCATIONLOG.log(id, "订单单状态变更，播种缺货",
                    new String[][] {
                            { "历史状态", AsnPrepareStatus.getNameByCode(AsnPrepareStatus.WAITING_PICK.getCode()) },
                            { "更改状态", AsnPrepareStatus.getNameByCode(AsnPrepareStatus.WAITING_LABEL.getCode()) } });
        }
    }

    /**
     * 扫描组装数据
     *
     * @param domain
     * @throws Exception
     */
    @Override
    public void scanBoxToBuildGridData(FbaGridDo domain) throws Exception {
        WhPickingTask whPickingTask = domain.getWhPickingTask();

        WhPickingTaskSkuQueryCondition query = new WhPickingTaskSkuQueryCondition();
        query.setTaskId(whPickingTask.getId());
        List<WhPickingTaskSku> whPickingTaskSkus = whPickingTaskSkuService.queryWhPickingTaskSkus(query, null);
        if (CollectionUtils.isEmpty(whPickingTaskSkus))
            throw new Exception(String.format("扫描的周转筐或拣货任务号没有查到相关SKU明细"));

        List<WhFbaAllocation> allList = whFbaAllocationService.queryAllocationByPickingTaskId(whPickingTask.getId());
        if (CollectionUtils.isEmpty(allList))
            throw new Exception(String.format("扫描的周转筐或拣货任务号没有查到相关发货单"));

        domain.setWhFbaAllocationList(allList);

        List<WhFbaAllocationItem> lessGridItemList = new ArrayList<>();// 播种差异（播种<已拣）

        List<WhFbaAllocationItem> lessPickItemList = new ArrayList<>();// 少拣的
        List<WhFbaAllocationItem> taskItemList = new ArrayList<>();
        boolean fnSkuFinish = StringUtils.isNotEmpty(domain.getFnSku()) && StringUtils.isNotEmpty(domain.getFbaNo());
        boolean exceptionFinish = PickingTaskType.BZCY.intCode().equals(whPickingTask.getTaskType())
                || PickingTaskType.BZYC.intCode().equals(whPickingTask.getTaskType());

        allList.forEach(f -> {
            f.getItems().forEach(item -> {
                if (item.getAllotQuantity() == null || item.getAllotQuantity() == 0)
                    return;
                // 第一次fnsku播种完成
                if (fnSkuFinish && !exceptionFinish && item.getPickQuantity() != null
                        && (item.getGridQuantity() == null ? 0 : item.getGridQuantity()) < item.getPickQuantity()) {
                    lessGridItemList.add(item);
                    return;
                }
                if (fnSkuFinish && !exceptionFinish
                        && (item.getPickQuantity() == null ? 0 : item.getPickQuantity()) < item.getAllotQuantity()) {
                    lessPickItemList.add(item);
                    return;
                }
                // 播种差异fnsku播种完成
                if (fnSkuFinish && exceptionFinish && item.getGridStatus() != null
                        && PickTaskGridStatus.GRID_EXCEPTION.intCode().equals(item.getGridStatus())
                        && (item.getPickQuantity() == null ? 0 : item.getPickQuantity()) < item.getAllotQuantity()) {
                    lessPickItemList.add(item);
                    return;
                }

                // 拣货任务播种完成
                if (!fnSkuFinish && item.getGridStatus() == null && item.getPickQuantity() != null
                        && item.getPickQuantity() > 0
                        && (item.getGridQuantity() == null || item.getGridQuantity() == 0)) {
                    lessGridItemList.add(item);
                    return;
                }
                if (!fnSkuFinish && item.getGridStatus() == null
                        && (item.getPickQuantity() == null || item.getPickQuantity() == 0)) {
                    lessPickItemList.add(item);
                    return;
                }

            });
            taskItemList.addAll(f.getItems());
        });

        // 已拣总数
        Integer pickQuantity = whPickingTaskService.queryPickQuantity(whPickingTask);
        // 已播总数
        Integer sowingQuantity = whApvGridDao.querySowingQuantity(whPickingTask.getTaskNo());
        whPickingTask.setSowDifferQuantity(
                (pickQuantity == null ? 0 : pickQuantity) - (sowingQuantity == null ? 0 : sowingQuantity));

        domain.setGridQuantity(sowingQuantity);
        domain.setPickQuantity(pickQuantity);
        domain.setTaskItemList(taskItemList);
        domain.setLessGridItemList(lessGridItemList);
        domain.setLessPickItemList(lessPickItemList);
        domain.setWhPickingTaskSkuList(whPickingTaskSkus);
    }

    @Override
    public void updateStockOutFinishGrid(WhBox stockoutWhBox, WhBox caYiWhBox, FbaGridDo domain) {
        WhPickingTask whPickingTask = domain.getWhPickingTask();

        // 当前发货单FNSKU少拣少播
        List<WhFbaAllocationItem> pickOutGrids = domain.getLessPickItemList();
        List<WhFbaAllocationItem> lessGrids = domain.getLessGridItemList();

        List<Integer> notCompleteApvIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pickOutGrids)) {
            notCompleteApvIds.addAll(
                    pickOutGrids.stream().map(WhFbaAllocationItem::getFbaId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(lessGrids)) {
            notCompleteApvIds.addAll(
                    lessGrids.stream().map(WhFbaAllocationItem::getFbaId).distinct().collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(notCompleteApvIds)) {
            // 删除批次详情
            notCompleteApvIds = notCompleteApvIds.stream().distinct().collect(Collectors.toList());
            whApvBatchDetailDao.batchDeleteWhApvBatchDetailByApvIds(notCompleteApvIds);
        }

        if (caYiWhBox != null) {
            List<SowStockout> cayiSowStockouts = FbaGridUtils.buildCayiSowStockoutData(lessGrids);
            // 创建播种差异任务
            String chaYiTaskNo = asnPrepareGridService.createCayiTask(caYiWhBox.getBoxNo(), cayiSowStockouts,
                    whPickingTask.getReceivePerson(), whPickingTask.getTaskType());
            initWhApvGrid(lessGrids, chaYiTaskNo);
            // 添加明细播种标识
            updateItemGridStatusToException(lessGrids,PickTaskGridStatus.GRID_EXCEPTION.intCode());
        }

        if (stockoutWhBox != null) {
            // 创建二次配货数据
            List<SowStockout> pickOutSowStockouts = FbaGridUtils.buildPickOutSowStockoutData(domain.getWhBox(),
                    stockoutWhBox, pickOutGrids);
            pickOutSowStockouts.forEach(o -> o.setOldTaskType(PickingTaskType.ASN_PREPARE.intCode()));
            whPickingTaskService.doSowStockOut(null, pickOutSowStockouts);
            // 添加明细播种标识
            updateItemGridStatusToException(pickOutGrids,PickTaskGridStatus.PICK_EXCEPTION.intCode());
        }
        // 已播SKU数据
        List<SowStockout> gridSkuSowStockouts = FbaGridUtils.buildHasGridSkuCountData(domain.getTaskItemList());
        whPickingTaskService.updatePickingTaskDiffer(gridSkuSowStockouts, whPickingTask);
        boolean fnSkuFinish = StringUtils.isNotEmpty(domain.getFnSku()) && StringUtils.isNotEmpty(domain.getFbaNo());

        StringRedisUtils.del(RedisKeys.getFbaGridSku(whPickingTask.getTaskNo()));
        if (fnSkuFinish)
            return;

        if (stockoutWhBox != null && CollectionUtils.isEmpty(lessGrids)
                || caYiWhBox != null && CollectionUtils.isEmpty(pickOutGrids)) {
            // 更新播种完成状态
            pickTaskExpandService.updatePickTaskExpandStatusByTaskNo(whPickingTask.getTaskNo(),
                    PickTaskGridStatus.COMPLETED.intCode());
            // TODO 解绑周转筐
            whBoxService.updateWhBoxOfUnbinding(domain.getBoxNo(),
                    new String[][] { { "解绑周转筐", String.valueOf(whPickingTask.getId()) } });

        }
        StringRedisUtils.del(RedisKeys.getFbaGridSku(whPickingTask.getTaskNo()));
    }

    // 添加明细播种标识
    private void updateItemGridStatusToException(List<WhFbaAllocationItem> lessList, Integer status) {
        if (CollectionUtils.isEmpty(lessList))
            return;
        status = status == null ? PickTaskGridStatus.GRID_EXCEPTION.intCode() : status;
        Integer finalStatus = status;
        lessList.stream().forEach(i -> i.setGridStatus(finalStatus));
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(lessList);

    }
}
