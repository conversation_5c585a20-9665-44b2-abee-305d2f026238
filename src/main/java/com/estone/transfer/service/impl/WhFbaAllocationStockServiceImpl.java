package com.estone.transfer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.android.domain.AndroidProductDo;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.enums.ApvTypeEnum;
import com.estone.apv.enums.WhApvOutStockChainStatusEnum;
import com.estone.apv.service.WhApvOutStockChainCancelService;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.common.SaleChannel;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.Constant;
import com.estone.common.util.DateUtils;
import com.estone.common.util.DingTalkSendUtils;
import com.estone.picking.enums.PickingTaskType;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.model.OmsFbaSkuMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.transfer.bean.*;
import com.estone.transfer.enums.*;
import com.estone.transfer.service.TransferStockService;
import com.estone.transfer.service.TransitBatchHandleService;
import com.estone.transfer.service.WhFbaAllocationStockService;
import com.estone.transfer.service.WhTransitStockLogService;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.PrestorageStockTransferOrder;
import com.estone.warehouse.bean.PrestorageStockTransferQueryCondition;
import com.estone.warehouse.bean.PrestorageStockTransferSkuBO;
import com.estone.warehouse.enums.*;
import com.estone.warehouse.service.ApvGridUpdateStockService;
import com.estone.warehouse.service.PrestorageStockTransferService;
import com.estone.warehouse.service.WhStockChangeRecordService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: Yimeil
 * @Date: 2021/6/29 18:14
 * @Version: 1.0.0
 */
@Service
@Slf4j
public class WhFbaAllocationStockServiceImpl implements WhFbaAllocationStockService {

    @Resource
    private TransferStockService transferStockService;

    @Resource
    private WhTransitStockLogService stockLogService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    @Resource
    private ApvGridUpdateStockService apvGridUpdateStockService;

    @Resource
    private TransitBatchHandleService transitBatchHandleService;

    @Resource
    private WhApvOutStockChainCancelService whApvOutStockChainCancelService;

    @Resource
    private PrestorageStockTransferService prestorageStockTransferService;

    @Resource
    private WhStockChangeRecordService whStockChangeRecordService;

    @Autowired
    private Environment env;

    /**
     * 分配
     *
     * @param allocation
     * @return
     */
    @Override
    public String allot(WhFbaAllocation allocation,Map<String, TransferStock> stockMap1)  {
        String orderNo = allocation.getFbaNo();

        List<WhApvOutStockChain> whApvOutStockChains = new ArrayList<>();

        List<TransferStock> stockList = new ArrayList();
        List<WhTransitStockLog> stockLogs = new ArrayList();
        Map<String, List<TransferStock>>  stocksMap = getStockDetailsMap(allocation);

        /** 库存变更推送 */
        List<AmqMessage> msgList = new ArrayList<>();
        // 记录存货、缺货的sku
        List<String> preStoreSkus = new ArrayList<>(), stockOutNotSkus = new ArrayList<>();

        StockLogStep logStep = StockLogStep.FBA_ALLOCATION_ALLOT;
        if (!allocation.isFba())
            logStep = StockLogStep.ASN_ALLOT;
        // 需要存货迁移的sku集合
        List<PrestorageStockTransferSkuBO> migrationSkus = new ArrayList<>();
        for (WhFbaAllocationItem item : allocation.buildGroupItems()) {
            Integer quantity = item.getSkuQuantity() == null ? 0 : item.getSkuQuantity();
            Integer allot = item.getAllotQuantity() == null ? 0 : item.getAllotQuantity();
            // 待分配数量
            Integer waitQty = quantity - allot;
            if (waitQty <= 0) {
                continue;
            }

            String site = item.getSite();
            String sku = item.getProductSku();
            String key = allocation.isFba()?allocation.getUnprefixedAccountNumber() + sku
                    :allocation.getUnprefixedAccountNumber() + site + sku;
            List<TransferStock> transferStocks = stocksMap.get(key);
            if (CollectionUtils.isEmpty(transferStocks)) {
                throw new BusinessException(String.format("[%s]无库存记录!",sku));
            }
            String pickLocation = null;
            // 1 匹配售后结算
            List<TransferStock> saledBalanceList = transferStocks.stream().filter(w ->
                            (StringUtils.isNoneBlank(w.getLocationTag()) &&
                                    StringUtils.contains(w.getLocationTag(), LocationTagEnum.SALED_BALANCE.getCode())))
                    .sorted(Comparator.comparing(w -> w.getLastUpTime(),Comparator.nullsFirst(Comparator.naturalOrder())))
                    //.sorted(Comparator.comparing(TransferStock::getSurplusQuantity).reversed())
                    .collect(Collectors.toList());
            waitQty = calcStockQty(saledBalanceList, waitQty, stockList, stockLogs, logStep, orderNo,whApvOutStockChains,msgList,null,null);
            if (CollectionUtils.isNotEmpty(saledBalanceList) && StringUtils.isBlank(pickLocation))
                pickLocation = saledBalanceList.get(0).getLocationNumber();

            // 2 获取非存或、非售后结算的库存记录
            if (waitQty > 0) {
                List<TransferStock> pickLocationStock = transferStocks.stream().filter(w ->
                                (StringUtils.isBlank(w.getLocationTag()) ||
                                        (!StringUtils.contains(w.getLocationTag(), LocationTagEnum.PRESTORE.getCode())
                                                && !StringUtils.contains(w.getLocationTag(), LocationTagEnum.SALED_BALANCE.getCode()))))
                        .sorted(Comparator.comparing(TransferStock::getSurplusQuantity).reversed())
                        .collect(Collectors.toList());
                waitQty = calcStockQty(pickLocationStock, waitQty, stockList, stockLogs, logStep, orderNo, whApvOutStockChains, msgList, null, null);
                if (CollectionUtils.isNotEmpty(pickLocationStock) && StringUtils.isBlank(pickLocation))
                    pickLocation = pickLocationStock.get(0).getLocationNumber();
            }

            if (StringUtils.isBlank(pickLocation)) {
                log.info(String.format("[%s]无库位分配!试进行存货迁移分配操作，迁入库位到上架再指定。", sku));
//                throw new BusinessException(String.format("[%s]无库位分配!", sku));
            }
            // 3 校验存货，存货够，生产迁移任务，不够分情况处理
            if (waitQty > 0){
                List<TransferStock> prestoreList = transferStocks.stream().filter(w ->
                        (StringUtils.isNoneBlank(w.getLocationTag()) &&
                                StringUtils.contains(w.getLocationTag(), LocationTagEnum.PRESTORE.getCode())))
                        .sorted(Comparator.comparing(TransferStock::getSurplusQuantity).reversed())
                        .collect(Collectors.toList());
                waitQty = calcStockQty(prestoreList, waitQty, stockList, stockLogs, logStep, orderNo,whApvOutStockChains,msgList, migrationSkus,pickLocation);
                // 存货足够
                if (waitQty <= 0){
                    // 记录存货迁移sku
                    preStoreSkus.add(sku);
                }
            }
            // 4 区分是否可以部分分配,滞销品可部分分配
            if (waitQty > 0){
                stockOutNotSkus.add(sku);
            }
        }

        // 存货迁移的不继续分配
        if (CollectionUtils.isNotEmpty(preStoreSkus)){
            log.warn("订单: " + orderNo + ", 匹配库存失败, 以下sku需要存货迁移："+ JSONObject.toJSONString(preStoreSkus));
            // 对应订单存在未完成的存货迁移任务时，提示其先完成存货迁移任务再执行订单分配
            PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
            queryCondition.setStatusList(Arrays.asList(PrestorageStockTransferStatusEnum.WAITTING_ACCEPTED.getCode(),
                    PrestorageStockTransferStatusEnum.PICKING.getCode(), PrestorageStockTransferStatusEnum.PICKING_COMPLETED.getCode(),
                    PrestorageStockTransferStatusEnum.UPLODING.getCode()));
            queryCondition.setRelevantOrderNo(orderNo);
            List<PrestorageStockTransferOrder> existOrders = prestorageStockTransferService.queryTransferOrders(queryCondition,null);
            if (CollectionUtils.isNotEmpty(existOrders)){
                List<String> prestorageStockOrderNos = existOrders.stream().map(PrestorageStockTransferOrder::getOrderNo).collect(Collectors.toList());
                log.warn("订单: "+ orderNo + ",存在未完成的存货迁移任务。为此不再生成存货迁移任务，未完成的存货迁移任务编号:"+ JSON.toJSONString(prestorageStockOrderNos));
                return "订单: " + orderNo + ", 匹配库存失败,其存在未完成或未废弃的存货迁移任务，请先完成存货迁移任务："+JSON.toJSONString(prestorageStockOrderNos);
            }
            ResponseJson responseJson = prestorageStockTransferService.addAllotOrder(migrationSkus);
            if (Objects.equals(StatusCode.FAIL,responseJson.getStatus())){
                String tips = "订单："+ orderNo + "生成存货迁移任务失败！"+ responseJson.getMessage();
                log.error(tips);
                return tips;
            }
            existOrders = prestorageStockTransferService.queryTransferOrders(queryCondition, null);
            if (CollectionUtils.isNotEmpty(existOrders)) {
                List<String> prestorageStockOrderNos = existOrders.stream().map(PrestorageStockTransferOrder::getOrderNo).collect(Collectors.toList());
                log.warn("订单: " + orderNo + ",存在未完成的存货迁移任务。为此不再生成存货迁移任务，未完成的存货迁移任务编号:" + JSON.toJSONString(prestorageStockOrderNos));
                return "订单: " + orderNo + ", 匹配库存失败, 以下sku需要存货迁移：" + JSONObject.toJSONString(preStoreSkus) + ",请先执行完存货迁移任务:" + JSON.toJSONString(prestorageStockOrderNos);
            }
            return "订单: " + orderNo + ", 匹配库存失败, 以下sku需要存货迁移："+ JSONObject.toJSONString(preStoreSkus)+",生成存货迁移任务失败！";
        }
        // 缺货提醒
        if (CollectionUtils.isNotEmpty(stockOutNotSkus)) {
            log.warn("订单: " + orderNo + ", 匹配库存失败, 以下sku缺货："+ JSONObject.toJSONString(stockOutNotSkus));
           // SystemLogUtils.FBAALLOCATIONLOG.log(allocation.getId(), " 匹配库存失败, 以下sku缺货："+ JSONObject.toJSONString(stockOutNotSkus));
            return "订单: " + orderNo + ", 匹配库存失败, 以下sku缺货："+ JSONObject.toJSONString(stockOutNotSkus);
        }
        // 获取每个sku分配的数量
        Map<String, Integer> updateItemQuantityMap = whApvOutStockChains.stream().collect(Collectors.groupingBy(whApvOutStockChain -> whApvOutStockChain.getSku(),
                Collectors.summingInt(w -> w.getQuantity() == null ? 0 : w.getQuantity())));
        // 匹配已分配数量
        allocation.buildItemQuantity(1, updateItemQuantityMap);
        transferStockService.batchUpdateTransferStock(stockList);

        for (WhTransitStockLog stockLog : stockLogs) {
            String content = stockLog.getContent();
            if (allocation.isFba()) {
                content=content+","+ TransferLogOrderTypeEnum.FBA.intCode();
            }
            else if (allocation.isJit()) {
                content=content+","+ TransferLogOrderTypeEnum.JIT.intCode();
            }
            else if (allocation.getAsnFirst()) {
                content=content+","+ TransferLogOrderTypeEnum.OVERSEA_WAREHOUSE.intCode();
            }
            else if (allocation.isTransfer()) {
                content=content+","+ TransferLogOrderTypeEnum.TRANSFER.intCode();
            }
            stockLog.setContent(content);
        }
        
        // 记录中转仓库存日志
        stockLogService.batchAddWhStockLog(stockLogs);

        // 添加库存变动批次明细
        Map<String, List<TransferStockBatchDetail>> batchDetail = transitBatchHandleService.createTransitBatchDetail(stockLogs, QuantityType.CHECK_OUT,
                TransitBatchOrderType.SHIPMENT_ORDER, TransitBatchStockType.FBA, null);

        // 推送库存变更给产品
        amqMessageService.batchCreateAmqMessage(msgList);
        // 保存出库明细
        if (CollectionUtils.isNotEmpty(whApvOutStockChains)) {
            List<WhApvOutStockChain> whApvOutStockChainList = whApvOutStockChainService.queryWhApvOutStockChains(CommonUtils.splitList(orderNo, ""),null);
            whApvOutStockChainService.deleteWhApvOutStockChainByIds(whApvOutStockChainList.stream().map(w -> w.getId()).collect(Collectors.toList()));
        }
        whApvOutStockChainService.batchCreateWhApvOutStockChain(whApvOutStockChains);
        // 删除 移会待分配记录
        if (CollectionUtils.isNotEmpty(whApvOutStockChains))
            whApvOutStockChainCancelService.deleteWhApvOutStockChainCancelByRelevantNos(whApvOutStockChains.stream().map(w -> w.getRelevantNo()).collect(Collectors.toList()));
        List<TransferStockBatchDetail> detailList = new ArrayList<>();
        Optional.ofNullable(batchDetail).orElse(new HashMap<>()).forEach((k,v) -> {
            detailList.addAll(v);
        });
        //自发仓调拨
        boolean anyMatch = Optional.ofNullable(detailList).orElse(new ArrayList<>()).stream()
                .anyMatch(t -> t.getCheckInType().equals(TransitBatchStockType.LOCAL_2_FBA.intCode()));
        if (anyMatch)
            return "true";
        return null;
    }

    //扣减库存，并且记录日志
    private Integer calcStockQty(List<TransferStock> transferStocks,Integer waitQty, List<TransferStock> stockList, List<WhTransitStockLog> stockLogs,
                                 StockLogStep step, String content,List<WhApvOutStockChain> whApvOutStockChains, List<AmqMessage> msgList,
                                 List<PrestorageStockTransferSkuBO> migrationSkus, String pickLocation ){
        if (CollectionUtils.isEmpty(transferStocks)) return waitQty;
        for (TransferStock transferStock:transferStocks){
            if (waitQty <= 0) return waitQty;
            Integer surplusQuantity = transferStock.getSurplusQuantity() == null ? 0:transferStock.getSurplusQuantity();
            if (surplusQuantity <= 0) continue;
            TransferStock updateStock = TransferStock.buildUpdateStock(transferStock);
            //Integer upQty = Math.min(surplusQuantity,waitQty)
            Integer upQty =  waitQty - surplusQuantity > 0 ? surplusQuantity : waitQty;
            waitQty -= upQty;
            // 记录库存变更
            updateStock.setSurplusQuantity(surplusQuantity - upQty);
            int allotQty = transferStock.getAllotQuantity() == null ? 0 : transferStock.getAllotQuantity();
            updateStock.setAllotQuantity(allotQty + upQty);
            stockList.add(updateStock);
            // 记录库存日志变更

            stockLogs.add(new WhTransitStockLog(transferStock.getSku(), transferStock.getStore(), transferStock.getSite()
                    , TransferStockLogType.USABLE_STOCK, transferStock.getId(), transferStock.getLocationNumber(),step, -upQty,
                    surplusQuantity, content));
            stockLogs.add(new WhTransitStockLog(transferStock.getSku(), transferStock.getStore(), transferStock.getSite(),
                    TransferStockLogType.ALLOCATED,transferStock.getId(), transferStock.getLocationNumber(), step, upQty, allotQty,
                    content));
            // 库存分配明细
            whApvOutStockChains.add(new WhApvOutStockChain(transferStock.getSku(),content, WhApvOutStockChainStatusEnum.ALLOT.intCode(),
                    transferStock.getId(),upQty, 0, 0));
            // 推送fba库存变更到oms
            OmsFbaSkuMessage omsFbaSkuMessage = new OmsFbaSkuMessage(transferStock.getStore(),transferStock.getSite(),transferStock.getSku(),- upQty);
            msgList.add(AssembleMessageDataUtils.assembleooFbaStockData(omsFbaSkuMessage));
            // 推送中转仓库存变更到redis
            msgList.add(AssembleMessageDataUtils.assembleDataToFinance(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode(),
                    amqMessage -> {
                        amqMessage.setRelevantParam(transferStock.getSku());
                        Map<String,Object> map = new HashMap<>();
                        map.put("sku",transferStock.getSku());
                        map.put("count_surplus",-upQty);
                        map.put("saleChannel",(transferStock.getRemark()));
                        // 消息体
                        String messageBody = JSONObject.toJSONString(map);
                        return messageBody;
                    }));
            //是否要生成存货迁移记录
            if (Objects.nonNull(migrationSkus)){
                PrestorageStockTransferSkuBO prestorageStockTransferSkuBO = new PrestorageStockTransferSkuBO();
                prestorageStockTransferSkuBO.setWarehouseType(CheckInWhType.FBA.intCode());
                prestorageStockTransferSkuBO.setSku(transferStock.getSku());
                prestorageStockTransferSkuBO.setEmigrationLocationNumber(transferStock.getId().toString());
                prestorageStockTransferSkuBO.setImmigrationLocationNumber(pickLocation);
                prestorageStockTransferSkuBO.setMigrationAmount(upQty);
                prestorageStockTransferSkuBO.setRelevantOrderNo(content);
                migrationSkus.add(prestorageStockTransferSkuBO);
            }
        }
        return waitQty;
    }

    /**
     * 拣货
     *
     * @param taskNo
     * @param domain
     * @param taskType
     * @param stockIdMap
     * @return
     */
    @Override
    public boolean multiplePick(String taskNo,  AndroidProductDo domain,
            Integer taskType, Map<Integer,Integer> stockIdMap) {

        String sku = domain.getSku();
        TransferStockQueryCondition query = new TransferStockQueryCondition();
        query.setIds(new ArrayList<>(stockIdMap.keySet()));
        List<TransferStock> dbStockList = transferStockService.queryTransferStocks(query, null);

        if (CollectionUtils.isEmpty(dbStockList)){
            log.error("TransferStock result is null: " + sku);
            throw new RuntimeException(String.format("sku:%s未查到库存记录", sku));
        }

        dbStockList = dbStockList.stream()
                .sorted(Comparator.comparing(TransferStock::getAllotQuantity))
                .collect(Collectors.toList());

        Integer sumAllotQuantity = dbStockList.stream().mapToInt(i -> i.getAllotQuantity() == null ? 0 : i.getAllotQuantity()).sum();
        Integer pickQuantity = domain.getPickQuantity();
        if ((sumAllotQuantity - pickQuantity) < 0) {
            log.error("orderNo:" + taskNo + ",sku:" + sku + "已分配库存不能为负: allotQuantity[ " + sumAllotQuantity
                    + " ], pickQuantity[ " + pickQuantity + " ]");
            throw new RuntimeException(
                    String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已拣[%s]", sku, sumAllotQuantity, pickQuantity));
        }

        List<WhTransitStockLog> stockLogs = new ArrayList<WhTransitStockLog>();
        List<TransferStock> stockList = new ArrayList<TransferStock>();
        StockLogStep logStep = StockLogStep.FBA_ALLOCATION_PICK;
        if (taskType != null && (taskType.equals(PickingTaskType.ASN_FIRST_SINGLE.intCode())
                || taskType.equals(PickingTaskType.ASN_FIRST_MULTIPLE.intCode())))
            logStep = StockLogStep.ASN_PICK;

        /**
         * 减：已分配 加：已拣库存
         */
        for (TransferStock stock : dbStockList) {

            Integer allotQuantity = stock.getAllotQuantity() == null ? 0 : stock.getAllotQuantity();
            Integer oldPickQuantity = stock.getPickQuantity() == null ? 0 : stock.getPickQuantity();
            Integer updateQuantity = allotQuantity;
            pickQuantity = stockIdMap.get(stock.getId());
            if (pickQuantity <= updateQuantity) {
                updateQuantity = pickQuantity;
            }

            TransferStock updateStock = TransferStock.buildUpdateStock(stock);
            updateStock.setAllotQuantity(allotQuantity - updateQuantity);
            updateStock.setPickQuantity(oldPickQuantity + updateQuantity);

            stockLogs.add(new WhTransitStockLog(stock.getSku(), stock.getStore(), stock.getSite(),
                    TransferStockLogType.ALLOCATED, stock.getId(), stock.getLocationNumber(), logStep, -updateQuantity, allotQuantity, taskNo));
            stockLogs.add(new WhTransitStockLog(stock.getSku(), stock.getStore(), stock.getSite(),
                    TransferStockLogType.PICKED_STOCK, stock.getId(), stock.getLocationNumber(),logStep, updateQuantity, oldPickQuantity, taskNo));
            stockList.add(updateStock);
        }

        transferStockService.batchUpdateTransferStock(stockList);
        // 记录中转仓库存日志
        stockLogService.batchAddWhStockLog(stockLogs);

        return true;
    }

    /**
     * 装箱改库存
     *
     * @param allocation
     */
    @Override
    @StockServicelock
    public void box(WhFbaAllocation allocation) {
        if (allocation == null || CollectionUtils.isEmpty(allocation.buildGroupItems()))
            return;
        List<String> skus = allocation.getItems().stream().map(WhFbaAllocationItem::getProductSku).distinct()
                .collect(Collectors.toList());
        // 获取拣货库存记录
        List<WhApvOutStockChain> whApvOutStockChains = apvGridUpdateStockService.getWhApvOutStockChains(null, skus,
                Arrays.asList(allocation), AssetOrderType.ASN_PREPARE_ORDER.intCode());
        if (CollectionUtils.isEmpty(whApvOutStockChains)) {
            throw new RuntimeException(String.format("sku:%s无库存拣货记录，装箱退库存失败", JSONObject.toJSONString(skus)));
        }

        Map<String, List<WhApvOutStockChain>> apvOutStockMap = whApvOutStockChains.stream()
                .collect(Collectors.groupingBy(WhApvOutStockChain::getSku));

        String orderNo = allocation.getFbaNo();
        Map<String, List<TransferStock>> stockMap = getStocksMap(allocation);
        List<TransferStock> stockList = new ArrayList<TransferStock>();
        List<WhTransitStockLog> stockLogs = new ArrayList<WhTransitStockLog>();/** 库存变更推送 */
        List<AmqMessage> msgList = new ArrayList<>();
        StockLogStep logStep = StockLogStep.FBA_ALLOCATION_BOX;
        if (!allocation.isFba())
            logStep = StockLogStep.ASN_BOX;

        for (WhFbaAllocationItem whAsnItem : allocation.buildGroupItems()) {
            String sku = whAsnItem.getProductSku();
            List<WhApvOutStockChain> apvOutStockChains = apvOutStockMap.get(sku);
            if (CollectionUtils.isEmpty(apvOutStockChains))
                throw new BusinessException("sku: " + sku + "拣货记录为空");
            Collections.sort(apvOutStockChains, (c1, c2) -> c2.getId().compareTo(c1.getId()));
            List<TransferStock> skuStockList = stockMap.get(allocation.getAccountNumber() + whAsnItem.getSite() + sku);
            if (CollectionUtils.isEmpty(skuStockList)) {
                throw new BusinessException("sku: " + sku + "库存记录为空");
            }
            /**
             * 加：已拣返架 减：已拣
             */
            Integer quantity = whAsnItem.getLoadingQuantity() == null ? 0 : whAsnItem.getLoadingQuantity();
            Integer grid = whAsnItem.getGridQuantity() == null ? 0 : whAsnItem.getGridQuantity();
            Integer updateQuantity = grid - quantity;
            Integer skuUpdateQuantity = grid - quantity;
            if (updateQuantity <= 0) {
                continue;
            }

            int pickNum = apvOutStockChains.stream()
                    .mapToInt(s -> s.getPickQuantity() == null ? 0 : s.getPickQuantity()).sum();

            if ((pickNum - updateQuantity) < 0) {
                log.error("orderNo:" + orderNo + ",sku:" + sku + "已拣库存不能为负: pickQuantity[ " + pickNum + " ], quantity[ "
                        + updateQuantity + " ]");
                throw new RuntimeException(
                        String.format("sku:%s已拣库存不能为负: 已拣库存[%s], 扣已拣[%s]", sku, pickNum, updateQuantity));
            }
            Map<Integer, TransferStock> whStockMap = skuStockList.stream()
                    .collect(Collectors.toMap(TransferStock::getId, s -> s));

            whApvOutStockChains.sort(Comparator.comparing(WhApvOutStockChain::getPickDiff).reversed());

            for (WhApvOutStockChain stockChain : apvOutStockChains) {
                if (updateQuantity <= 0)
                    break;
                if (stockChain.getPickQuantity() == null || stockChain.getPickQuantity() == 0)
                    continue;
                Integer stockId = stockChain.getStockId();
                TransferStock whStock = whStockMap.get(stockId);
                if (whStock == null)
                    continue;

                Integer apvPick = stockChain.getPickQuantity();
                Integer updateNum = updateQuantity;
                if (apvPick <= updateQuantity) {
                    updateNum = apvPick;
                }
                Integer pickQuantity = whStock.getPickQuantity() == null ? 0 : whStock.getPickQuantity();
                Integer pickReturnQuantity = whStock.getPickReturnQuantity() == null ? 0
                        : whStock.getPickReturnQuantity();

                if (updateNum > pickQuantity) {
                    log.error("orderNo:" + orderNo + ",sku:" + sku + "已拣库存不能为负: pickQuantity[ " + pickQuantity
                            + " ], pick[ " + updateNum + " ]");
                    throw new RuntimeException(
                            String.format("sku:%s已拣库存不能为负: 已拣库存[%s], 扣已拣[%s]", sku, pickQuantity, updateNum));
                }

                TransferStock updateStock = TransferStock.buildUpdateStock(whStock);

                updateStock.setPickQuantity(pickQuantity - updateNum);
                updateStock.setPickReturnQuantity(pickReturnQuantity + updateNum);
                stockList.add(updateStock);

                stockLogs.add(new WhTransitStockLog(whAsnItem.getProductSku(), whAsnItem.getStore(),
                        whAsnItem.getSite(), TransferStockLogType.PICKED_STOCK, whStock.getId(),
                        whStock.getLocationNumber(), logStep, -updateNum, pickQuantity, orderNo));
                stockLogs.add(new WhTransitStockLog(whAsnItem.getProductSku(), whAsnItem.getStore(),
                        whAsnItem.getSite(), TransferStockLogType.PICKED_RETURN_STOCK, whStock.getId(),
                        whStock.getLocationNumber(), logStep, updateNum, pickReturnQuantity, orderNo));
                updateQuantity -= apvPick;
            }

            // 推送fba库存变更到oms
            OmsFbaSkuMessage omsFbaSkuMessage = new OmsFbaSkuMessage(allocation.getAccountNumber(), whAsnItem.getSite(),
                    sku, skuUpdateQuantity);
            msgList.add(AssembleMessageDataUtils.assembleooFbaStockData(omsFbaSkuMessage));
            // 推送中转仓库存变更到redis
            msgList.add(AssembleMessageDataUtils
                    .assembleDataToFinance(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode(), amqMessage -> {
                        amqMessage.setRelevantParam(sku);
                        Map<String, Object> map = new HashMap<>();
                        map.put("sku", sku);
                        map.put("count_surplus", skuUpdateQuantity);
                        map.put("saleChannel",(allocation.getPurposeHouse()));
                        // 消息体
                        String messageBody = JSONObject.toJSONString(map);
                        return messageBody;
                    }));
        }

        if (CollectionUtils.isNotEmpty(stockList)) {
            transferStockService.batchUpdateTransferStock(stockList);
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            stockLogService.batchAddWhStockLog(stockLogs);
        }
        // 推送库存变更给oms
        if (CollectionUtils.isNotEmpty(msgList)){
            amqMessageService.batchCreateAmqMessage(msgList);
        }
    }

    /**
     * 交运改库存
     *
     * @param allocation
     */
    // 根据已播交运
    @Override
    @StockServicelock
    public void deliverBySelf(WhFbaAllocation allocation) {
        String orderNo = allocation.getFbaNo();
        List<WhApvOutStockChain> whApvOutStockChainList = whApvOutStockChainService.queryWhApvOutStockChains(CommonUtils.splitList(orderNo, ","), null);
        if (CollectionUtils.isEmpty(whApvOutStockChainList))
            throw new BusinessException("orderNo: " + orderNo + "无库存分配记录");
        // 托管，半托管，单品 按拣货数量交运
        boolean bool = allocation.getWhAsnExtra() != null
                && (allocation.isTransfer())
                && (ApvTypeEnum.SM.getCode().equalsIgnoreCase(allocation.getApvType())
                        || ApvTypeEnum.SS.getCode().equalsIgnoreCase(allocation.getApvType()));
        // 为仓发单，则按装箱数量交运，key -> sku,value -> 要扣减的装箱数量
        Map<String, Integer> skuQtyMap;
        if (Boolean.TRUE.equals(allocation.getIsAsn())){
            skuQtyMap = Optional.ofNullable(allocation.getItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku))
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                                                                        .filter(v -> Objects.nonNull(v.getLoadingQuantity()))
                                                                        .mapToInt(WhFbaAllocationItem::getLoadingQuantity)
                                                                        .sum()));
        } else {
            skuQtyMap = new HashMap<>();
        }
        Map<Integer, Integer> pickQtyMap = whApvOutStockChainList.stream().collect(Collectors.toMap(WhApvOutStockChain::getStockId, w -> {
            if (skuQtyMap.containsKey(w.getSku())){
                Integer skuQty = skuQtyMap.get(w.getSku());
                Integer qty = Math.min(w.getPickQuantity(), skuQty);
                skuQtyMap.put(w.getSku(), skuQty - qty);
                return qty;
            }
            if(bool){
                return w.getPickQuantity();
            }
            return w.getGirdQuantity();
        }));
        TransferStockQueryCondition transferStockQueryCondition = new TransferStockQueryCondition();
        transferStockQueryCondition.setIds(pickQtyMap.keySet().stream().collect(Collectors.toList()));
        List<TransferStock> dbStockList = transferStockService.queryTransferStocks(transferStockQueryCondition, null);
        if (CollectionUtils.isEmpty(dbStockList))
            throw new BusinessException("交运对应库存记录不存在");
        List<TransferStock> stockList = new ArrayList<TransferStock>();
        List<WhTransitStockLog> stockLogs = new ArrayList<WhTransitStockLog>();
        StockLogStep logStep = StockLogStep.FBA_ALLOCATION_DELIVER;
        if (!allocation.isFba())
            logStep = StockLogStep.ASN_DELIVER;

        List<String> negativeStock = new ArrayList<>();
        for (TransferStock transferStockDetail:dbStockList){
            Integer pickQty = pickQtyMap.get(transferStockDetail.getId());
            if (pickQty == null || pickQty <= 0){
                continue;
            }
            TransferStock updateStock = TransferStock.buildUpdateStock(transferStockDetail);
            Integer deliverQuantity = transferStockDetail.getFirstDeliverQuantity();
            Integer pickQuantity = transferStockDetail.getPickQuantity();

            updateStock.setFirstDeliverQuantity(deliverQuantity + pickQty);
            updateStock.setPickQuantity(pickQuantity - pickQty);
            stockList.add(updateStock);
            stockLogs.add(
                    new WhTransitStockLog(transferStockDetail.getSku(), transferStockDetail.getStore(), transferStockDetail.getSite(),
                            TransferStockLogType.PICKED_STOCK,transferStockDetail.getId(),transferStockDetail.getLocationNumber(), logStep, -pickQty,
                            pickQuantity, orderNo));

            stockLogs.add(
                    new WhTransitStockLog(transferStockDetail.getSku(), transferStockDetail.getStore(), transferStockDetail.getSite(),
                            TransferStockLogType.FIRST_DELIVER,transferStockDetail.getId(),transferStockDetail.getLocationNumber(),logStep,
                            pickQty, deliverQuantity, orderNo));

            whStockChangeRecordService.generateStockChangeRecord(transferStockDetail.getId(), -pickQty,
                    allocation.getShipmentId(), WhAllocateTypeEnum.FBA, OrderTypeEnum.ORDER, false);
            log.info("中转仓剩余已拣库存:" + updateStock.getPickQuantity()); // TODO liurui 临时添加日志打印
            if (updateStock.getPickQuantity() < 0) {
                String skuMsg = String.format("%s[%s]", transferStockDetail.getSku(), transferStockDetail.getId());
                negativeStock.add(skuMsg);
                log.info("已拣为负SKU:" + skuMsg);
            }
        }

        transferStockService.batchUpdateTransferStock(stockList);
        stockLogService.batchAddWhStockLog(stockLogs);
        if (CollectionUtils.isNotEmpty(negativeStock)) {
            sendStockDingMsg(orderNo, negativeStock);
        }
    }

    // 根据装箱数量交运
    @Override
    @StockServicelock
    public void deliverFBABySelf(WhFbaAllocation allocation) {
        if (allocation == null || CollectionUtils.isEmpty(allocation.buildGroupItems()))
            return;
        if (!allocation.isFba())
            throw new BusinessException(allocation.getFbaNo() + "非FBA单据，交运失败");
        List<String> skus = allocation.getItems().stream().map(WhFbaAllocationItem::getProductSku).distinct()
                .collect(Collectors.toList());
        // 获取拣货库存记录
        List<WhApvOutStockChain> whApvOutStockChains = apvGridUpdateStockService.getWhApvOutStockChains(null, skus,
                Arrays.asList(allocation), AssetOrderType.ASN_PREPARE_ORDER.intCode());
        if (CollectionUtils.isEmpty(whApvOutStockChains)) {
            throw new RuntimeException(String.format("sku:%s无库存拣货记录，交运失败", JSONObject.toJSONString(skus)));
        }

        Map<String, List<WhApvOutStockChain>> apvOutStockMap = whApvOutStockChains.stream()
                .collect(Collectors.groupingBy(WhApvOutStockChain::getSku));

        String orderNo = allocation.getFbaNo();
        String shipmentId = allocation.getShipmentId();
        Map<String, List<TransferStock>> stocksMap = getStockDetailsMap(allocation);
        List<TransferStock> stockList = new ArrayList<>();
        List<WhTransitStockLog> stockLogs = new ArrayList<>();
        StockLogStep logStep = StockLogStep.FBA_ALLOCATION_DELIVER;

        for (WhFbaAllocationItem whAsnItem : allocation.buildGroupItems()) {
            String sku = whAsnItem.getProductSku();
            List<WhApvOutStockChain> apvOutStockChains = apvOutStockMap.get(sku);
            if (CollectionUtils.isEmpty(apvOutStockChains))
                throw new BusinessException("sku: " + sku + "拣货记录为空");
            Collections.sort(apvOutStockChains, (c1, c2) -> c2.getId().compareTo(c1.getId()));
            String key = allocation.isFba()?allocation.getUnprefixedAccountNumber() + sku
                    :allocation.getUnprefixedAccountNumber() + whAsnItem.getSite() + sku;
            List<TransferStock> skuStockList = stocksMap.get(key);
            if (CollectionUtils.isEmpty(skuStockList)) {
                throw new BusinessException("sku: " + sku + "库存记录为空");
            }
            Integer loadingQuantity = whAsnItem.getLoadingQuantity() == null ? 0 : whAsnItem.getLoadingQuantity();
            final Integer loadQty = loadingQuantity;

            Map<Integer, TransferStock> whStockMap = skuStockList.stream()
                    .collect(Collectors.toMap(TransferStock::getId, s -> s));

            whApvOutStockChains.sort(Comparator.comparing(WhApvOutStockChain::getPickDiff).reversed());

            for (WhApvOutStockChain stockChain : apvOutStockChains) {
                if (loadingQuantity <= 0) continue;
                TransferStock whStock = whStockMap.get(stockChain.getStockId());
                if (whStock == null) continue;
                Integer apvPick;
                if ((apvPick=stockChain.getPickQuantity()) == null || apvPick == 0) continue;

                Integer pickQuantity = whStock.getPickQuantity();
                if (pickQuantity == null || pickQuantity <= 0) continue;
                Integer deliverQuantity = whStock.getFirstDeliverQuantity() == null ? 0 : whStock.getFirstDeliverQuantity();

                Integer modifyQty;
                if (pickQuantity >= loadingQuantity) {
                    modifyQty = loadingQuantity;
                    loadingQuantity = 0;
                } else {
                    modifyQty = pickQuantity;
                    loadingQuantity -= pickQuantity;
                }

                TransferStock updateStock = TransferStock.buildUpdateStock(whStock);
                updateStock.setPickQuantity(pickQuantity - modifyQty);
                updateStock.setFirstDeliverQuantity(deliverQuantity + modifyQty);
                stockList.add(updateStock);
                stockLogs.add(
                        new WhTransitStockLog(whStock.getSku(), whStock.getStore(), whStock.getSite(),
                                TransferStockLogType.PICKED_STOCK,whStock.getId(),whStock.getLocationNumber(), logStep, -modifyQty,
                                pickQuantity, String.format("%s[%s]", orderNo, shipmentId)));

                stockLogs.add(
                        new WhTransitStockLog(whStock.getSku(), whStock.getStore(), whStock.getSite(),
                                TransferStockLogType.FIRST_DELIVER,whStock.getId(),whStock.getLocationNumber(),logStep,
                                modifyQty, deliverQuantity, String.format("%s[%s]", orderNo, shipmentId)));

                whStockChangeRecordService.generateStockChangeRecord(whStock.getId(), -modifyQty,
                        allocation.getShipmentId(), WhAllocateTypeEnum.FBA, OrderTypeEnum.ORDER, false);
            }
            if (loadingQuantity > 0) {
                log.error("orderNo:" + orderNo + ",shipmentId:" + shipmentId + ",sku:" + sku
                        + "已拣库存少于交运装箱数量: deliverQuantity[ " + loadQty + " ]");
                throw new RuntimeException("sku:" + sku + "已拣库存少于交运装箱数量: deliverQuantity[ " + loadQty + " ]");
            }
        }
        transferStockService.batchUpdateTransferStock(stockList);
        stockLogService.batchAddWhStockLog(stockLogs);
    }

    private void sendStockDingMsg(String orderNo, List<String> skus){
        if (CollectionUtils.isEmpty(skus)) {
            return;
        }
        try {
            String property = env.getProperty("spring.profiles.active");
            StringBuffer subStr = new StringBuffer();
            subStr.append("内容：").append("中转仓已拣库存为负").append(",\n")
                    .append("环境：").append((StringUtils.contains(property, "prod") ? "正式" : "测试")).append(",\n")
                    .append("单号：").append(orderNo).append(",\n")
                    .append("SKU：").append(StringUtils.join(skus, ","));
            log.info(subStr.toString());
            if (StringUtils.contains(property, "prod")) {
                DingTalkSendUtils.sendSkuSaleStatisticRecordMsg(subStr.toString());
            }
        } catch (Exception e) {
            log.error("sendStockDingMsg error:" + e.getMessage(), e);
        }
    }

    /**
     * 捞取库存
     *
     * @param allocation
     * @return
     */
    public Map<String,  List<TransferStock>> getStocksMap(WhFbaAllocation allocation) {
        // 库存集合Map 过滤掉库存为空，或者虚拟库位的sku
        return getTransferStockDetails(allocation).stream().filter(s -> StringUtils.isNotBlank(s.getLocationNumber())
                && !ArrayUtils.contains(Constant.locations, s.getLocationNumber())).collect(Collectors.groupingBy(t -> t.getStore() + t.getSite() + t.getSku()));
    }

    /**
     * 捞取库存明细
     *
     * @param allocation
     * @return
     */
    public Map<String,  List<TransferStock>> getStockDetailsMap(WhFbaAllocation allocation) {
        if (allocation.isFba()){
            return getTransferStockDetails(allocation).stream().filter(s -> StringUtils.isNotBlank(s.getLocationNumber())
                    && !ArrayUtils.contains(Constant.locations, s.getLocationNumber())).collect(Collectors.groupingBy(t -> t.getStore()  + t.getSku()));
        }
        // 库存集合Map 过滤掉库存为空，或者虚拟库位的sku
        return getTransferStockDetails(allocation).stream().filter(s -> StringUtils.isNotBlank(s.getLocationNumber())
                && !ArrayUtils.contains(Constant.locations, s.getLocationNumber())).collect(Collectors.groupingBy(t -> t.getStore() + t.getSite() + t.getSku()));
    }

    /**
     * 捞取库存
     *
     * @param allocation
     * @return
     */
    @Override
    public Map<String, TransferStock> getStockMap(WhFbaAllocation allocation) {

        List<String> skus = allocation.getItems().stream().map(item -> item.getProductSku()).distinct()
                .collect(Collectors.toList());
        TransferStockQueryCondition query = new TransferStockQueryCondition();
        query.setSkuList(skus);
        // 非亚马逊的库存需要根据站点查询
        if (!allocation.isFba()){
            List<String> siteList = allocation.getItems().stream().map(item -> item.getSite()).distinct()
                    .collect(Collectors.toList());
            query.setSiteList(siteList);
        }
        query.setStore(allocation.getUnprefixedAccountNumber());
        List<TransferStock> dbStockList = transferStockService.queryTransferStocks(query, null);
        if (CollectionUtils.isEmpty(dbStockList)) {
            return null;
        }

        if (allocation.isFba()){
            return dbStockList.stream()
                    .collect(Collectors.toMap(t->t.getStore()+t.getSku(), t -> t, (t1, t2) -> t2));
        }
        return dbStockList.stream()
                    .collect(Collectors.toMap(t->t.getStore()+t.getSite()+t.getSku(), t -> t, (t1, t2) -> t2));
    }

    //查询库存明细
    private List<TransferStock> getTransferStockDetails(WhFbaAllocation allocation) {
        List<String> skus = allocation.getItems().stream().map(item -> item.getProductSku()).distinct()
                .collect(Collectors.toList());
        TransferStockQueryCondition query = new TransferStockQueryCondition();
        query.setSkuList(skus);
        // 非亚马逊的库存需要根据站点查询
        if (!allocation.isFba()){
            List<String> siteList = allocation.getItems().stream().map(item -> item.getSite()).distinct()
                    .collect(Collectors.toList());
            query.setSiteList(siteList);
        }
        query.setStore(allocation.getUnprefixedAccountNumber());
        query.setQueryLastUpTime(true);
        List<TransferStock> dbStockList = transferStockService.queryTransferStocks(query, null);
        if (CollectionUtils.isEmpty(dbStockList)) {
            return Collections.EMPTY_LIST;
        }
        return dbStockList;
    }

}
