package com.estone.transfer.action;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.estone.apv.domain.GpsrTagDo;
import com.estone.picking.enums.PickingTaskStatus;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.sku.service.*;
import com.estone.temu.bean.TemuPackageInfo;
import com.estone.temu.bean.TemuPrepareOrderQueryCondition;
import com.estone.temu.bean.dto.TemuForPackDto;
import com.estone.temu.domain.PddGridDo;
import com.estone.temu.enums.TemuPackageStatus;
import com.estone.temu.service.TemuPrepareOrderService;
import com.estone.temu.service.TemuTaskService;
import com.estone.transfer.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.apv.bean.PackExceptionUuidItem;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.domain.WhApvGoodsDo;
import com.estone.apv.enums.PackExceptionTypeEnum;
import com.estone.apv.service.PackExceptionUuidItemService;
import com.estone.apv.service.WhApvService;
import com.estone.apv.util.ApvPackUtils;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.common.CacheName;
import com.estone.common.SaleChannel;
import com.estone.common.upload.SeaweedFSUtils;
import com.estone.common.util.*;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskItem;
import com.estone.picking.bean.WhPickingTaskItemQueryCondition;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.dao.WhPickingTaskDao;
import com.estone.picking.dao.WhPickingTaskItemDao;
import com.estone.picking.enums.PickTaskGridStatus;
import com.estone.picking.enums.PickingTaskType;
import com.estone.sku.bean.WhUniqueApvGrid;
import com.estone.sku.bean.WhUniqueApvGridQueryCondition;
import com.estone.sku.bean.WhUniqueSku;
import com.estone.sku.bean.WhUniqueSkuLog;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.domain.WhFbaAllocationDo;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.enums.JITBoxOrderType;
import com.estone.transfer.enums.ProductType;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;

import jodd.util.ArraysUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 中转仓包装
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
@RequestMapping(value = "fba/packs")
public class WhFbaPackController {

    private static Logger logger = LoggerFactory.getLogger(WhFbaPackController.class);

    private static final String SHEIN_PDFURL = "/fba/shein/pdfUrl/";

    @Resource
    private JitPickBoxScanService jitPickBoxScanService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhUniqueSkuLogService whUniqueSkuLogService;

    @Resource
    private PackExceptionUuidItemService packExceptionUuidItemService;

    @Resource
    private WhUniqueApvGridService whUniqueApvGridService;

    @Resource
    private WhPickingTaskDao whPickingTaskDao;

    @Resource
    private WhPickingTaskItemDao whPickingTaskItemDao;

    @Resource
    private WhFbaAllocationHandleService whFbaAllocationHandleService;

    @Resource
    private WhApvService whApvService;
    @Resource
    private JitPickupOrderService jitPickupOrderService;

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private SkuGpsrWaringService skuGpsrWaringService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private TemuTaskService temuTaskService;

    @Resource
    private TemuPrepareOrderService temuPrepareOrderService;

    @Resource
    private WhPickingTaskService whPickingTaskService;

    private static final String GPSR_TAG_PRINT = "pack/gpsr_tag_print";

    /**
     * PDF
     * shein/pdd的相关标签仓储到redis
     */
    @RequestMapping(value = "uploadPdf", method = {RequestMethod.POST})
    @ResponseBody
    public void handleFileUpload(@RequestParam("pdfFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
           return;
        }
        byte[] bytes = file.getBytes();
        // 在这里你可以对文件进行各种操作，比如转换为Base64字符串
        String base64PDF = Base64.getEncoder().encodeToString(bytes);

        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new IOException("pdf名字未定义");
        }
        int indexOf = fileName.lastIndexOf(".");

        String name = indexOf!=-1?fileName.substring(0, indexOf):fileName;

        String enName = ProductType.getCodeByName(name);

        //上传pdf到文件系统
        String pdfUrl = PdfUtils.uploadPDFFile(SeaweedFSUtils.getJoinUrl(null), SHEIN_PDFURL,name+".pdf", bytes);

        StringRedisUtils.set(RedisConstant.TRANSFER_SHEIN_PDF_URL_KEY+enName,pdfUrl);

        StringRedisUtils.set(RedisConstant.TRANSFER_SHEIN_KEY+enName,base64PDF);

    }





    /**
     * 单品单件包装页面
     *
     * @param domain
     * @param waybillSize
     * @return
     */
    @RequestMapping(value = "ss/init", method = { RequestMethod.GET })
    public String ssInit(@ModelAttribute("domain") WhApvGoodsDo domain,
            @RequestParam(value = "waybillSize", required = false) String waybillSize) {

        // 面单尺寸
        if (StringUtils.isNotBlank(waybillSize)) {
            domain.setWaybillSize(waybillSize);
        }
        else {
            domain.setWaybillSize("1"); // 默认100 *100
        }

        return "pack/ss_fba_init";
    }


    /**
     * 中转仓扫描 周转框 或者拣货任务号
     *
     * @param pickType 页面传入拣货类型判断
     * @param whApvDo
     * @return
     */
    @RequestMapping(value = "box/scan", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson ssBoxScan(@ModelAttribute("domain") WhApvGoodsDo whApvDo, @RequestParam("box") String box,
                                  @RequestParam("pickType") Integer pickType) {

        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equals(rsp.getStatus(), StatusCode.FAIL)) {
            return rsp;
        }
        String taskNo = rsp.getMessage();
        if (StringUtils.isEmpty(taskNo) || StringUtils.isEmpty(rsp.getLocation())) {
            rsp.setMessage("拣货任务号 或者拣货类型为空！");
            return rsp;
        }
        if (StringUtils.isEmpty(rsp.getLocation()) || !rsp.getLocation().equals(pickType + "")
                && !PickingTaskType.ASN_FIRST.getCode().equals(rsp.getLocation())
                && !PickingTaskType.ASN_FIRST_SINGLE.getCode().equals(rsp.getLocation())
                && !PickingTaskType.ASN_FIRST_MULTIPLE.getCode().equals(rsp.getLocation())
                && !PickingTaskType.JIT_ASN_SINGLESINGLE.getCode().equals(rsp.getLocation())
                && !PickingTaskType.TRANSFER_BZCY.getCode().equals(rsp.getLocation())
                && !PickingTaskType.TRANSFER_BZYC.getCode().equals(rsp.getLocation())) {
            rsp.setMessage("拣货类型不匹配！");
            rsp.setStatus(StatusCode.FAIL);
            return rsp;
        }

        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setTaskNo(taskNo);
        if (PickingTaskType.TRANSFER_SINGLESINGLE.intCode().equals(pickType)){
            queryCondition.setApvType("SS");
        }
        if (PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode().equals(pickType)){
            queryCondition.setApvType("SM");
        }
        if (PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(pickType)){
            queryCondition.setApvType("MM");
        }
        queryCondition.setStatusList(Arrays.asList(AsnPrepareStatus.CHECK_PRINT.intCode(),AsnPrepareStatus.PICK_STOCK_OUT.intCode()));
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocations(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocations)) {
            // 中转仓单件、单品包含拼多多包装
            if (PickingTaskType.TRANSFER_SINGLESINGLE.getCode().equals(rsp.getLocation())
                    || PickingTaskType.TRANSFER_SINGLEMULTIPLE.getCode().equals(rsp.getLocation())
                    || PickingTaskType.ASN_FIRST_SINGLE.getCode().equals(rsp.getLocation())
                    || PickingTaskType.ASN_FIRST_MULTIPLE.getCode().equals(rsp.getLocation())
                    || PickingTaskType.TRANSFER_BZCY.getCode().equals(rsp.getLocation())
                    || PickingTaskType.TRANSFER_BZYC.getCode().equals(rsp.getLocation())){
                TemuPrepareOrderQueryCondition temuQuery = new TemuPrepareOrderQueryCondition();
                temuQuery.setTaskNo(taskNo);
                temuQuery.setApvType(queryCondition.getApvType());
                temuQuery.setPackageStatus(TemuPackageStatus.WAITING_GRID.intCode());
                List<TemuPackageInfo> packageInfoList = temuPrepareOrderService.queryTemuPackageAndItems(temuQuery, null);
                if (CollectionUtils.isEmpty(packageInfoList)) {
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage("没有可用的发货单");
                    return rsp;
                }
                List<Integer> lessPick = packageInfoList.stream()
                        .filter(i -> i.getPrepareQuantity() != null && i.getPrepareQuantity() > 0
                                && (i.getPickQuantity() == null || i.getPickQuantity() == 0))
                        .map(TemuPackageInfo::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lessPick)) {
                    rsp.setLocation(StringUtils.join(lessPick, ","));
                } else {
                    rsp.setLocation(null);
                }
            } else {
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage("没有可用的发货单");
                return rsp;
            }
        } else {
            rsp.setLocation(null);
        }
        // 将拣货任务号设置进去，扫描sku 统一拣货任务号 查询
        rsp.setMessage(taskNo);
        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    /**
     * 单品单件扫描sku 匹配发货单
     *
     * @param domain
     * @param sku
     * @param waybillSize
     * @return
     */
    @RequestMapping(value = "ss/scan/sku", method = { RequestMethod.GET })
    public String ssScanSku(@ModelAttribute("domain") WhApvGoodsDo domain, @RequestParam("sku") String sku,
            @RequestParam(value = "waybillSize", required = false) String waybillSize,
            @RequestParam(value = "scanTaskNo", required = false) String scanTaskNo,
            @RequestParam("uuid") String uuid) {
        domain.getQuery().setSku(uuid);
        String erroeMes = whUniqueSkuService.checkScanUniqueSku(uuid, UniqueSkuStep.PACKING.intCode());
        if (StringUtils.isNotBlank(erroeMes)) {
            if(erroeMes.contains("唯一码已绑定发货单")){
                String scanPage = "";
                if(StringUtils.isNotBlank(waybillSize) ){
                    if("2".equals(waybillSize)){
                        scanPage = "单件包装（150）";
                    }else{
                        scanPage = "单件包装（100）";
                    }
                }
                this.savePackExceptionUuidItem(uuid, scanTaskNo, scanPage, erroeMes, PackExceptionTypeEnum.REPEAT_SCAN);
            }
            domain.setErrorMsg(erroeMes);
            return "pack/ss_fba_sacn_sku";
        }
        if (StringUtils.isNotBlank(sku)) {
            try {
                //兼容SKU编码和唯一码
                sku = CompatibleSkuUtils.getSku(sku);

                if (!RedissonLockUtil.tryLock(RedisKeys.getPackSkuCacheKey(sku), 5, 90)) {
                    erroeMes = "单件包装抢单失败，请稍后操作";
                    domain.setErrorMsg(erroeMes);
                    return "pack/ss_sacn_sku";
                }
                WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
                queryCondition.setSku(sku);
                queryCondition.setTaskNo(scanTaskNo);
                queryCondition.setStatusList(Arrays.asList(AsnPrepareStatus.CHECK_PRINT.intCode()));
                queryCondition.setApvType("SS");
                queryCondition.setPackPriority(true);
                WhFbaAllocation whFbaAllocation=whFbaAllocationService.scanMaxPriorityGoodsPrintFba(queryCondition,domain);
                if (whFbaAllocation == null) {
                    queryCondition.setApvType("SS");
                    queryCondition.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
                    try {
                        whFbaAllocation = this.scanMaxPriorityGoodsPrintTemu(queryCondition, domain);
                    } catch (Exception e) {
                        if (StringUtils.isNotBlank(domain.getErrorMsg())) {
                            return "pack/ss_fba_sacn_sku";
                        }
                        throw new RuntimeException(e.getMessage());
                    }
                    if (StringUtils.isNotBlank(scanTaskNo) || StringUtils.isNotBlank(whFbaAllocation.getFbaNo())) {
                        WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
                        taskQuery.setApvNo(whFbaAllocation.getFbaNo());
                        taskQuery.setTaskNo(scanTaskNo);
                        taskQuery.setTaskStatus(PickingTaskStatus.COMPLETED.intCode());
                        List<WhPickingTask> whPickingTaskList = whPickingTaskService.queryWhPickingTasks(taskQuery,null);
                        if (CollectionUtils.isEmpty(whPickingTaskList)) {
                            throw new RuntimeException("该发货单没有对应拣货任务！");
                        }
                        whPickingTaskList.sort(Comparator.comparingInt(WhPickingTask::getId).reversed());
                        domain.setTaskNo(whPickingTaskList.get(0).getTaskNo());
                        domain.setTaskType(whPickingTaskList.get(0).getTaskType());
                    }
                }
                domain.setWhFbaAllocation(whFbaAllocation);
            }catch (Exception e){
                log.error("中转仓单品包装扫描异常"+e.getMessage(),e);
                domain.setErrorMsg("中转仓单品包装扫描异常"+e.getMessage());
                return "pack/ss_fba_sacn_sku";
            }
            finally {
                RedissonLockUtil.unlock(RedisKeys.getPackSkuCacheKey(sku));
            }
        }

        return "pack/ss_fba_sacn_sku";
    }

    /**
     * 查询temu待包装
     */
    private WhFbaAllocation scanMaxPriorityGoodsPrintTemu(WhFbaAllocationQueryCondition fbaQuery, WhApvGoodsDo apvGoodsDo){
        TemuPrepareOrderQueryCondition temuQuery = new TemuPrepareOrderQueryCondition();
        temuQuery.setTaskNo(fbaQuery.getTaskNo());
        temuQuery.setSku(fbaQuery.getSku());
        temuQuery.setApvType(fbaQuery.getApvType());
        temuQuery.setTaskType(fbaQuery.getTaskType());
        temuQuery.setPackageStatusList(Collections.singletonList(TemuPackageStatus.WAITING_GRID.intCode()));
        PddGridDo pddGridDo = new PddGridDo();
        TemuForPackDto temuForPackDto = temuTaskService.scanMaxPriorityGoodsPrintTemu(temuQuery, pddGridDo);
        if (temuForPackDto == null) {
            throw new RuntimeException(String.format("扫描sku:%s,没有查询到中转仓发货单号",fbaQuery.getSku()));
        }
        if(StringUtils.equalsIgnoreCase("SS", fbaQuery.getApvType())) {
            if (temuForPackDto.getPickQuantity() == null || temuForPackDto.getPickQuantity() == 0) {
                throw new RuntimeException(String.format("扫描sku:%s,TEMU发货单拣货为0 状态错误",fbaQuery.getSku()));
            }
        }
        WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
        whFbaAllocation.setId(temuForPackDto.getId());
        whFbaAllocation.setFbaNo(temuForPackDto.getPackageSn());
        whFbaAllocation.setPurposeHouse(SaleChannel.CHANNEL_TEMU);
        whFbaAllocation.setIsAsn(false);
        WhFbaAllocationItem item = new WhFbaAllocationItem();
        item.setProductSku(temuForPackDto.getSku());
        item.setQuantity(temuForPackDto.getRealQuantity());
        item.setPickQuantity(temuForPackDto.getPickQuantity());
        item.setGridQuantity(Optional.ofNullable(temuForPackDto.getGridQuantity()).orElse(0));
        item.setWhSku(temuForPackDto.getWhSku());
        whFbaAllocation.setItems(List.of(item));
        apvGoodsDo.setSkuTagMap(pddGridDo.getSkuTagMap());
        apvGoodsDo.setWhSkuWithPmsInfos(pddGridDo.getWhSkuWithPmsInfos());
        return whFbaAllocation;
    }

    /**
     * 撤销打过标签的订单
     *
     * @param apvId
     * @return
     * @return String
     */
    @RequestMapping(value = "revoke", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson revokePrint(@ModelAttribute("domain") WhApvGoodsDo domain, @RequestParam("apvId") Integer apvId,
                                    @RequestParam(value = "apvNo", required = false) String apvNo) {
        ResponseJson response = new ResponseJson();

        // 撤销回按货打单状态
        whFbaAllocationService.revokeScanedWhApvByStatus(apvId, AsnPrepareStatus.CHECK_PRINT.intCode());

        SystemLogUtils.FBAALLOCATIONLOG.log(apvId, "发货单状态变更  撤回扫描",
                new String[][] { { "历史状态", AsnPrepareStatus.WAITING_DELIVER.getName() },
                        { "更改状态",AsnPrepareStatus.CHECK_PRINT.getName()} });
        whUniqueSkuService.revokePacking(apvNo);
        return response;
    }

    //打印JIT标签
    @RequestMapping(value ="printJitTag", method = {RequestMethod.GET})
    public String printJitTag(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("apvNo") String apvNo,
            @RequestParam(value = "skuBarcode", required = false) String skuBarcode, @RequestParam("sku") String sku) {
        if (StringUtils.isBlank(sku) || StringUtils.isBlank(apvNo)) {
            domain.setErrorMsg("参数错误!");
            return "pack/sacn_print_tag";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(apvNo);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            domain.setErrorMsg(String.format("发货单号:%s,不存在",apvNo));
            return "pack/sacn_print_tag";
        }
        List<WhFbaAllocationItem> items = allocationList.get(0).getItems().stream().filter(i->sku.equals(i.getProductSku())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s 不存在",apvNo,sku));
            return "pack/sacn_print_tag";
        }
        if (StringUtils.isNotBlank(skuBarcode)){
            items = items.stream().filter(i -> skuBarcode.equals(i.getSkuBarcode())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(items)) {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s 不存在",apvNo,sku));
            return "pack/sacn_print_tag";
        }


        //JIT备货单
        WhAsnExtra whAsnExtra = allocationList.get(0).getWhAsnExtra();
        if (whAsnExtra==null) {
            domain.setErrorMsg(String.format("发货单号:%s,WhAsnExtra不存在关联数据",apvNo));
            return "pack/sacn_print_tag";
        }

        if (StringUtils.isBlank(items.get(0).getSkuBarcode()) && AsnPackageMethodEnum.JIT_HALF.getCode().equals(whAsnExtra.getPackageMethod())) {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s sku条码信息不存在",apvNo,sku));
            return "pack/sacn_print_tag";
        }

        try {
            boolean jitAsn = allocationList.get(0).getIsAsn() != null && allocationList.get(0).getIsAsn();
            Integer[] packageMethods = new Integer[] { AsnPackageMethodEnum.JIT_HALF.getCode() };
            if (ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod()) || jitAsn) {
                Integer quantity = Optional.ofNullable(items.get(0).getGridQuantity())
                        .orElse(Optional.ofNullable(items.get(0).getPickQuantity()).orElse(0));
                if (jitAsn) {
                    if (items.get(0).getSuitFlag() != null && items.get(0).getSuitFlag() == 1) {
                        quantity = Optional.ofNullable(items.get(0).getSkuSuitNum()).orElse(1);
                    }
                    return getJitAsnPdfUrl(domain, allocationList.get(0), quantity, allocationList.get(0).getAllotNum(),
                            items.get(0), sku);
                }
                else {
                    return getJitPdfUrl(domain, apvNo, quantity, allocationList.get(0).getAllotNum(),
                            items.get(0).getSkuBarcode(), sku);
                }
            }
        }
        catch (Exception e) {
            log.error("打印jit标签异常" + e.getMessage(), e);
            domain.setErrorMsg("打印jit标签异常" + e.getMessage());
            return "pack/sacn_print_tag";

        }
        return "";
    }

    //打印JIT标签
    @RequestMapping(value = "printLocalJitTag", method = { RequestMethod.GET })
    public String printLocalJitTag(@ModelAttribute("domain") WhFbaAllocationDo domain,
            @RequestParam("apvNo") String apvNo,
            @RequestParam(value = "skuBarcode", required = false) String skuBarcode,
            @RequestParam(value = "sku", required = false) String sku) {
        if (StringUtils.isBlank(apvNo)) {
            domain.setErrorMsg("参数错误!");
            return "pack/sacn_print_tag";
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(apvNo);
        List<WhApv> allocationList = whApvService.queryWhApvAndItemList(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            domain.setErrorMsg(String.format("发货单号:%s,不存在",apvNo));
            return "pack/sacn_print_tag";
        }
        WhApv apv = allocationList.get(0);

        if (StringUtils.isBlank(sku)){
            String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + apvNo + ".pdf";
            File file = new File(mergePdfPath);
            if (file.exists()) {
                domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static/"));
                return "pack/sacn_print_tag";
            }
        }


        ApvPackUtils.buildZhSku(apv);
        List<WhApvItem> items = apv.getWhApvItems().stream().filter(i-> StringUtils.equals(i.getSku(),sku)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s 不存在",apvNo,sku));
            return "pack/sacn_print_tag";
        }
        if (StringUtils.isNotBlank(skuBarcode)){
            items = items.stream().filter(i -> skuBarcode.equals(i.getSkuBarcode())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(items)) {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s 不存在",apvNo,sku));
            return "pack/sacn_print_tag";
        }


        if (StringUtils.isBlank(items.get(0).getSkuBarcode())) {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s sku条码信息不存在",apvNo,sku));
            return "pack/sacn_print_tag";
        }

        try {
            Integer apvAllotNum = apv.getWhApvItems().stream().map(WhApvItem::getSaleQuantity).reduce(0, Integer::sum);
            Integer quantity = Optional.ofNullable(items.get(0).getSaleQuantity()).orElse(0);
            return getJitPdfUrl(domain, apvNo, quantity, apvAllotNum, items.get(0).getSkuBarcode(), sku);
        }
        catch (Exception e) {
            log.error("打印jit标签异常" + e.getMessage(), e);
            domain.setErrorMsg("打印jit标签异常" + e.getMessage());
            return "pack/sacn_print_tag";

        }
    }


    private String getJitPdfUrl(WhFbaAllocationDo domain, String apvNo, Integer pickQuantity, Integer allotNum,
            String skuBarCode, String sku) throws Exception {
        String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + apvNo + ".pdf";
        File file = new File(mergePdfPath);
        Integer quantity = Optional.ofNullable(pickQuantity).orElse(0);
        domain.setPrintCopies(quantity);
        if (!file.exists()) {
            String jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(apvNo, 1, null, null);
            if (StringUtils.isNotBlank(jitPdfUrl)) {
                PdfUtils.parseAndSplitToSave(jitPdfUrl, apvNo, allotNum);
            }
        }
        String skuMergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + apvNo + "_" + skuBarCode + ".pdf";

        File skuFile = new File(skuMergePdfPath);
        //避免单品单件解析失败，查询一个未拆分的pdf
        if (!skuFile.exists() && allotNum != null && allotNum == 1) {
            skuMergePdfPath = mergePdfPath;
            skuFile = file;
        }

        if (skuFile.exists()) {
            domain.setPrintUrl(StringUtils.substringAfterLast(skuMergePdfPath, "static/"));
        }
        else {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s sku条码标签不存在", apvNo, sku));
        }
        return "pack/sacn_print_tag";
    }

    private String getJitAsnPdfUrl(WhFbaAllocationDo domain, WhFbaAllocation allocation, Integer pickQuantity, Integer allotNum,
                                WhFbaAllocationItem item, String sku) throws Exception {
        String apvNo = allocation.getFbaNo();
        String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + allocation.getFbaNo() + ".pdf";
        File file = new File(mergePdfPath);
        Integer quantity = Optional.ofNullable(pickQuantity).orElse(0);
        domain.setPrintCopies(quantity);

        String skuMergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + apvNo + "_" + item.getSkuBarcode() + ".pdf";
        File skuFile = new File(skuMergePdfPath);
        if (!skuFile.exists() && item.getScItemId() != null) {
            skuMergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + apvNo + "_" + item.getScItemId() + ".pdf";
            skuFile = new File(skuMergePdfPath);
        }
        // 避免单品单件解析失败，查询一个未拆分的pdf
        if (!skuFile.exists()) {
            try {
                String scItemId = item.getScItemId() == null?item.getProductBarcode():item.getScItemId().toString();
                String url = jitPickupOrderService.tagPrint(allocation.getAccountNumber(),
                        allocation.getWhAsnExtra().getBizType(), scItemId);
                PdfUtils.convertPdfFromHttpToPdf(url, skuMergePdfPath);
                skuFile = new File(skuMergePdfPath);
            }
            catch (Exception ex) {
                log.error("打印jit标签异常" + ex.getMessage(), ex);
            }
        }
        //避免单品单件解析失败，查询一个未拆分的pdf
        if (!skuFile.exists() && allotNum != null && allotNum == 1) {
            skuMergePdfPath = mergePdfPath;
            skuFile = file;
        }
        if (skuFile.exists()) {
            domain.setPrintUrl(StringUtils.substringAfterLast(skuMergePdfPath, "static/"));
        }
        else {
            domain.setErrorMsg(String.format("发货单号:%s,sku:%s sku条码标签不存在", apvNo, sku));
        }
        return "pack/sacn_print_tag";
    }

    //打印其它标签
    @RequestMapping(value = "printTag", method = {RequestMethod.GET})
    public String printHeavyTag(@ModelAttribute("domain") WhFbaAllocationDo domain,
                                @RequestParam("id") Integer id,
                                @RequestParam(name = "skuBarcode",required = false) String skuBarcode,
                                @RequestParam(name = "sku", required = false)String sku) {
        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "pack/sacn_print_tag";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            WhFbaAllocation whFbaAllocation = allocationList.get(0);
            if (CollectionUtils.isEmpty(allocationList) || whFbaAllocation == null) {
                domain.setErrorMsg("没有sku面单!");
                return "pack/sacn_print_tag";
            }
            List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
            //JIT备货单
            WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
            if (whAsnExtra==null) {
                domain.setErrorMsg("没有sku面单!");
                return "pack/sacn_print_tag";
            }
            if (AsnPackageMethodEnum.JIT_HALF.getCode().equals(whAsnExtra.getPackageMethod())) {
                return "";
            }
            List<String> pdfUrlList=new ArrayList<>();
            List<String> base64List=new ArrayList<>();
            List<String> base64SkuList=new ArrayList<>();
            for (WhFbaAllocationItem item : items) {
                //shein且sku不为空值的单，就只筛选出其对应sku进行打印
                if (whFbaAllocation.isShein() && StringUtils.isNotBlank(sku)
                        && (StringUtils.isBlank(skuBarcode)
                                || StringUtils.equalsIgnoreCase(item.getSkuBarcode(), skuBarcode))
                        && !Objects.equals(sku, item.getProductSku())) {
                    continue;
                }
                Integer[] packageMethods = new Integer[]{AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()};
                if (ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod())) {
                    Integer orderType = null;
                    if (AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                        orderType = 2;
                    }
                    String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + ".pdf";
                    if (!AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                        File file = new File(mergePdfPath);
                        if (file.exists()) {
                            domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static/"));
                            return "pack/sacn_print_tag";
                        }
                    }
                    String jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(allocationList.get(0).getFbaNo(), 1, orderType, null);
                    if (StringUtils.isNotBlank(jitPdfUrl) && !AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()
                            .equals(whAsnExtra.getPackageMethod())) {
                        PdfUtils.parseAndSplitToSave(jitPdfUrl, allocationList.get(0).getFbaNo(), allocationList.get(0).getAllotNum());
                        domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static/"));
                    }
                    else {
                        whAsnExtra.setPdfUrlList(Collections.singletonList(jitPdfUrl));
                        whAsnExtra.setBase64List(Collections.singletonList(convertPdfFromHttpToBase64(jitPdfUrl)));
                        domain.setWhAsnExtra(whAsnExtra);
                    }
                    return "pack/sacn_print_tag";
                }
                String tagSplit = item.getTag();
                if (StringUtils.isBlank(tagSplit)) {
                    continue;
                }
                Integer pickQuantity=Optional.ofNullable(item.getGridQuantity()).orElse(Optional.ofNullable(item.getPickQuantity()).orElse(0));
                if (pickQuantity<=0){
                    continue;
                }

                //shein的订单，包装打印的sku标签要为合并环保标后的
                boolean mergeResult = false;
                Set<String> mergeTagCode = Arrays.asList(ProductType.BOX,ProductType.BAG,ProductType.PREVENT_SUFFOCATION)
                        .stream()
                        .map(ProductType::getCode)
                        .collect(Collectors.toSet());
                if(whFbaAllocation.isShein()){
                    List<String> tagList = CommonUtils.splitList(tagSplit,",").stream().filter(tag -> mergeTagCode.contains(tag)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tagList)){
                        String skuCodePdfString =  convertPdfFromHttpToBase64(item.getTemuCodeUrl());
                        base64SkuList.add(skuCodePdfString);
                        for (String tag : tagList){
                            String pdfString = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_KEY + tag);
                            base64SkuList.add(pdfString);
                        }
                        mergeResult = true;
                    }
                }
                for (Integer i = 0; i < pickQuantity; i++) {
                    if(whFbaAllocation.isShein()){
                        if (!mergeResult){
                            base64List.add(convertPdfFromHttpToBase64(item.getTemuCodeUrl()));
                        }
                    }else {
                        base64List.add(convertPdfFromHttpToBase64(whAsnExtra.getPrintSkuTagUrl()));
                    }
                }
                List<String> tagList = Arrays.asList(tagSplit.split(","));
                for (String tag : tagList) {
                    String url = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_PDF_URL_KEY + tag);
                    if (StringUtils.isNotBlank(url)) {
                        pdfUrlList.add(url);
                    }
                    if (whFbaAllocation.isShein() && mergeResult && mergeTagCode.contains(tag)){
                        continue;
                    }
                    String base64 = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_KEY + tag);
                    if (StringUtils.isNotBlank(base64)) {
                        for (Integer i = 0; i < pickQuantity; i++) {
                            base64List.add(base64);
                        }
                    }
                }
                if(whFbaAllocation.isShein()) {
                    pdfUrlList.add(item.getTemuCodeUrl());
                    domain.setPrintCopies(pickQuantity);
                }
            }

            if(!whFbaAllocation.isShein()){
                pdfUrlList.add(whAsnExtra.getPrintSkuTagUrl());
            }
            whAsnExtra.setBase64List(base64List);
            whAsnExtra.setBase64SkuList(base64SkuList);
            whAsnExtra.setPdfUrlList(pdfUrlList);
            domain.setWhAsnExtra(whAsnExtra);
            domain.setWhFbaAllocation(whFbaAllocation);
            return "pack/sacn_print_tag";
        } catch (Exception e) {
            domain.setErrorMsg(String.format("调用sku面单接口失败！%s", e.getMessage()));
            log.error("调用sku面单接口失败!" + e.getMessage(),e);
            return "pack/sacn_print_tag";
        }
    }


    //见单出单打印其它标签
    @RequestMapping(value = "asnPrintTag", method = {RequestMethod.GET})
    public String asnPrintTag(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "pack/sacn_print_tag";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            WhFbaAllocation whFbaAllocation = allocationList.get(0);
            if (CollectionUtils.isEmpty(allocationList) || whFbaAllocation == null) {
                domain.setErrorMsg("没有sku面单!");
                return "pack/sacn_print_tag";
            }
            List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
            //JIT备货单
            WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
            if (whAsnExtra==null) {
                domain.setErrorMsg("没有sku面单!");
                return "pack/sacn_print_tag";
            }
            List<String> pdfUrlList=new ArrayList<>();
            List<String> base64List=new ArrayList<>();
            // 要进行合并的sku列表
            List<String> mergeSkuList =new ArrayList<>();
            //key为sku，name为要进行合并打印的sku标签列表对象
            Map<String, List<String>> mergeBase64SkuMap =new HashMap<>();
            //key为sku，name为要进行合并打印的sku标签的个数
            Map<String, Integer> mergeBase64Copies =new HashMap<>();
            for (WhFbaAllocationItem item : items) {
                Integer[] packageMethods = new Integer[]{AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode(), AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()};
                if (ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod())) {
                    Integer orderType = null;
                    if (AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                        orderType = 2;
                    }
                    String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + ".pdf";
                    if (!AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                        File file = new File(mergePdfPath);
                        if (file.exists()) {
                            domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static/"));
                            return "pack/sacn_print_tag";
                        }
                    }
                    String jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(allocationList.get(0).getFbaNo(), 1, orderType, null);
                    if (StringUtils.isNotBlank(jitPdfUrl) && !AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()
                            .equals(whAsnExtra.getPackageMethod())) {
                        PdfUtils.parseAndSplitToSave(jitPdfUrl, allocationList.get(0).getFbaNo(), allocationList.get(0).getAllotNum());
                        domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static/"));
                    }
                    else {
                        whAsnExtra.setPdfUrlList(Collections.singletonList(jitPdfUrl));
                        whAsnExtra.setBase64List(Collections.singletonList(convertPdfFromHttpToBase64(jitPdfUrl)));
                        domain.setWhAsnExtra(whAsnExtra);
                    }
                    return "pack/sacn_print_tag";
                }
                String tagSplit = item.getTag();
                if (StringUtils.isBlank(tagSplit)) {
                    continue;
                }
                Integer pickQuantity=Optional.ofNullable(item.getGridQuantity()).orElse(Optional.ofNullable(item.getPickQuantity()).orElse(0));
                if (pickQuantity<=0){
                    continue;
                }
                //shein的订单，包装打印的sku标签要为合并环保标后的
                boolean mergeResult = false;
                Set<String> mergeTagCode = Arrays.asList(ProductType.BOX,ProductType.BAG,ProductType.PREVENT_SUFFOCATION)
                        .stream()
                        .map(ProductType::getCode)
                        .collect(Collectors.toSet());
                List<String> base64SkuList = new ArrayList<>();
                if(whFbaAllocation.isShein()){
                    List<String> tagList = CommonUtils.splitList(tagSplit,",").stream().filter(tag -> mergeTagCode.contains(tag)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tagList)){
                        String skuCodePdfString =  convertPdfFromHttpToBase64(item.getTemuCodeUrl());
                        base64SkuList.add(skuCodePdfString);
                        for (String tag : tagList){
                            String pdfString = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_KEY + tag);
                            base64SkuList.add(pdfString);
                        }
                        mergeResult = true;
                        mergeBase64SkuMap.put(item.getProductSku(), base64SkuList);
                        mergeBase64Copies.put(item.getProductSku(), pickQuantity);
                        mergeSkuList.add(item.getProductSku());
                    }
                }
                for (Integer i = 0; i < pickQuantity; i++) {
                    if(whFbaAllocation.isShein()){
                        if (!mergeResult){
                            base64List.add(convertPdfFromHttpToBase64(item.getTemuCodeUrl()));
                        }
                    }else {
                        base64List.add(convertPdfFromHttpToBase64(whAsnExtra.getPrintSkuTagUrl()));
                    }
                }
                List<String> tagList = Arrays.asList(tagSplit.split(","));
                for (String tag : tagList) {
                    String url = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_PDF_URL_KEY + tag);
                    if (StringUtils.isNotBlank(url)) {
                        pdfUrlList.add(url);
                    }
                    if (whFbaAllocation.isShein() && mergeResult && mergeTagCode.contains(tag)){
                        continue;
                    }
                    String base64 = StringRedisUtils.get(RedisConstant.TRANSFER_SHEIN_KEY + tag);
                    if (StringUtils.isNotBlank(base64)) {
                        for (Integer i = 0; i < pickQuantity; i++) {
                            base64List.add(base64);
                        }
                    }
                }
                if(whFbaAllocation.isShein()) {
                    pdfUrlList.add(item.getTemuCodeUrl());
                }
            }
            if(!whFbaAllocation.isShein()){
                pdfUrlList.add(whAsnExtra.getPrintSkuTagUrl());
            }
            whAsnExtra.setMergeSku(mergeSkuList);
            whAsnExtra.setMergeSkuMap(mergeBase64SkuMap);
            whAsnExtra.setMergeSkuCopies(mergeBase64Copies);
            whAsnExtra.setBase64List(base64List);
            whAsnExtra.setPdfUrlList(pdfUrlList);
            domain.setWhAsnExtra(whAsnExtra);
            domain.setWhFbaAllocation(whFbaAllocation);
            return "pack/sacn_print_tag";
        } catch (Exception e) {
            domain.setErrorMsg(String.format("调用sku面单接口失败！%s", e.getMessage()));
            log.error("调用sku面单接口失败!" + e.getMessage(),e);
            return "pack/sacn_print_tag";
        }
    }


    @RequestMapping(value = "localPrintTag", method = {RequestMethod.GET})
    public String localPrintTag(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("apvNo") String apvNo) {
        if (StringUtils.isBlank(apvNo)) {
            domain.setErrorMsg("参数错误!");
            return "pack/sacn_print_tag";
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(apvNo);
        List<WhApv> allocationList = whApvService.queryWhApvAndItemList(query, null);
        try {
            WhApv whFbaAllocation = allocationList.get(0);
            if (CollectionUtils.isEmpty(allocationList) || whFbaAllocation == null) {
                domain.setErrorMsg("没有sku面单!");
                return "pack/sacn_print_tag";
            }
            List<WhApvItem> items = whFbaAllocation.getWhApvItems();
            // 要进行合并的sku列表
            Integer orderType = null;
            String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getApvNo() + ".pdf";
            File file = new File(mergePdfPath);
            if (file.exists()) {
                domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static/"));
                return "pack/sacn_print_tag";
            }
            String jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(allocationList.get(0).getApvNo(), 1, orderType, null);
            if (StringUtils.isNotBlank(jitPdfUrl)) {
                int allotQty = items.stream().mapToInt(WhApvItem::getSaleQuantity).sum();
                PdfUtils.parseAndSplitToSave(jitPdfUrl, allocationList.get(0).getApvNo(), allotQty);
                domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static/"));
            }
            return "pack/sacn_print_tag";

        } catch (Exception e) {
            domain.setErrorMsg(String.format("调用sku面单接口失败！%s", e.getMessage()));
            log.error("调用sku面单接口失败!" + e.getMessage(),e);
            return "pack/sacn_print_tag";
        }
    }


    /**
     * 保存唯一码扫描异常
     */
    private boolean savePackExceptionUuidItem(String uuid, String taskNo, String scanPage, String errorMsg, PackExceptionTypeEnum typeEnum){
        if(StringUtils.isBlank(uuid) || typeEnum == null){
            return false;
        }
        try {
            String sku = uuid.split("=")[0];
            String apvNo = null;
            if(StringUtils.isNotBlank(errorMsg)) {
                if (errorMsg.contains(":")) {
                    apvNo = errorMsg.split(":")[1];
                } else if (errorMsg.contains("：")) {
                    apvNo = errorMsg.split("：")[1];
                }
            }
            PackExceptionUuidItem item = new PackExceptionUuidItem();
            item.setUuid(uuid);
            item.setSku(sku);
            item.setExceptionType(typeEnum.getCode());
            item.setScanUser(DataContextHolder.getUserId());
            item.setScanPage(scanPage);
            item.setScanTime(new Timestamp(System.currentTimeMillis()));
            item.setPickingTackNo(taskNo);
            item.setBindingApvNo(apvNo);
            packExceptionUuidItemService.createPackExceptionUuidItem(item);
            return true;
        }catch (Exception e){
            logger.error("savePackExceptionUuidItem error:", e);
        }
        return false;
    }

    /**
     * 单品多件扫描
     *
     * @param whApvDo
     * @return
     */
    @RequestMapping(value = "sm/init", method = { RequestMethod.GET })
    public String smInit(@ModelAttribute("domain") WhApvGoodsDo whApvDo) {
        return "pack/sm_fba_init";
    }

    /**
     *
     * @param whApvDo
     * @param sku
     * @param boxNo 周转筐
     * @param lastOrderId
     * @return
     */
    @RequestMapping(value = "sm/check/sku", method = { RequestMethod.GET })
    public String smCheckSku(@ModelAttribute("domain") WhApvGoodsDo whApvDo, @RequestParam("sku") String sku,
            @RequestParam(value = "orderId", required = false) String boxNo,
            @RequestParam(value = "lastApvId", required = false) String lastOrderId,
            @RequestParam("uuid") String uuid) {
        String erroeMes = whUniqueSkuService.checkScanUniqueSku(uuid, UniqueSkuStep.PACKING.intCode());
        if (StringUtils.isNotBlank(erroeMes)) {
            if(erroeMes.contains("唯一码已绑定发货单")){
                String scanPage = "多件包装";
                this.savePackExceptionUuidItem(uuid, boxNo, scanPage, erroeMes, PackExceptionTypeEnum.REPEAT_SCAN);
            }
            whApvDo.setErrorMsg(erroeMes);
            return "pack/sm_fba_scan_sku";
        }
        // 不能为空
        if (StringUtils.isBlank(sku)) {
            return "pack/sm_fba_scan_sku";
        }
        // 统一转换成拣货任务号
        ResponseJson rsp = whBoxService.checkPackBoxNoScan(boxNo, null);
        String taskNo = rsp.getMessage();
        if (StringUtils.isNotBlank(sku)) {
            try {
                WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
                queryCondition.setSku(sku);
                queryCondition.setTaskNo(taskNo);
                queryCondition.setStatusList(Arrays.asList(AsnPrepareStatus.CHECK_PRINT.intCode(),
                        AsnPrepareStatus.PICK_STOCK_OUT.intCode()));
                queryCondition.setApvType("SM");
                queryCondition.setPackPriority(true);
                WhFbaAllocation whFbaAllocation = whFbaAllocationService.scanMaxPriorityGoodsPrintFba(queryCondition,
                        whApvDo);
                if (whFbaAllocation == null) {
                    queryCondition.setApvType("SM");
                    queryCondition.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
                    try {
                        whFbaAllocation = this.scanMaxPriorityGoodsPrintTemu(queryCondition, whApvDo);
                    } catch (Exception e) {
                        if (StringUtils.isNotBlank(whApvDo.getErrorMsg())) {
                            return "pack/sm_fba_scan_sku";
                        }
                        throw new RuntimeException(e.getMessage());
                    }

                }
                whApvDo.setWhFbaAllocation(whFbaAllocation);
            }
            catch (Exception ex) {
                whApvDo.setErrorMsg(ex.getMessage());
            }
        }

        return "pack/sm_fba_scan_sku";
    }

    /**
     * 多品多件包装校验
     *
     * @param uuid
     * @return
     */
    @RequestMapping(value = "checkScanPackingUniqueSku", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson checkScanPackingUniqueSku(@RequestParam("uuid") String uuid,
                                                  @RequestParam(value = "type", required = false) Integer type) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        logger.info("fbaCheckScanPackingUniqueSku-> "+uuid);
        if (StringUtils.isNotBlank(uuid) && !uuid.contains("=")) {
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("SKU");
            return response;
        }

        String erroeMes = null;
        boolean isCancelApvNo=false;
        if (StringUtils.isNotBlank(uuid) && uuid.contains("=")) {
            WhUniqueSku unique = whUniqueSkuService.getWhUniqueSkuByUuidAndSku(uuid);
            if (unique == null) {
                try {
                    unique = whUniqueSkuService.scanAndAddWhUniqueSku(uuid, UniqueSkuStep.PACKING.intCode());
                }catch (Exception e){
                    erroeMes = "生成唯一码错误：" + e.getMessage();
                    response.setMessage(erroeMes);
                    return response;
                }
            }
            if (unique != null) {
                // 非多品多件校验
                if (!Integer.valueOf(1).equals(type) && UniqueSkuStep.PACKING.intCode().equals(unique.getStep())
                        && StringUtils.isNotBlank(unique.getApvNo())) {
                    erroeMes = "唯一码已绑定发货单：" + unique.getApvNo();
                }
                if (StringUtils.isNotBlank(unique.getApvNo())){
                    response.setMessage(unique.getApvNo());
                    WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
                    queryCondition.setFbaNo(unique.getApvNo());
                    List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition,null);
                    if (!Integer.valueOf(1).equals(type) && CollectionUtils.isEmpty(whFbaAllocationList)) {
                        // 唯一码校验兼容拼多多
                        TemuPrepareOrderQueryCondition temuQuery = new TemuPrepareOrderQueryCondition();
                        temuQuery.setPackageSn(unique.getApvNo());
                        List<TemuPackageInfo> packageInfos = temuPrepareOrderService.queryTemuPackageAndItems(temuQuery, null);
                        if (CollectionUtils.isNotEmpty(packageInfos)
                                && !TemuPackageStatus.CANCEL.intCode().equals(packageInfos.get(0).getPackageStatus())) {
                            whFbaAllocationList = new ArrayList<>();
                            WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
                            whFbaAllocation.setStatus(AsnPrepareStatus.CHECK_PRINT.intCode());
                            whFbaAllocationList.add(whFbaAllocation);
                        }
                    }
                    if (CollectionUtils.isEmpty(whFbaAllocationList) || whFbaAllocationList.get(0).getStatus().equals(AsnPrepareStatus.CANCEL.intCode())){
                        WhUniqueSkuLog uniqueSkuLog = new WhUniqueSkuLog();
                        uniqueSkuLog.setUniqueId(unique.getId());
                        uniqueSkuLog.setStep(UniqueSkuStep.PACKING.intCode());
                        uniqueSkuLog.setOrderNo(unique.getApvNo());
                        uniqueSkuLog.setContent("中转仓唯一码包装扫描，关联出库单 " + unique.getApvNo()+"已取消");
                        whUniqueSkuLogService.createWhUniqueSkuLog(uniqueSkuLog);
                        isCancelApvNo=true;
                    }
                    //仓发被标记拆分
                    if (CollectionUtils.isNotEmpty(whFbaAllocationList) && whFbaAllocationList.get(0).getIsAsn() != null
                            && whFbaAllocationList.get(0).getIsAsn()
                            && whFbaAllocationList.get(0).getTransitType() != null
                            && whFbaAllocationList.get(0).getTransitType() == 1) {
                        Map<String, Integer> skuMap = whFbaAllocationList.get(0).getItems().stream()
                                .collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku,
                                        Collectors.summingInt(i -> Optional.ofNullable(i.getGridQuantity()).orElse(0))));
                        erroeMes = String.format("该SKU对应的发货单:%s，%s已标记拆包，请将SKU放回周转筐", whFbaAllocationList.get(0).getFbaNo(),
                                JSON.toJSONString(skuMap));
                    }
                }
            }
            else {
                logger.info("唯一码不存在-> "+uuid);
                erroeMes = "唯一码不存在！";
            }
        }
        else {
            erroeMes = "请扫描正确的唯一码！";
        }


        if(isCancelApvNo){
            response.setMessage(String.format("唯一码：%s,对应出库单%s已取消",uuid,response.getMessage()));
            return response;
        }

        if (StringUtils.isNotBlank(erroeMes)) {
            response.setMessage(erroeMes);
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 核对完成
     *
     * @param apvId
     * @return
     */
    @RequestMapping(value = "sm/check/pass", method = { RequestMethod.GET})
    @ResponseBody
    public ResponseJson smPass(@RequestParam("apvId") Integer apvId) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        try {
            whFbaAllocationService.passBasketScan(apvId);
            // 清除上一次扫的发货单
            CacheUtils.set(CacheName.SCAN_APV_ID, apvId.toString(), null);
        } catch (Exception e) {
            response.setMessage("中转仓多件包装失败！"+e.getMessage());
            log.error("中转仓多件包装失败！"+e.getMessage(),e);
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }




    /**
     * 多品扫描
     *
     * @param whApvDo
     * @return
     */
    @RequestMapping(value = "check", method = { RequestMethod.GET })
    public String checkScan(@ModelAttribute("domain") WhApvGoodsDo whApvDo) {
        return "singlemoreproduct/fba_singlemoreproduct_scan";
    }


    /**
     * 多品包装扫描
     * @param whApvDo
     * @param uniqueKey
     * @param apvNo
     * @return
     */
    @RequestMapping(value = "check/uniqueKey", method = { RequestMethod.GET })
    public String scanUniqueKey(@ModelAttribute("domain") WhApvGoodsDo whApvDo,
            @RequestParam("uniqueKey") String uniqueKey,
            @RequestParam(required = false, value = "suitSku") String suitSku,
            @RequestParam(required = false, value = "beforeSku") String beforeSku,
            @RequestParam(required = false, value = "apvNo") String apvNo) {
        try{

            String sku = CompatibleSkuUtils.getSku(uniqueKey);
            String existStr = StringRedisUtils.get(RedisConstant.JIT_MM_APV_PACK_KEY + apvNo);

            if (StringUtils.isNotBlank(apvNo) && StringUtils.isNotBlank(existStr)) {
                WhApvGoodsDo apvDo = JSONObject.parseObject(existStr, new TypeReference<WhApvGoodsDo>() {
                });
                if (apvDo == null || apvDo.getWhFbaAllocation() == null || apvDo.getWhFbaAllocation().getItems() ==null
                        || apvDo.getWhFbaAllocation().getItems().size() == 0) {
                    return "singlemoreproduct/fba_singlemoreproduct_scan_view";
                }

                WhFbaAllocation whFbaAllocation = apvDo.getWhFbaAllocation();
                if (whFbaAllocation.isShein() && StringUtils.isNotBlank(whFbaAllocation.getFbaNo())) {
                    String errorMsg = packExceptionUuidItemService.checkFbaGpsrTag(whFbaAllocation.getFbaNo(), sku);
                    if (StringUtils.isNotBlank(errorMsg)) {
                        whApvDo.setErrorMsg(errorMsg);
                        return "singlemoreproduct/fba_singlemoreproduct_scan_view";
                    }
                }


                if (StringUtils.isBlank(suitSku))
                    suitSku = null;
                String finalSuitSku = suitSku;
                WhFbaAllocationItem fbaAllocationItem = null;

                List<WhFbaAllocationItem> fbaAllocationItems = apvDo.getWhFbaAllocation().buildGroupItems();
                if (apvDo.getWhFbaAllocation().containSuitSku()) {
                    fbaAllocationItems = apvDo.getWhFbaAllocation().getItems();
                }
                if (StringUtils.isNotBlank(beforeSku) && !StringUtils.equalsIgnoreCase(sku, beforeSku)) {
                    fbaAllocationItem = fbaAllocationItems.stream().filter(item -> item.getProductSku().equals(sku))
                            .findFirst().orElse(null);
                }
                else {
                    fbaAllocationItem = fbaAllocationItems.stream()
                            .filter(item -> item.getProductSku().equals(sku)
                                    && StringUtils.equalsIgnoreCase(finalSuitSku, item.getProductBarcode()))
                            .findFirst().orElse(null);
                }
                BeanUtils.copyProperties(apvDo, whApvDo);
                whApvDo.setWhFbaAllocationItem(fbaAllocationItem);
                if (StringUtils.isBlank(suitSku)){
                    whApvDo.setSuitSkuMap(null);
                }
                return "singlemoreproduct/fba_singlemoreproduct_scan_view";
            }

            WhFbaAllocation whFbaAllocation = null;
            WhFbaAllocationItem fbaAllocationItem = null;

            if (StringUtils.isBlank(uniqueKey) || !uniqueKey.contains("=")) {
                return "singlemoreproduct/fba_singlemoreproduct_scan_view";
            }

            WhUniqueApvGridQueryCondition gridQuery = new WhUniqueApvGridQueryCondition();
            gridQuery.setUuid(uniqueKey);
            List<WhUniqueApvGrid> whUniqueApvGridList = whUniqueApvGridService.queryWhUniqueApvGrids(gridQuery, null);

            if (CollectionUtils.isNotEmpty(whUniqueApvGridList)) {
                Optional<WhUniqueApvGrid> optional =  whUniqueApvGridList.stream().max(Comparator.comparing(WhUniqueApvGrid::getCreationDate));
                if (optional.isPresent() && StringUtils.isNotBlank(optional.get().getApvNo())) {
                    WhUniqueApvGrid whUniqueApvGrid = optional.get();
                    apvNo = whUniqueApvGrid.getApvNo();
                    whApvDo.setTaskNo(whUniqueApvGrid.getTaskNo());
                    WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
                    taskQuery.setGridStatus(PickTaskGridStatus.COMPLETED.intCode());
                    taskQuery.setTaskNo(whUniqueApvGrid.getTaskNo());
                    int gridFinish = whPickingTaskDao.queryWhPickingTaskCount(taskQuery);
                    if (gridFinish == 0) {
                        whApvDo.setErrorMsg("拣货任务：" + whUniqueApvGrid.getTaskNo() + " 未播种完成，请完成播种后再包装");
                        return "singlemoreproduct/fba_singlemoreproduct_scan_view";
                    }

                    // 2.用拣货任务查询
                    WhPickingTask whPickingTask = whPickingTaskDao.queryWhPickingTask(taskQuery);
                    // 设置拣货类型
                    if (whPickingTask != null && whPickingTask.getTaskType() != null) {
                        whApvDo.setLocation(whPickingTask.getTaskType().toString());
                    }

                    //获取拣货任务的全部订单号
                    WhPickingTaskItemQueryCondition taskItemQueryCondition=new WhPickingTaskItemQueryCondition();
                    taskItemQueryCondition.setTaskId(whPickingTask.getId());

                    List<WhPickingTaskItem> whPickingTaskItems = whPickingTaskItemDao.queryPickingTaskItemAndSkuList(taskItemQueryCondition, null);

                    List<String> apvNoList= whPickingTaskItems.stream().map(WhPickingTaskItem::getApvNo).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(apvNoList)) {
                        whApvDo.setErrorMsg("拣货任务：" + whUniqueApvGrid.getTaskNo() + "没有查询到发货单号");
                        return "singlemoreproduct/fba_singlemoreproduct_scan_view";
                    }

                    WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
                    queryCondition.setFbaNo(StringUtils.join(apvNoList,","));
                    List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);

                    if (CollectionUtils.isEmpty(whFbaAllocationList)) {
                        return "singlemoreproduct/fba_singlemoreproduct_scan_view";
                    }

                    for (WhFbaAllocation fbaAllocation : whFbaAllocationList) {
                        if (fbaAllocation.getFbaNo().equals(apvNo)) {
                            WhFbaAllocationQueryCondition fbaAllocationQueryCondition=new WhFbaAllocationQueryCondition();
                            fbaAllocationQueryCondition.setSku(sku);
                            fbaAllocationQueryCondition.setFbaNo(apvNo);
                            fbaAllocationQueryCondition.setStatusList(Arrays.asList(AsnPrepareStatus.CHECK_PRINT.intCode(),AsnPrepareStatus.PICK_STOCK_OUT.intCode()));
                            fbaAllocationQueryCondition.setTaskNo(whApvDo.getTaskNo());
                            fbaAllocationQueryCondition.setApvType("MM");
                            whFbaAllocation=whFbaAllocationService.scanMaxPriorityGoodsPrintFba(fbaAllocationQueryCondition,whApvDo);

                            if (Objects.isNull(whFbaAllocation) && StringUtils.isNotBlank(whApvDo.getErrorMsg())){
                                return "singlemoreproduct/fba_singlemoreproduct_scan_view";
                            }

                            if (Objects.isNull(whFbaAllocation)){
                                throw new RuntimeException(String.format("扫描sku:%s,没有查询到中转仓发货单号",queryCondition.getSku()));
                            }
                            List<WhFbaAllocationItem> fbaAllocationItems = whFbaAllocation.getItems().stream()
                                    .filter(f -> StringUtils.equalsIgnoreCase(sku, f.getProductSku()))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(fbaAllocationItems)) {
                                if (!whFbaAllocation.containSuitSku()) {
                                    fbaAllocationItem = whFbaAllocation.buildGroupItems().stream()
                                            .filter(f -> StringUtils.equalsIgnoreCase(sku, f.getProductSku()))
                                            .collect(Collectors.toList()).get(0);
                                }
                                else {
                                    if (fbaAllocationItems.stream()
                                            .anyMatch(i -> i.getSuitFlag() != null && i.getSuitFlag() != 0)) {
                                        fbaAllocationItems
                                                .removeIf(i -> i.getSuitFlag() == null || i.getSuitFlag() == 0);
                                    }
                                    fbaAllocationItem = fbaAllocationItems.get(0);
                                    Map<String, List<WhFbaAllocationItem>> suitMap = whFbaAllocation.getItems().stream()
                                            .collect(Collectors.groupingBy(
                                                    i -> Optional.ofNullable(i.getProductBarcode()).orElse("普通")));
                                    whApvDo.setSuitSkuMap(suitMap);
                                    Map<String, Set<String>> skuFnSkuMap = whFbaAllocation.getItems().stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku,
                                            Collectors.mapping(WhFbaAllocationItem::getProductBarcode, Collectors.toSet())));
                                    whApvDo.setSkuFnSkuMap(JSON.toJSONString(skuFnSkuMap));
                                }

                            }
                            whApvDo.setFbaSkuMap(whFbaAllocation.buildGroupItems().stream().collect(Collectors.toMap(WhFbaAllocationItem::getProductSku,i->i)));
                        }
                    }

                    whApvDo.setWhFbaAllocationList(whFbaAllocationList);

                }else{
                    return "singlemoreproduct/fba_singlemoreproduct_scan_view";
                }
            }

            whApvDo.setWhFbaAllocation(whFbaAllocation);
            whApvDo.setWhFbaAllocationItem(fbaAllocationItem);
            StringRedisUtils.set(RedisConstant.JIT_MM_APV_PACK_KEY + apvNo, JSONObject.toJSONString(whApvDo),  3600L);
            return "singlemoreproduct/fba_singlemoreproduct_scan_view";
        }catch (Exception e) {
            log.error("中转仓多品包装失败！"+e.getMessage(),e);
            whApvDo.setErrorMsg(e.getMessage());
            return "singlemoreproduct/fba_singlemoreproduct_scan_view";

        }
    }

    /**
     * 核对完成
     *
     * @param orderDo
     * @return
     */
    @RequestMapping(value = "check/pass", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson pass(@ModelAttribute("domain") WhApvGoodsDo orderDo, @RequestParam("apvId") Integer apvId,
                             @RequestParam("boxNo") String boxNo,
                             @RequestParam(value = "totalQuantity", required = false) Integer totalQuantity,
                             @RequestParam(value = "skuCode", required = false) List<String> skuCodeList) {
        ResponseJson rsp=new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        if (apvId==null || StringUtils.isBlank(boxNo)){
            rsp.setMessage("参数异常，任务号为空 ！");
            return rsp;
        }
        try{
            rsp = whBoxService.checkPackBoxNoScan(boxNo, BoxType.TRANSFER_WAREHOUSE.intCode());

            if (StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL)) {
                return rsp;

            }
            WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
            queryCondition.setId(apvId);
            List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition,null);

            WhFbaAllocation whFbaAllocation=new WhFbaAllocation();
            if (CollectionUtils.isEmpty(whFbaAllocationList)){
                rsp.setMessage("找不到发货单 ！");
                return rsp;
            }else {
                whFbaAllocation = whFbaAllocationList.get(0);
                if (whFbaAllocation.getStatus().equals(AsnPrepareStatus.CANCEL.intCode())){
                    rsp.setMessage("发货单已取消发货 ！");
                    return rsp;
                }
                if (whFbaAllocation.getStatus().equals(AsnPrepareStatus.DELIVER.intCode())){
                    rsp.setMessage("发货单已交运 ！");
                    return rsp;
                }
            }
            whFbaAllocationService.passMoreProductScan(whFbaAllocation);
            // 批次内的订单 为 1 完成的时候，解锁周转筐
            if (StringUtils.isBlank(boxNo)) {
                // 获取真正的 周转筐号码 解锁
                String findBoxNo = whBoxService.findBoxNo(boxNo);
                if (StringUtils.isNotBlank(findBoxNo)) {
                    // 解锁
                    whBoxService.updateWhBoxOfUnbinding(findBoxNo, new String[][] { { "中转仓多品扫描完成！" } });
                }

                // 传递给页面 已经解锁
                rsp.setMessage(boxNo);
            }
            //发货单包装完成，移除扫描缓存
            StringRedisUtils.del(RedisConstant.JIT_MM_APV_PACK_KEY + whFbaAllocation.getFbaNo());


        }catch (Exception e) {
            log.error("中转仓多品包装失败！"+e.getMessage(),e);
            rsp.setMessage("中转仓多品包装失败!"+e.getMessage());
            return rsp;

        }
        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    /**
     * 打印箱唛
     *
     * @param apvNo
     * @return
     */
    @RequestMapping(value = "localPrintXiangmai", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson localPrintXiangmai(@RequestParam("apvNo") String apvNo) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(apvNo);
        WhApv whApv = whApvService.queryWhApv(query);
        if (whApv == null) {
            responseJson.setMessage("找不到发货单 ！");
            return responseJson;
        }
        try {
            String jitPdfUrl = PdfUtils.STATIC_FILE_JIT_MD_PATH + "/" + apvNo + ".pdf";
            File file = new File(jitPdfUrl);
            if (file.exists()) {
                jitPdfUrl = StringUtils.substringAfterLast(jitPdfUrl, "static/");
            }
            else {
                jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(apvNo, 2, null, null);
                responseJson.getBody().put("jitPdfUrl", convertPdfFromHttpToBase64(jitPdfUrl));
            }
            try {
                Integer boxNumber = jitPickBoxScanService.packGetBoxNumber(whApv.getId());
                String boxStr = String.valueOf(boxNumber == null ? "": boxNumber);
                responseJson.setLocation(boxStr);
            }
            catch (Exception e) {
                log.error("获取分拣框失败：" + e.getMessage(), e);
            }
            responseJson.setMessage(jitPdfUrl);
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;

        }
        catch (Exception e) {
            responseJson.setMessage("打印面单失败! " + e.getMessage());
            return responseJson;
        }
    }

    /**
     * 打印箱唛
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "printXiangmai", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson printXiangmai(@RequestParam("id") Integer id) {
        ResponseJson responseJson=new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            int boxTotal = allocationList.get(0).getItems().stream().map(WhFbaAllocationItem::getBoxNo).distinct()
                    .collect(Collectors.toList()).size();
            WhFbaAllocation whFbaAllocation = allocationList.get(0);
            //JIT备货单打印箱唛
            WhAsnExtra whAsnExtra = allocationList.get(0).getWhAsnExtra();
            Integer[] packageMethods = new Integer[]{AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode(), AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()};
            if (!Objects.equals(SaleChannel.CHANNEL_SHEIN, whFbaAllocation.getPurposeHouse()) && whAsnExtra != null
                    && ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod())) {
                Integer orderType = null;
                Integer boxQuantity = null;
                if (AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                    orderType = 2;
                    boxQuantity = boxTotal;
                }
                else {
                    try {
                        Integer boxNumber = jitPickBoxScanService.fbaPackGetBoxNumber(allocationList.get(0));
                        String boxStr = String.valueOf(boxNumber);
                        if(Objects.equals(AsnPackageMethodEnum.JIT_HALF.getCode(),whAsnExtra.getPackageMethod())){
                            boxStr = JITBoxOrderType.HALF_JIT_BOX_ORDER_TYPE.getCode() + boxStr;
                        }
                        responseJson.setLocation(boxNumber == null ? null : String.valueOf(boxStr));
                    }
                    catch (Exception e) {
                        log.error("获取分拣框失败：" + e.getMessage(), e);
                    }
                }
                String jitPdfUrl = PdfUtils.STATIC_FILE_JIT_MD_PATH + "/" + allocationList.get(0).getFbaNo() + ".pdf";
                File file = new File(jitPdfUrl);
                if (file.exists()) {
                    jitPdfUrl = StringUtils.substringAfterLast(jitPdfUrl, "static/");
                }
                else {
                    jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(allocationList.get(0).getFbaNo(), 2,
                            orderType, boxQuantity);
                    responseJson.getBody().put("jitPdfUrl", convertPdfFromHttpToBase64(jitPdfUrl));
                }

                responseJson.setMessage(jitPdfUrl);
                responseJson.setStatus(StatusCode.SUCCESS);
                return responseJson;
            }

            String printXiangmaiFBA = PdfUtils.STATIC_FILE_JIT_MD_PATH + "/" + allocationList.get(0).getFbaNo() + ".pdf";
            File file = new File(printXiangmaiFBA);
            if (file.exists()) {
                printXiangmaiFBA = StringUtils.substringAfterLast(printXiangmaiFBA, "static/");
            }
            else {
                printXiangmaiFBA = whFbaAllocationHandleService.printXiangmaiFBA(allocationList.get(0));
                responseJson.getBody().put("jitPdfUrl", convertPdfFromHttpToBase64(printXiangmaiFBA));
            }
            responseJson.setMessage(printXiangmaiFBA);
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        } catch (Exception e) {
            responseJson.setMessage("打印面单失败! "+e.getMessage());
            return responseJson;
        }
    }

    @RequestMapping(value = "print/base64", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson printPdfUrl(@RequestParam("base64") String pdfUrl) {
        ResponseJson responseJson=new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            responseJson.setMessage(convertPdfFromHttpToBase64(pdfUrl));
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        } catch (Exception e) {
            responseJson.setMessage("PDF转Base64失败！");
            return responseJson;
        }
    }


    public String convertPdfFromHttpToBase64(String pdfUrl) {
        try {
            URL url = new URL(pdfUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            InputStream inputStream = connection.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            byte[] pdfBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(pdfBytes);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }


    @PostMapping(value = "checkWaitSplit")
    @ResponseBody
    public ResponseJson checkWaitSplit(@RequestParam("apvId") Integer apvId) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            return whFbaAllocationHandleService.checkWaitSplit(apvId);
        }
        catch (Exception e) {
            responseJson.setMessage("标记待拆包失败！" + e.getMessage());
            return responseJson;
        }
    }

    @GetMapping(value = "printGpsrTag")
    public String printGpsrTag(@ModelAttribute("domain") GpsrTagDo tagDo, @RequestParam("apvNo") String apvNo,
                               @RequestParam(value = "sku", required = false) String sku,
                               @RequestParam(value = "quantity", required = false) Integer quantity) {

        try {
            packExceptionUuidItemService.printFbaGpsrTag(tagDo, apvNo, sku, quantity);
        } catch (Exception e) {
            logger.error("中转仓单据获取GPSR标签失败！"+e.getMessage(), e);
            tagDo.setErrorMsg("中转仓单据获取GPSR标签失败！"+e.getMessage());
            return GPSR_TAG_PRINT;
        }
        return GPSR_TAG_PRINT;
    }
}
