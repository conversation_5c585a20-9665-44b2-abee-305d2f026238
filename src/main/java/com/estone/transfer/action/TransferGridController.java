package com.estone.transfer.action;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.estone.apv.bean.ApvGridData;
import com.estone.apv.bean.WhApvGrid;
import com.estone.apv.domain.WhApvMoreProductsDo;
import com.estone.apv.util.ApvGridUtils;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.CompatibleSkuUtils;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.enums.PickingTaskType;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.sowstockout.bean.SowStockout;
import com.estone.sowstockout.bean.SowStockoutQueryCondition;
import com.estone.sowstockout.service.SowStockoutService;
import com.estone.system.param.bean.SystemParam;
import com.estone.transfer.service.TransferGridService;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.enums.BoxStatus;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 多品多件播种
 * @date 2020/3/12 9:47
 */
@Controller
@RequestMapping(value = "transferGrid")
@Slf4j
public class TransferGridController {

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private TransferGridService transferGridService;

    @Resource
    private SowStockoutService sowStockoutService;
    @Resource
    private WhPickingTaskService whPickingTaskService;
    private final static String GRID_SCAN_VIEW = "transfer/transfer_grid_scan_view";

    private final static String GRID_SCAN = "transfer/transfer_grid_scan";

    @RequestMapping(value = "grid", method = { RequestMethod.GET })
    public String checkScan(@ModelAttribute("domain") WhApvMoreProductsDo domain) {
        initData(domain);
        return GRID_SCAN;
    }

    private void initData(WhApvMoreProductsDo domain) {
        // 最大格子数量
        SystemParam systemParam = CacheUtils.SystemParamGet("apv_params.grid_num");
        Integer gridNum = Integer.valueOf(systemParam.getParamValue());
        domain.setGridNum(gridNum);
    }

    /**
     * 扫描序列号
     *
     * @param domain
     * @param gridLocation
     * @param box 周转筐
     * @return
     */
    @RequestMapping(value = "scan/box", method = { RequestMethod.GET })
    public String scanBox(@ModelAttribute("domain") WhApvMoreProductsDo domain,
            @RequestParam("gridLocation") Integer gridLocation, @RequestParam("box") String box) {
        box = StringUtils.trim(box);
        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equals(rsp.getStatus(), StatusCode.FAIL)) {
            domain.setErrorMsg(rsp.getMessage());
            return GRID_SCAN_VIEW;
        }
        String taskNo = rsp.getMessage();
        if (StringUtils.isEmpty(taskNo) || StringUtils.isEmpty(rsp.getLocation())) {
            domain.setErrorMsg("拣货任务号 或者拣货类型为空！");
            return GRID_SCAN_VIEW;
        }
        domain.setTaskType(Integer.valueOf(rsp.getLocation()));
        WhPickingTask whPickingTask = null;
        try {
            // 获取拣货任务
            whPickingTask = (WhPickingTask) rsp.getBody().get("whPickingTask");
            // 拣货仓库类型
            Integer warehouseType = whPickingTask.getWarehouseType();
            domain.setWarehouseType(warehouseType);
            domain.setIsAsn(whPickingTask.getIsAsn());
            domain.setWaybillType(whPickingTask.getWaybillType());

            if (PickingTaskType.ASN_PREPARE.intCode().equals(whPickingTask.getIsAsn())) {
                domain.setErrorMsg(PickingTaskType.ASN_PREPARE.getName() + "拣货类型，请在FBA播种页面播种！");
                return GRID_SCAN_VIEW;
            }

            if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(whPickingTask.getTaskType())) {
                domain.setErrorMsg(PickingTaskType.ASN_FIRST_SINGLE.getName() + "拣货类型，不需要播种！");
                return GRID_SCAN_VIEW;
            }

            boolean isAsn = whPickingTask.getIsAsn() != null
                    && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn());
            
            if (whPickingTask.getTaskType() == null || !isAsn
                    && !PickingTaskType.getJITIntCode().contains(whPickingTask.getTaskType())
                            && !PickingTaskType.getJitAsnIntCode().contains(whPickingTask.getTaskType())) {
                domain.setErrorMsg("扫描的非中转仓或者仓发任务 ！");
                return GRID_SCAN_VIEW;
            }
        }
        catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
            return GRID_SCAN_VIEW;
        }
        initData(domain);

        ApvGridData apvGridData = new ApvGridData();
        apvGridData.setInit(true);
        apvGridData.setTaskNo(taskNo);
        apvGridData.setGridLocation(gridLocation);
        apvGridData.setTaskType(whPickingTask.getTaskType());
        apvGridData.setWhPickingTask(whPickingTask);
        transferGridService.scanBoxToBuildGridData(apvGridData);

        domain.setSerialNumber(taskNo);
        domain.setApvCount(apvGridData.getApvCount());
        domain.setPickingQuantity(apvGridData.getPickQuantity());
        domain.setSowingQuantity(apvGridData.getSowingQuantity());
        buildGridData(domain, apvGridData);
        return GRID_SCAN_VIEW;
    }

    /**
     * @param gridLocation 位置
     * @param box 周转筐
     * @Description 多品多件播种
     * <AUTHOR>
     * @date 2020/3/12 15:59
     * @version 1.0
     */
    @RequestMapping(value = "scan/sku", method = { RequestMethod.GET })
    public String scanMaxPriorityWhApvGrid(@ModelAttribute("domain") WhApvMoreProductsDo domain,
            @RequestParam("gridLocation") Integer gridLocation, @RequestParam("box") String box,
            @RequestParam("sku") String sku, @RequestParam("uuid") String uuid) {
        // 兼容SKU编码和唯一码
        sku = CompatibleSkuUtils.getSku(sku);

        // 通过扫描的 周转筐/拣货任务号 校验 捡货任务任务
        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL)) {
            domain.setErrorMsg(rsp.getMessage());
            return GRID_SCAN_VIEW;

        }
        // 序列号
        String serialNumber = rsp.getMessage();

        if (StringUtils.isEmpty(serialNumber)) {
            domain.setErrorMsg("任务号为空！");
            return GRID_SCAN_VIEW;
        }

        WhPickingTask whPickingTask = (WhPickingTask) rsp.getBody().get("whPickingTask");
        domain.setTaskType(Integer.valueOf(rsp.getLocation()));
        domain.setSerialNumber(serialNumber);
        domain.setIsAsn(whPickingTask.getIsAsn());
        // 输入唯一码时，校验唯一格式，是否重复扫描。跨仓/本仓补码
        String erroeMes = whUniqueSkuService.checkAsnGridScanUniqueSku(uuid, serialNumber,
                UniqueSkuStep.GRID.intCode());

        if (StringUtils.isNotBlank(erroeMes)) {
            domain.setErrorMsg(erroeMes);
            return GRID_SCAN_VIEW;
        }

        ApvGridData apvGridData = new ApvGridData();
        apvGridData.setTaskNo(serialNumber);
        apvGridData.setGridLocation(gridLocation);
        apvGridData.setSku(sku);
        apvGridData.setUuid(uuid);
        apvGridData.setTaskType(whPickingTask.getTaskType());
        apvGridData.setWhPickingTask(whPickingTask);
        try {
            WhApvGrid scanWhApvGrid = transferGridService.scanSkuTogrid(apvGridData);
            if (scanWhApvGrid != null) {
                if (Integer.valueOf(-1).equals(scanWhApvGrid.getId())) {
                    domain.setErrorMsg("播种数量不能大于拣货数量!");
                    return GRID_SCAN_VIEW;
                }
                if (Integer.valueOf(-2).equals(scanWhApvGrid.getId())) {
                    domain.setErrorMsg("播种数量超过需求数量,后续进行库内返架!");
                    return GRID_SCAN_VIEW;
                }
                if (scanWhApvGrid.isCancelApvNo()) {
                    domain.setErrorMsg(String.format("唯一码：%s,对应出库单%s已取消", uuid, scanWhApvGrid.getWhApv().getApvNo()));
                    return GRID_SCAN_VIEW;
                }
                // 设置
                domain.setWhApvGrid(scanWhApvGrid);
            }
        }
        catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
            return GRID_SCAN_VIEW;
        }

        initData(domain);
        transferGridService.scanBoxToBuildGridData(apvGridData);
        buildGridData(domain, apvGridData);
        return GRID_SCAN_VIEW;
    }

    /**
     * 扫描后组装格子状态数据
     *
     * @param domain
     * @param apvGridData
     */
    public void buildGridData(WhApvMoreProductsDo domain, ApvGridData apvGridData) {
        if (CollectionUtils.isNotEmpty(apvGridData.getFbaAllocations())) {
            domain.setWhApvGridMap(apvGridData.getWhApvGridMap());
            // 少播种的格子
            if (CollectionUtils.isNotEmpty(apvGridData.getLessGridNos())) {
                domain.setLessGridNoStr(StringUtils.join(apvGridData.getLessGridNos(), ","));
            }
            // 少拣的格子
            if (CollectionUtils.isNotEmpty(apvGridData.getLessPickNos())) {
                domain.setLessPickNoStr(StringUtils.join(apvGridData.getLessPickNos(), ","));
            }
            // 取消的格子
            if (CollectionUtils.isNotEmpty(apvGridData.getCancelGridNos())) {
                domain.setCancelGridNoStr(StringUtils.join(apvGridData.getCancelGridNos(), ","));
            }
            domain.setSuccessApvCount(apvGridData.getSuccessGridApvCount());
        }
    }

    /**
     * 绑定异常周转筐(二次配货)
     *
     * @param stockoutBox 播种异常
     * @param boxCayi 播种差异
     * @param box 拣货周转筐
     * @param differ 总播种差异
     * @param lessGridNoStr 播种差异格子号
     * @param lessPickNoStr 拣货少拣格子号
     * @param cancelGridNoStr 取消等格子号
     * @return
     */
    @RequestMapping(value = "binding/stockout", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson bindingStockoutBox(@RequestParam(name = "stockoutBox", required = false) String stockoutBox,
            @RequestParam(name = "boxCayi", required = false) String boxCayi, @RequestParam("box") String box,
            @RequestParam("differ") Integer differ,
            @RequestParam(name = "lessGridNoStr", required = false) String lessGridNoStr,
            @RequestParam(name = "taskType", required = false) Integer taskType,
            @RequestParam(name = "lessPickNoStr", required = false) String lessPickNoStr,
            @RequestParam(name = "cancelGridNoStr", required = false) String cancelGridNoStr) {

        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        WhPickingTask whPickingTask = null;
        String errorMes = null;

        try {

            WhBox stockoutWhBox = null;
            errorMes = ApvGridUtils.checkSubmitStockoutGridData(stockoutBox, lessPickNoStr);
            if (StringUtils.isNotBlank(errorMes)) {
                rsp.setMessage(errorMes);
                return rsp;
            }
            if (StringUtils.isNotBlank(stockoutBox)) {
                stockoutWhBox = whBoxService.queryWhBoxByBoxNo(stockoutBox);
                // 检查提交的周转筐是否可用
                errorMes = ApvGridUtils.checkSubmitTransferStockoutWhBox(stockoutWhBox,taskType);
                if (StringUtils.isNotBlank(errorMes)) {
                    rsp.setMessage(errorMes);
                    return rsp;
                }
            }

            // 1,先查询缺货数量有多少
            String findBoxNo = whBoxService.findBoxNo(box);
            if (StringUtils.isEmpty(findBoxNo)) {
                rsp.setMessage("找不到周转筐");
                return rsp;
            }
            WhBox whBox = whBoxService.queryWhBoxByBoxNo(findBoxNo);
            if (whBox == null) {
                rsp.setMessage("找不到周转筐");
                return rsp;
            }

            Integer relationNo = Integer.valueOf(whBox.getRelationNo());
            whPickingTask = whPickingTaskService.getWhPickingTask(relationNo);
            String serialNumber = whPickingTask.getTaskNo();
            whPickingTask.setBoxNo(whBox.getBoxNo());

            errorMes = ApvGridUtils.checkSubmitFinishGridData(boxCayi, lessGridNoStr);
            if (StringUtils.isNotBlank(errorMes)) {
                rsp.setMessage(errorMes);
                return rsp;
            }

            WhBox caYiWhBox = null;
            if (StringUtils.isNotBlank(boxCayi)) {
                caYiWhBox = whBoxService.queryWhBoxByBoxNo(boxCayi);
                // 检查提交的周转筐是否可用
                errorMes = ApvGridUtils.checkSubmitTranferCaYiWhBox(caYiWhBox, whPickingTask);
                if (StringUtils.isNotBlank(errorMes)) {
                    rsp.setMessage(errorMes);
                    return rsp;
                }
            }

            ApvGridData apvGridData = new ApvGridData();
            apvGridData.setTaskNo(serialNumber);
            apvGridData.setWhPickingTask(whPickingTask);
            apvGridData.setWhBox(whBox);
            apvGridData.setDiffer(differ);
            apvGridData.setTaskType(whPickingTask.getTaskType());
            transferGridService.scanBoxToBuildGridData(apvGridData);
            // 对比提交的数据和查询的是否一致(可能提交前又有新取消的单)
            errorMes = ApvGridUtils.checkSubmitGridNoStr(lessGridNoStr, lessPickNoStr, cancelGridNoStr, apvGridData);
            if (StringUtils.isNotBlank(errorMes)) {
                rsp.setMessage(errorMes);
                return rsp;
            }

            if (stockoutWhBox != null) {
                SowStockoutQueryCondition query = new SowStockoutQueryCondition();
                query.setStockoutBox(stockoutBox);
                List<SowStockout> sowStockouts = sowStockoutService.querySowStockouts(query, null);

                WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
                taskQuery.setTaskTypeList(
                        Arrays.asList(PickingTaskType.BZYC.intCode(), PickingTaskType.TRANSFER_BZYC.intCode(),
                                PickingTaskType.TEMU_BZYC.intCode(), PickingTaskType.JIT_ASN_BZYC.intCode()));
                taskQuery.setYcBoxNo(stockoutBox);
                List<WhPickingTask> taskList = whPickingTaskService.queryWhPickingTasks(taskQuery, null);

                if (CollectionUtils.isNotEmpty(sowStockouts) || CollectionUtils.isNotEmpty(taskList)) {
                    // 校验快递多品类型的只能单独的绑定在同一个周转框中
                    boolean express = sowStockouts.stream().allMatch(
                            s -> PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(s.getOldTaskType()));
                    boolean taskBool = PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode()
                            .equals(whPickingTask.getTaskType());
                    boolean allJitAsn = sowStockouts.stream().allMatch(
                            s -> PickingTaskType.JIT_ASN_MULTIPLE.intCode().equals(s.getOldTaskType()));
                    boolean taskJitAsnBool = PickingTaskType.JIT_ASN_MULTIPLE.intCode()
                            .equals(whPickingTask.getTaskType());
                    if ((taskBool && !express) || (!taskBool && express)) {
                        rsp.setMessage("中转仓多品类型的只能单独的绑定在同一个周转框中!");
                        return rsp;
                    }
                    if (taskJitAsnBool && !allJitAsn || !taskJitAsnBool && allJitAsn) {
                        rsp.setMessage("仓发多品类型的只能单独的绑定在同一个周转框中!");
                        return rsp;
                    }
                }
                // 校验播种异常周转筐是否超出了绑定APV数量
                errorMes = ApvGridUtils.checkstockoutWhBoxIsFull(stockoutWhBox, sowStockouts, apvGridData);
                if (StringUtils.isNotBlank(errorMes)) {
                    rsp.setMessage(errorMes);
                    return rsp;
                }
            }
            transferGridService.updateStockoutFinishGrid(stockoutWhBox, caYiWhBox, apvGridData);

            // 播种差异时，对播种差异的sku生成对应的盘点需求
            //whPickInventoryDemandService.generateInventoryDemand(whPickingTask);

            rsp.setStatus(StatusCode.SUCCESS);

        }
        catch (Exception e) {
            rsp.setMessage(e.getMessage());
        }

        return rsp;
    }

    /**
     * (跨仓、本仓二次配货、播种差异)任务播种完成
     *
     * @param domain
     * @param box
     * @param boxCayi
     * @param lessGridNoStr
     * @param lessPickNoStr
     * @param cancelGridNoStr
     * @return
     */
    @RequestMapping(value = "grid/finish", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson gridFinish(@ModelAttribute("domain") WhApvMoreProductsDo domain,
            @RequestParam("box") String box, @RequestParam(name = "boxCayi", required = false) String boxCayi,
            @RequestParam(name = "lessGridNoStr", required = false) String lessGridNoStr,
            @RequestParam(name = "lessPickNoStr", required = false) String lessPickNoStr,
            @RequestParam(name = "cancelGridNoStr", required = false) String cancelGridNoStr) {

        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL)) {
            responseJson.setMessage(rsp.getMessage());
            return responseJson;
        }
        String serialNumber = "";
        serialNumber = rsp.getMessage();
        String errorMes = null;

        if (StringUtils.isEmpty(serialNumber)) {
            responseJson.setMessage("找不到批次号 ！");
            return responseJson;
        }
        errorMes = ApvGridUtils.checkSubmitFinishGridData(boxCayi, lessGridNoStr);
        if (StringUtils.isNotBlank(errorMes)) {
            responseJson.setMessage(errorMes);
            return responseJson;
        }

        WhPickingTask whPickingTask = (WhPickingTask) rsp.getBody().get("whPickingTask");
        whPickingTask.setBoxNo(box);

        WhBox caYiWhBox = null;
        if (StringUtils.isNotBlank(boxCayi)) {
            caYiWhBox = whBoxService.queryWhBoxByBoxNo(boxCayi);
            // 检查提交的周转筐是否可用
            errorMes = ApvGridUtils.checkSubmitTranferCaYiWhBox(caYiWhBox, whPickingTask);
            if (StringUtils.isNotBlank(errorMes)) {
                responseJson.setMessage(errorMes);
                return responseJson;
            }
        }

        ApvGridData apvGridData = new ApvGridData();
        apvGridData.setTaskNo(serialNumber);
        apvGridData.setWhPickingTask(whPickingTask);
        apvGridData.setTaskType(whPickingTask.getTaskType());
        transferGridService.scanBoxToBuildGridData(apvGridData);

        List<WhApvGrid> whApvGrids = apvGridData.getWhApvGrids();

        // 对比提交的数据和查询的是否一致(可能提交前又有新取消的单)
        errorMes = ApvGridUtils.checkSubmitGridNoStr(lessGridNoStr, lessPickNoStr, cancelGridNoStr, apvGridData);
        if (StringUtils.isNotBlank(errorMes)) {
            responseJson.setMessage(errorMes);
            return responseJson;
        }

        Integer successGridApvCount = apvGridData.getSuccessGridApvCount() == null ? 0
                : apvGridData.getSuccessGridApvCount();
        // 检查播种差异周转筐是否用对
        errorMes = ApvGridUtils.checkCurrentCaYiWhBoxIsTure(caYiWhBox, whPickingTask.getTaskType(),
                successGridApvCount);
        if (StringUtils.isNotBlank(errorMes)) {
            responseJson.setMessage(errorMes);
            return responseJson;
        }
        // 是否需要先解绑再绑定
        boolean unbinDingBox = false;
        if (caYiWhBox != null && caYiWhBox.getStatus().equals(BoxStatus.ALREADY_USED.intCode())
                && successGridApvCount == 0) {
            unbinDingBox = true;
        }

        List<String> pickOutSkus = ApvGridUtils.getPickOutSkuList(apvGridData.getLessPickNos(), whApvGrids);
        if (CollectionUtils.isNotEmpty(pickOutSkus) || caYiWhBox != null) {
            try {
                // 拣货缺货货 or 播种差异
                if (CollectionUtils.isNotEmpty(pickOutSkus)) {
                    transferGridService.updateLessPickAndLessGridFinish(pickOutSkus, caYiWhBox, apvGridData,
                            unbinDingBox);

                }
                else {
                    transferGridService.updateLessGridFinish(caYiWhBox, apvGridData, unbinDingBox);

                }
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                responseJson.setMessage("操作失败：" + e.getMessage());
                return responseJson;
            }
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }


    /**
     * @Description 正常播种完成(无拣货缺货无播种差异)
     * <AUTHOR>
     * @date 2020/3/13 9:05
     * @version 1.0
     */
    @RequestMapping(value = "complete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson complete(@RequestParam("box") String box) {
        box = StringUtils.trim(box);
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        try {
            ResponseJson responseJson = transferGridService.normalGridComplete(box);
            if (StatusCode.FAIL.equals(responseJson.getStatus())) {
                return responseJson;
            }
            rsp.setStatus(StatusCode.SUCCESS);
        } catch (Exception e) {
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }
}
