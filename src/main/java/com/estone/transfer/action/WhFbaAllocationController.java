package com.estone.transfer.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.bean.WhApvOutStockChainCancel;
import com.estone.apv.bean.WhApvOutStockChainCancelQueryCondition;
import com.estone.apv.bean.WhApvOutStockChainQueryCondition;
import com.estone.apv.common.ApvGridStatus;
import com.estone.apv.common.ApvTaskRedisLock;
import com.estone.apv.enums.ApvTypeEnum;
import com.estone.apv.service.AliExpressCallService;
import com.estone.apv.service.WhApvOutStockChainCancelService;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.apv.enums.WhApvOutStockChainStatusEnum;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.bean.WhAsnExtraQueryCondition;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.asn.enums.AsnStatus;
import com.estone.asn.enums.CollectMethodEnum;
import com.estone.asn.service.WhAsnExtraService;
import com.estone.common.SaleChannel;
import com.estone.common.SelectJson;
import com.estone.common.config.WmsMqConfig;
import com.estone.common.enums.CountryEnum;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.upload.SeaweedFSUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.ResultModel;
import com.estone.foreign.bean.PushOmsPacData;
import com.estone.picking.enums.PickingTaskType;
import com.estone.sku.bean.*;
import com.estone.sku.domain.WhSkuDo;
import com.estone.sku.enums.PrintSkuQrCodeRedisLock;
import com.estone.sku.enums.ProcessType;
import com.estone.sku.enums.UniqueSkuFrom;
import com.estone.sku.service.SkuTagInfoService;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.sku.utils.UniqueSkuUtils;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.estone.system.param.bean.SystemParam;
import com.estone.transfer.bean.*;
import com.estone.transfer.domain.BarnSkuPrintVo;
import com.estone.transfer.domain.WhFbaAllocationDo;
import com.estone.transfer.enums.*;
import com.estone.transfer.service.*;
import com.google.common.base.Joiner;
import com.itextpdf.text.Document;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;
import jodd.util.ArraysUtil;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBlockingDeque;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping(value = "fba/allocation")
public class WhFbaAllocationController extends BaseController {
    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhFbaAllocationHandleService whFbaAllocationHandleService;

    @Resource
    private WhAsnExtraService whAsnExtraService;

    @Resource
    private WhFbaAllocationItemService whFbaAllocationItemService;

    @Resource
    private TransferStockService transferStockService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    @Resource
    private WhApvOutStockChainCancelService whApvOutStockChainCancelService;

    @Resource
    private WhFbaChangeService whFbaChangeService;

    @Resource
    private SkuTagInfoService skuTagInfoService;

    @Resource
    private AliExpressCallService aliExpressCallService;

    @Resource
    private AsnPickBoxService asnPickBoxService;

    @Resource
    private JitPickupOrderService jitPickupOrderService;

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private SmtAidcSettlementService smtAidcSettlementService;

    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    @Resource(name = "smtQueryOrderBlockingDeque")
    private RBlockingDeque<String> rBlockingDeque;

    private static final String STATIC_FILE_PATH = "/usr/local/erp/static";
    private static final String FIRST_ASN_PRINT_SKU_VIEW = "transfer/firstAsnPrintSku";

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhFbaAllocationDo domain) {
        initFormData(domain);
        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_FIRST.getCode())) {
            return "transfer/asnOrderList";
        } else if(StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_TRANSFER.getCode())){
            return "transfer/fbaTransferOrderList";
        }else if(StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())) {
            return "transfer/jitAsnOrderList";
        }else {
            return "transfer/fbaAllocationList";
        }
    }

    private void initFormData(@ModelAttribute("domain") WhFbaAllocationDo domain) {

        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_FIRST.getCode())) {
            Object[] statusArray = Arrays.asList(AsnPrepareStatus.values()).stream()
                    .filter(p -> !p.getCode().equalsIgnoreCase(AsnPrepareStatus.WAITING_LABEL.getCode())
                            && !p.getCode().equalsIgnoreCase(AsnPrepareStatus.WAITING_CHECK.getCode()))
                    .collect(Collectors.toList()).toArray();
            domain.setStatusJson(SelectJson.getList(statusArray));
        } else if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_TRANSFER.getCode())) {
            Object[] statusArray = Arrays.asList(AsnPrepareStatus.WAITING_GEN, AsnPrepareStatus.SINGLETON_TOUCHING,
                    AsnPrepareStatus.EXCESSIVE_PARTS_TOUCHING, AsnPrepareStatus.MULTI_TOUCHING,AsnPrepareStatus.WAITING_GRID,
                    AsnPrepareStatus.CHECK_PRINT, AsnPrepareStatus.PICK_STOCK_OUT,
                    AsnPrepareStatus.WAITING_DELIVER, AsnPrepareStatus.DELIVER,
                    AsnPrepareStatus.LOADED, AsnPrepareStatus.CANCEL).toArray();
            domain.setStatusJson(SelectJson.getList(statusArray));
            Object[] pickBoxStatusArray = Arrays.asList(new SelectJson(ApvGridStatus.PENDING.getCode(),"否"),
                    new SelectJson(ApvGridStatus.COMPLETED.getCode(),"是")).toArray();
            domain.setPickBoxStatusJson(JSON.toJSONString(pickBoxStatusArray));
        } else if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())) {
            Object[] statusArray = Arrays.asList(AsnPrepareStatus.WAITING_GEN, AsnPrepareStatus.SINGLEMULTIPLE_TOUCHING,
                     AsnPrepareStatus.MULTI_TOUCHING,AsnPrepareStatus.WAITING_GRID,
                    AsnPrepareStatus.CHECK_PRINT, AsnPrepareStatus.PICK_STOCK_OUT,
                    AsnPrepareStatus.WAITING_DELIVER, AsnPrepareStatus.DELIVER,
                    AsnPrepareStatus.LOADED, AsnPrepareStatus.CANCEL).toArray();
            domain.setStatusJson(SelectJson.getList(statusArray));
            Object[] tagArray = Arrays.asList(AsnTagStatus.NORMAL_BACKUP, AsnTagStatus.URGENT_BACKUP).toArray();
            domain.setTagsJson(SelectJson.getList(tagArray));
            domain.setCollectJson(SelectJson.getList(CollectMethodEnum.values()));
        } else {
            Object[] statusArray = Arrays.asList(AsnPrepareStatus.values()).stream()
                    .filter(p -> !p.getCode().equalsIgnoreCase(AsnPrepareStatus.PICK_STOCK_OUT.getCode()))
                    .collect(Collectors.toList()).toArray();
            domain.setStatusJson(SelectJson.getList(statusArray));
        }
        SystemParam param = CacheUtils.SystemParamGet("ALLOCATION.ASN_SHIPPING_METHOD");
        if (param != null && StringUtils.isNotBlank(param.getParamValue())) {
            // "韵达,中通,圆通,申通,百世易通, 顺丰,德邦"
            domain.setShippingMethodList(Arrays.asList(StringUtils.split(param.getParamValue(), ",")));
        } else {
            log.warn("请配置快海外仓出单交运物流公司选项后重试:ALLOCATION.ASN_SHIPPING_METHOD");
        }
        List<String> saleChannelList = SaleChannel.saleChannels;
        if(StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_TRANSFER.getCode())){
            saleChannelList = Arrays.asList(SaleChannel.CHANNEL_SMT,SaleChannel.CHANNEL_SHEIN);
        }
        domain.setSaleChannelList(saleChannelList);
        List<SelectJson> selectJsons = new ArrayList<>();
        for (String channel : saleChannelList) {
            selectJsons.add(new SelectJson(channel, channel));
        }
        domain.setSaleChannelJson(JSON.toJSONString(selectJsons));


        Object[] packageMethodEnums = AsnPackageMethodEnum.values();
        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_TRANSFER.getCode())){
            packageMethodEnums = Arrays.asList(AsnPackageMethodEnum.JIT,AsnPackageMethodEnum.JIT_HALF,
                    AsnPackageMethodEnum.URGENT,AsnPackageMethodEnum.BACKUP)
                    .toArray();
        }
        domain.setTypeJson(SelectJson.getList(packageMethodEnums));

        domain.setApvTypeJson(SelectJson.getList(ApvTypeEnum.values()));
    }

    private void queryWhFbaAllocations(@ModelAttribute("domain") WhFbaAllocationDo domain) {
        WhFbaAllocationQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhFbaAllocationQueryCondition();
            domain.setQuery(query);
        }
        query.setOrderType(domain.getOrderType());
        Pager page = domain.getPage();
        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_FIRST.getCode())) {
            query.setIsAmazonFba(false);
            query.setQueryWhAsnExtra(true);
            query.setQueryAsnPickBoxNumber(true);
        }else if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_TRANSFER.getCode())){
            query.setIsTransferOrder(true);
            query.setQueryWhAsnExtra(true);
        }else if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())){
            query.setIsTransferOrder(true);
            query.setQueryWhAsnExtra(true);
            query.setQueryAsnPickBoxNumber(true);
            if(StringUtils.isNotBlank(query.getQueryOrderType()) && StringUtils.isNotBlank(query.getQueryOrderTypeVal())) {
                switch (query.getQueryOrderType()) {
                    case "YST":
                        query.setFbaNo(query.getQueryOrderTypeVal());
                        break;
                    case "SHIPMENTID":
                        query.setShipmentId(query.getQueryOrderTypeVal());
                        break;
                    case "CONSIGNORDERNO":
                        query.setConsignOrderNoStr(query.getQueryOrderTypeVal());
                        break;
                    case "PICKUPORDERID":
                        query.setPickupOrderId(query.getQueryOrderTypeVal());
                        break;
                    case "LBX":
                        query.setLbxNo(query.getQueryOrderTypeVal());
                        break;
                    case "SHIPPINGORDERNO":
                        query.setShippingOrderNo(query.getQueryOrderTypeVal());
                        break;
                }
            }
        } else {
            query.setIsAmazonFba(true);
            query.setQueryShipmentIds(true);
        }
        if (CollectionUtils.isEmpty(query.getStatusList()) && query.getStatus() == null) {
            query.setStatusList(AsnPrepareStatus.getExcludeAllotCode());
        }
        if (StringUtils.isNotEmpty(query.getTrackingNumber())
                && !StringUtils.contains(query.getTrackingNumber(), ",")) {
            query.setRightLikeTrackingNumber(query.getTrackingNumber());
        }
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, page);
        if (CollectionUtils.isNotEmpty(whFbaAllocations)) {
            List<Integer> boxed = List.of(11, 13, 17, 18);
            boolean isFba = Boolean.TRUE.equals(query.getIsAmazonFba());
            whFbaAllocations.forEach(allocation -> {
                if (isFba || SaleChannel.CHANNEL_SMT.equalsIgnoreCase(allocation.getPurposeHouse())) {
                    Iterator<WhFbaAllocationItem> iterator = allocation.getItems().iterator();
                    while (iterator.hasNext()) {
                        WhFbaAllocationItem next = iterator.next();
                        if (boxed.contains(allocation.getStatus()) && (next.getLoadingQuantity() == null || next.getLoadingQuantity() <= 0)) {
                            iterator.remove();
                        }
                    }
                };
                if (isFba) {
                    allocation.buildFbaItem();
                } else {
                    Map<String, Map<String, List<WhFbaAllocationItem>>> boxMap = allocation.getItems().stream()
                            .filter(i -> i.getId() != null)
                            .collect(Collectors.groupingBy(
                                    item -> item.getBoxNo() == null ? "null" : item.getBoxNo().toString(), HashMap::new,
                                    Collectors.groupingBy(WhFbaAllocationItem::getFnSku, HashMap::new,
                                            Collectors.toList())));
                    allocation.buildGroupItems();
                    allocation.setBoxMap(boxMap);
                }
            });
        }
        // smt仓发页面需要查询分摊揽收费用
        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())){
            List<String> icoNoList = whFbaAllocations.stream().flatMap(allocation -> allocation.getItems().stream())
                    .filter(i -> org.apache.commons.lang3.StringUtils.isNotBlank(i.getTag())).map(i -> i.getTag()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(icoNoList)) {
                // 查询已经同步回来的发货单
                SmtAidcSettlementQueryCondition smtAidcSettlementQueryCondition = new SmtAidcSettlementQueryCondition();
                smtAidcSettlementQueryCondition.setBizBillCodeList(icoNoList);
                smtAidcSettlementQueryCondition.setReadOnly(true);
                List<SmtAidcSettlement> existSmtAidcSettlements = smtAidcSettlementService.querySmtAidcSettlements(smtAidcSettlementQueryCondition, null);
                if (CollectionUtils.isNotEmpty(existSmtAidcSettlements)) {
                    Map<String, BigDecimal> settleAmountMap = existSmtAidcSettlements.stream().collect(Collectors.toMap(SmtAidcSettlement::getBizBillCode
                            , SmtAidcSettlement::getSettleAmount, (k1, k2) -> k2));
                    for (WhFbaAllocation whFbaAllocation:whFbaAllocations) {
                        if (CollectionUtils.isNotEmpty(whFbaAllocation.getItems()) && StringUtils.isNotBlank(whFbaAllocation.getItems().get(0).getTag()))
                            whFbaAllocation.setSettleAmount(settleAmountMap.get(whFbaAllocation.getItems().get(0).getTag()));
                    }
                }

            }
        }
        domain.setWhFbaAllocations(whFbaAllocations);
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhFbaAllocationDo domain) {
        initFormData(domain);
        queryWhFbaAllocations(domain);
        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_FIRST.getCode())) {
            List<AsnPrepareStatus> collect = Arrays.stream(AsnPrepareStatus.values()).filter(c -> !StringUtils.equals("11", c.getCode())).collect(Collectors.toList());
            Object[] objects = collect.toArray();
            domain.setStatusJson(SelectJson.getList(objects));
            return "transfer/asnOrderList";
        }
        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.ASN_TRANSFER.getCode())) {
            return "transfer/fbaTransferOrderList";
        }
        if (StringUtils.isNotEmpty(domain.getOrderType())
                && domain.getOrderType().equals(PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())) {
            return "transfer/jitAsnOrderList";
        }
        return "transfer/fbaAllocationList";
    }

    // 分配
    @RequestMapping(value = "allot", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson allot(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        List<String> failedList = new ArrayList<>();
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setIds(ids);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            response.setMessage("符合操作条件的数据为0！");
            return response;
        }
        for (WhFbaAllocation allocation : allocationList) {
            try {
                whFbaAllocationHandleService.exeAllot(allocation);
            } catch (Exception e) {
                failedList.add(String.format("%s,%s", allocation.getId(), e.getMessage()));
                log.error(e.getMessage(), e);
            }
        }
        // 存在失败的项
        response.setMessage(String.format("成功[%s]条，失败[%s]条，失败详情【%s】", ids.size() - failedList.size(), failedList.size(),
                StringUtils.join(failedList, ";")));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 生成
    @RequestMapping(value = "createPickingTask", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson createPickingTask(@RequestParam("ids") List<Integer> ids,
                                          @RequestParam(value = "orderType", required = false) String orderType) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        String key = StringUtils.isNotEmpty(orderType)
                && orderType.equalsIgnoreCase(PickingTaskType.ASN_FIRST.getCode())
                ? ApvTaskRedisLock.ASN_FIRST_TOUCHING.getName()
                : ApvTaskRedisLock.ASN_PREPARE_TOUCHING.getName();
        try {
            if (JedisUtils.exists(key + "-" + PickingTaskType.ASN_PREPARE.intCode())) {
                response.setMessage("有账号正在合单，请稍后再合！");
                return response;
            } else {
                JedisUtils.set(key + "-" + PickingTaskType.ASN_PREPARE.intCode(), "lock", 600L);
            }

            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            query.setIds(ids);
            query.setStatus(AsnPrepareStatus.WAITING_GEN.intCode());
            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isEmpty(allocationList)) {
                response.setMessage("符合操作条件的数据为0！");
                return response;
            }
            // 移除 allocationList中明细里allotQuantity为0的明细
            // 并且移除移除明细后allocationList中明细为空的allocation
            allocationList = allocationList.stream().filter(allocation -> {
                List<WhFbaAllocationItem> items = allocation.getItems();
                List<WhFbaAllocationItem> collect = items.stream()
                        .filter(item -> item.getAllotQuantity() != null && item.getAllotQuantity() > 0)
                        .collect(Collectors.toList());
                allocation.setItems(collect);
                return CollectionUtils.isNotEmpty(collect);
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allocationList)) {
                response.setMessage("符合操作条件的数据为0！");
                return response;
            }
            Integer tempInteger = whFbaAllocationHandleService.createPickingTask(allocationList);
            if (tempInteger > 0) {
                // 推送amazon待拣货状态到oms
                for (WhFbaAllocation whFbaAllocation: allocationList) {
                    if (whFbaAllocation.isFba() && AsnPrepareStatus.WAITING_PICK.intCode().equals(whFbaAllocation.getStatus())) {
                        whFbaAllocationService.sendMsg(whFbaAllocation);
                    }
                }
                response.setStatus(StatusCode.SUCCESS);
                response.setMessage("生成任务成功：" + tempInteger + "条记录！");
            } else {
                response.setMessage("生成任务失败！");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            JedisUtils.del(key + "-" + PickingTaskType.ASN_PREPARE.intCode());
        }

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 海外仓合单拣货任务生成（优化版）
     * 
     * @param ids 选中的发货单ID列表
     * @param orderType 订单类型
     * @param selectionType 单据类型选择：ALL_SINGLE(所有单品)、ALL_MULTIPLE(所有多品)、SELECTED(当前勾选)
     * @param locationLimit 货位数上限，默认50，必须大于0的正整数
     * @param pcsLimit PCS上限，默认1000，必须大于0的正整数
     * @return ResponseJson
     */
    @RequestMapping(value = "createPickingTaskAdvanced", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson createPickingTaskAdvanced(@RequestParam(value = "ids", required = false) List<Integer> ids,
                                                  @RequestParam(value = "orderType", required = false) String orderType,
                                                  @RequestParam("selectionType") String selectionType,
                                                  @RequestParam(value = "locationLimit", defaultValue = "50") Integer locationLimit,
                                                  @RequestParam(value = "pcsLimit", defaultValue = "1000") Integer pcsLimit) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        // 参数校验
        if (StringUtils.isBlank(selectionType)) {
            response.setMessage("请选择单据类型！");
            return response;
        }
        
        if (locationLimit == null || locationLimit <= 0) {
            response.setMessage("货位数上限必须大于0！");
            return response;
        }
        
        if (pcsLimit == null || pcsLimit <= 0) {
            response.setMessage("PCS上限必须大于0！");
            return response;
        }

        String key = StringUtils.isNotEmpty(orderType)
                && orderType.equalsIgnoreCase(PickingTaskType.ASN_FIRST.getCode())
                ? ApvTaskRedisLock.ASN_FIRST_TOUCHING.getName()
                : ApvTaskRedisLock.ASN_PREPARE_TOUCHING.getName();
                
        try {
            if (JedisUtils.exists(key + "-" + PickingTaskType.ASN_PREPARE.intCode())) {
                response.setMessage("有账号正在合单，请稍后再合！");
                return response;
            } else {
                JedisUtils.set(key + "-" + PickingTaskType.ASN_PREPARE.intCode(), "lock", 600L);
            }

            // 根据selectionType确定查询条件
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            query.setStatus(AsnPrepareStatus.WAITING_GEN.intCode());
            query.setIsAmazonFba(false);
            query.setOrderType(PickingTaskType.ASN_FIRST.getCode());
            query.setQueryWhAsnExtra(true);
            switch (selectionType) {
                case "ALL_SINGLE":
                    // 查询所有单品类型（SS/SM）
                    query.setApvType(ApvTypeEnum.SS.getCode()+","+ApvTypeEnum.SM.getCode());
                    break;
                case "ALL_MULTIPLE":
                    // 查询所有多品类型（MM）
                    query.setApvType(ApvTypeEnum.MM.getCode());
                    break;
                case "SELECTED":
                    // 使用选中的ids
                    if (CollectionUtils.isEmpty(ids)) {
                        response.setMessage("请选择要生成任务的发货单！");
                        return response;
                    }
                    query.setIds(ids);
                    break;
                default:
                    response.setMessage("不支持的单据类型选择！");
                    return response;
            }

            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isEmpty(allocationList)) {
                response.setMessage("符合操作条件的数据为0！");
                return response;
            }

            // 如果是选中模式，需要进行类型校验
            if ("SELECTED".equals(selectionType)) {
                String validationError = validateSelectedOrderTypes(allocationList);
                if (StringUtils.isNotBlank(validationError)) {
                    response.setMessage(validationError);
                    return response;
                }
            }

            // 过滤有效的明细数据
            allocationList = allocationList.stream().filter(allocation -> {
                List<WhFbaAllocationItem> items = allocation.getItems();
                List<WhFbaAllocationItem> collect = items.stream()
                        .filter(item -> item.getAllotQuantity() != null && item.getAllotQuantity() > 0)
                        .collect(Collectors.toList());
                allocation.setItems(collect);
                return CollectionUtils.isNotEmpty(collect);
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(allocationList)) {
                response.setMessage("符合操作条件的数据为0！");
                return response;
            }

            // 按照货位数和PCS限制创建拣货任务
            Integer tempInteger = createPickingTaskWithLimits(allocationList, locationLimit, pcsLimit);
            
            if (tempInteger > 0) {
                // 推送amazon待拣货状态到oms
                for (WhFbaAllocation whFbaAllocation: allocationList) {
                    if (whFbaAllocation.isFba() && AsnPrepareStatus.WAITING_PICK.intCode().equals(whFbaAllocation.getStatus())) {
                        whFbaAllocationService.sendMsg(whFbaAllocation);
                    }
                }
                response.setStatus(StatusCode.SUCCESS);
                response.setMessage("生成任务成功：" + tempInteger + "条记录！");
            } else {
                response.setMessage("生成任务失败！");
            }

        } catch (Exception e) {
            log.error("海外仓合单任务生成失败", e);
            response.setMessage("生成任务失败：" + e.getMessage());
        } finally {
            JedisUtils.del(key + "-" + PickingTaskType.ASN_PREPARE.intCode());
        }

        return response;
    }

    /**
     * 校验选中订单的类型一致性
     * 
     * @param allocationList 发货单列表
     * @return 错误信息，无错误时返回null
     */
    private String validateSelectedOrderTypes(List<WhFbaAllocation> allocationList) {
        Set<String> apvTypes = allocationList.stream()
                .map(WhFbaAllocation::getApvType)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
                
        if (apvTypes.isEmpty()) {
            return "选中的发货单类型信息不完整！";
        }
        
        // 检查是否混合了单品和多品类型
        boolean hasSingleTypes = apvTypes.stream().anyMatch(type -> ApvTypeEnum.SS.getCode().equals(type) || ApvTypeEnum.SM.getCode().equals(type));
        boolean hasMultipleTypes = apvTypes.stream().anyMatch(type -> ApvTypeEnum.MM.getCode().equals(type));
        
        if (hasSingleTypes && hasMultipleTypes) {
            return "选中的发货单类型不一致，不能混合单品(SS/SM)和多品(MM)类型！";
        }
        
        return null; // 校验通过
    }

    /**
     * 按照货位数和PCS限制创建拣货任务
     * 参考中转仓合单逻辑，根据货位和PCS生成拣货任务
     * 
     * @param allocationList 发货单列表
     * @param locationLimit 货位数上限
     * @param pcsLimit PCS上限
     * @return 生成的任务数量
     */
    private Integer createPickingTaskWithLimits(List<WhFbaAllocation> allocationList, Integer locationLimit, Integer pcsLimit) {
        log.info("开始创建海外仓拣货任务，发货单数量: {}, 货位数上限: {}, PCS上限: {}", allocationList.size(), locationLimit, pcsLimit);
        
        // 首先为发货单匹配库位信息（参考 WhAsnServiceImpl#createPickingTask）
        boolean bool = whApvOutStockChainService.matchTransferOutStockChainList(allocationList, WhApvOutStockChainStatusEnum.ALLOT);
        if (!bool) {
            log.error("发货单匹配库位信息失败");
            return -1;
        }

        // 匹配库位信息后，根据getGroupItems的条目数量进行排序（由少到多）
        allocationList.sort((a1, a2) -> {
            int size1 = CollectionUtils.isEmpty(a1.getGroupItems()) ? 0 : a1.getGroupItems().size();
            int size2 = CollectionUtils.isEmpty(a2.getGroupItems()) ? 0 : a2.getGroupItems().size();
            return Integer.compare(size1, size2);
        });
        
        List<List<WhFbaAllocation>> taskGroups = new ArrayList<>();
        List<WhFbaAllocation> currentGroup = new ArrayList<>();
        Set<String> currentLocationSet = new HashSet<>(); // 当前组涉及的所有库位集合
        int currentPcsCount = 0;
        
        for (WhFbaAllocation allocation : allocationList) {
            // 计算当前发货单的PCS数量
            int allocationPcs = allocation.getGroupItems().stream()
                    .mapToInt(item -> item.getAllotQuantity() != null ? item.getAllotQuantity() : 0)
                    .sum();
            
            // 获取当前发货单涉及的库位集合
            Set<String> allocationLocationSet = getAllocationLocationSet(allocation);
            
            // 如果单个发货单的PCS就超过限制，单独成为一个拣货任务
            if (allocationPcs > pcsLimit) {
                if (!currentGroup.isEmpty()) {
                    taskGroups.add(new ArrayList<>(currentGroup));
                    currentGroup.clear();
                    currentLocationSet.clear(); // 清空库位集合
                    currentPcsCount = 0;
                }
                taskGroups.add(Arrays.asList(allocation));
                log.info("发货单[{}]PCS数量[{}]超过限制[{}]，单独创建拣货任务", allocation.getFbaNo(), allocationPcs, pcsLimit);
                continue;
            }
            
            // 计算合并后的库位集合大小（去重后的库位数量）
            Set<String> mergedLocationSet = new HashSet<>(currentLocationSet);
            mergedLocationSet.addAll(allocationLocationSet);
            
            // 检查加入当前组是否超过限制
            if (currentPcsCount + allocationPcs > pcsLimit || 
                mergedLocationSet.size() > locationLimit) {
                // 超过限制，当前组结束，开始新组
                if (!currentGroup.isEmpty()) {
                    taskGroups.add(new ArrayList<>(currentGroup));
                    log.info("当前组达到限制，创建拣货任务组：发货单数[{}]，库位数[{}]，PCS数[{}]", 
                           currentGroup.size(), currentLocationSet.size(), currentPcsCount);
                }
                // 重置当前组
                currentGroup = new ArrayList<>();
                currentLocationSet.clear(); // 清空库位集合
                currentPcsCount = 0;
            }
            
            // 将当前发货单加入当前组
            currentGroup.add(allocation);
            currentLocationSet.addAll(allocationLocationSet); // 合并库位集合
            currentPcsCount += allocationPcs;
            
            log.debug("发货单[{}]加入当前组，当前组：发货单数[{}]，库位数[{}]，PCS数[{}]", 
                     allocation.getFbaNo(), currentGroup.size(), currentLocationSet.size(), currentPcsCount);
        }
        
        // 处理最后一组
        if (!currentGroup.isEmpty()) {
            taskGroups.add(currentGroup);
            log.info("最后一组创建拣货任务组：发货单数[{}]，库位数[{}]，PCS数[{}]", 
                   currentGroup.size(), currentLocationSet.size(), currentPcsCount);
        }
        
        log.info("拆分完成，共生成{}个拣货任务组", taskGroups.size());
        
        // 为每个组创建拣货任务
        int totalTasks = 0;
        for (List<WhFbaAllocation> group : taskGroups) {
            try {
                Integer taskCount = whFbaAllocationHandleService.createPickingTask(group);
                if (taskCount != null && taskCount > 0) {
                    totalTasks += taskCount;
                }
            } catch (Exception e) {
                log.error("创建拣货任务失败，组大小: {}", group.size(), e);
            }
        }
        
        log.info("海外仓拣货任务创建完成，总任务数: {}", totalTasks);
        return totalTasks;
    }

    /**
     * 获取发货单涉及的货位集合
     * 参考 WhAsnServiceImpl#getLocationCount 实现，通过WhApvOutStockChain获取准确的货位信息
     * 
     * @param allocation 发货单
     * @return 货位号集合
     */
    private Set<String> getAllocationLocationSet(WhFbaAllocation allocation) {
        Set<String> locationNumbers = new HashSet<>();
        if (allocation == null) {
            return locationNumbers;
        }
        
        // 使用 getGroupItems() 获取items
        List<WhFbaAllocationItem> items = allocation.getGroupItems();
        if (CollectionUtils.isEmpty(items)) {
            return locationNumbers;
        }
        
        // 从WhApvOutStockChain中获取货位信息
        for (WhFbaAllocationItem item : items) {
            if (CollectionUtils.isNotEmpty(item.getWhApvOutStockChains())) {
                item.getWhApvOutStockChains().stream()
                    .map(WhApvOutStockChain::getLocationNumber)
                    .filter(Objects::nonNull)
                    .filter(StringUtils::isNotBlank)
                    .forEach(locationNumbers::add);
            }
        }
        
        return locationNumbers;
    }

    /**
     * 打印SKU标签
     *
     * @param domain
     * @param id
     * @return
     */
    @GetMapping(value = "toPrintSKU")
    public String toPrintSKU(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id,
                             @RequestParam(value = "fnSku", required = false) String fnSku) {
        if (id == null) {
            return "transfer/printSku";
        }
        assembleFnSkuMap(domain, id, fnSku);
        return "transfer/toPrintSku";
    }

    private void assembleFnSkuMap(WhFbaAllocationDo domain, Integer id, String fnSku) {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setFnSku(fnSku);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(allocationList) && allocationList.get(0) != null
                && CollectionUtils.isNotEmpty(allocationList.get(0).getItems())) {
            WhFbaAllocation fbaAllocation = allocationList.get(0);
            // 根据fnSku分组,然后根据sku去重
            Map<String, List<WhFbaAllocationItem>> fnSkuMap = fbaAllocation.getItems().stream().collect(
                    Collectors.groupingBy(i -> i.getFnSku(), Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<WhFbaAllocationItem>(Comparator.comparing(o -> o.getProductSku()))),
                            ArrayList::new)));
            domain.setFnSkuMap(fnSkuMap);
            WhFbaAllocation allocation = new WhFbaAllocation();
            allocation.setId(id);
            domain.setWhFbaAllocation(allocation);
            if (StringUtils.isNotEmpty(fnSku)) {
                Map<String, List<WhFbaAllocationItem>> map = new HashMap<>();
                map.put(fnSku, fnSkuMap.get(fnSku));
                domain.setFnSkuMap(map);
            }
            if (!fbaAllocation.isFba()) {
                domain.setOrderType(PickingTaskType.ASN_FIRST.getCode());
            }

            WhAsnExtra asnExtra = fbaAllocation.getWhAsnExtra();
            boolean isBarn = Objects.nonNull(asnExtra) && Objects.equals(asnExtra.getPackageMethod(), AsnPackageMethodEnum.BARN.getCode());
            domain.setHasBarnOrder(isBarn);
        }
    }

    /**
     * 打印SKU标签
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printSKU", method = {RequestMethod.POST})
    public ModelAndView printSKU(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id,
                                 @RequestParam("allocationStr") String allocationStr) {
        ModelAndView modelAndView = new ModelAndView("transfer/printSku");
        if (id == null) {
            return modelAndView;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);

        if (CollectionUtils.isEmpty(allocationList)) {
            modelAndView.addObject(domain);
            return modelAndView;
        }

        WhFbaAllocation allocation = JSONObject.parseObject(allocationStr, WhFbaAllocation.class);
        WhFbaAllocation exist = allocationList.get(0);
        if (!exist.isFba()) {
            domain.setOrderType(PickingTaskType.ASN_FIRST.getCode());
        }

        Integer packageMethod = Optional.ofNullable(exist.getWhAsnExtra())
                .orElse(new WhAsnExtra())
                .getPackageMethod();
        boolean isBarn = Objects.equals(AsnPackageMethodEnum.BARN.getCode(), packageMethod);
        //谷仓海外仓的单需要调用第三方接口获取到对应的sku标签数据
        if (isBarn) {
            try {
                List<BarnSkuPrintVo> barnSkuPrintVoList = whFbaAllocationHandleService.generateBarnPrintSkuList(allocation.getItems());
                domain.setSkuPrintsURL(barnSkuPrintVoList);
                SystemLogUtils.FBAALLOCATIONLOG.log(exist.getId(), "谷仓海外仓调拨发货单打印SKU");
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            modelAndView.setViewName("transfer/batchPrintSku");
            modelAndView.addObject(domain);
            return modelAndView;
        }

        List<WhFbaAllocationItem> items = allocation.getItems();
        List<WhFbaAllocationItem> printList = new ArrayList<>();
        for (WhFbaAllocationItem item : items) {
            for (int i = 0; i < item.getQuantity(); i++) {
                printList.add(item);
            }
        }
        exist.setItems(printList);
        domain.setWhFbaAllocation(exist);
        SystemLogUtils.FBAALLOCATIONLOG.log(exist.getId(), "FBA调拨发货单打印SKU");
        modelAndView.addObject(domain);
        return modelAndView;
    }

    /**
     * 打印FNSKU标签
     *
     * @param fnskuName
     * @param printNum
     * @return
     */
    @RequestMapping(value = "toPrintFNSKU")
    @ResponseBody
    public ResponseJson toPrintFNSKU(@RequestParam("fnskuName") String fnskuName,
                                     @RequestParam("printNum") Integer printNum) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (StringUtils.isEmpty(fnskuName) || printNum == null) {
            responseJson.setMessage("打印的FNSKU或打印数量不能为空");
            return responseJson;
        }
        WhFbaAllocationItemQueryCondition itemQueryCondition = new WhFbaAllocationItemQueryCondition();
        itemQueryCondition.setFnSku(fnskuName.trim());
        List<WhFbaAllocationItem> whFbaAllocationItems = whFbaAllocationItemService
                .queryWhFbaAllocationItems(itemQueryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationItems)) {
            responseJson.setMessage("FNSKU不存在!");
            return responseJson;
        }

        // 过滤fnsku数量小于0
        List<Integer> fbaIds = new ArrayList<>();
        for (WhFbaAllocationItem whFbaAllocationItem : whFbaAllocationItems) {
            WhApvOutStockChainQueryCondition queryCondition = new WhApvOutStockChainQueryCondition();
            queryCondition.setApvId(whFbaAllocationItem.getFbaId());
            queryCondition.setSku(whFbaAllocationItem.getProductSku());
            queryCondition.setOrderType(AssetOrderType.ASN_PREPARE_ORDER.intCode());
            List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService.queryWhApvOutStockChains(queryCondition, null);
            List<Integer> stockIds = whApvOutStockChains.stream().map(w -> w.getStockId()).collect(Collectors.toList());
            WhApvOutStockChainCancelQueryCondition cancelQueryCondition = new WhApvOutStockChainCancelQueryCondition();
            cancelQueryCondition.setApvId(whFbaAllocationItem.getFbaId());
            cancelQueryCondition.setSku(whFbaAllocationItem.getProductSku());
            cancelQueryCondition.setOrderType(AssetOrderType.ASN_PREPARE_ORDER.intCode());
            List<WhApvOutStockChainCancel> whApvOutStockChainCancels = whApvOutStockChainCancelService.queryWhApvOutStockChainCancels(cancelQueryCondition, null);
            stockIds.addAll(whApvOutStockChainCancels.stream().map(w -> w.getStockId()).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(stockIds)) {
                continue;
            }
            TransferStockQueryCondition stockQueryCondition = new TransferStockQueryCondition();
            stockQueryCondition.setIds(stockIds);
            List<TransferStock> transferStockDetails = transferStockService.queryTransferStocks(stockQueryCondition,
                    null);
            if (CollectionUtils.isEmpty(transferStockDetails)) {
                continue;
            }
            int pickReturnQuantity = transferStockDetails.stream().mapToInt(a -> Optional.ofNullable(a.getPickReturnQuantity()).orElse(0)).sum();
            if (pickReturnQuantity > 0) {
                WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
                whSkuQueryCondition.setSku(whFbaAllocationItem.getProductSku());
                List<WhSku> whSkuList = whSkuService.querySkuAndQcCategory(whSkuQueryCondition, null);
                if (CollectionUtils.isNotEmpty(whSkuList)) {
                    fbaIds.add(whSkuList.get(0).getId());
                }
            }
        }
        if (CollectionUtils.isEmpty(fbaIds)) {
            responseJson.setStatus(StatusCode.UNAUTHORIZED);
            return responseJson;
        }
        String ids = Joiner.on(",").join(fbaIds);
        responseJson.setMessage(ids);
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    /**
     * 打印FNSKU标签
     *
     * @param domain
     * @param outIds
     * @return
     */
    @RequestMapping(value = "printFNSKU", method = {RequestMethod.POST})
    public ModelAndView printSkuQRCode(@ModelAttribute("domain") WhSkuDo domain, @RequestParam("outIds") String outIds,
                                       @RequestParam("printNum") Integer printNum) {
        ModelAndView modelAndView = new ModelAndView("sku/batchPrintSkuQRcode");
        if (outIds == null || printNum == null) {
            return modelAndView;
        }
        WhSkuQueryCondition query = new WhSkuQueryCondition();
        List<Integer> outIdList = Arrays.asList(outIds.split(",")).stream().map(s -> Integer.parseInt(s.trim()))
                .collect(Collectors.toList());
        query.setIds(outIdList);
        List<WhSku> whSkuList = whSkuService.queryWhSkus(query, null);

        if (CollectionUtils.isNotEmpty(outIdList)) {
            domain.setPrintSkuUser(DataContextHolder.getUsername());// 打印sku的操作员
            domain.setPrintType(1);// 批量打印SKU
            for (WhSku whSku : whSkuList) {
                try {
                    if (RedissonLockUtil.tryLock(PrintSkuQrCodeRedisLock.EXISTS_SKU_QR_CODE.getKey() + whSku.getSku(),
                            1, PrintSkuQrCodeRedisLock.EXISTS_SKU_QR_CODE.getTimeout())) {
                        String uuid = whSku.getSku();
                        String code = "";
                        int uuidCode = 0;
                        String dateStr = DateUtils.dateToString(new Date(), "yyMMdd");
                        String historySkuUuid = UniqueSkuUtils.getMaxUuid(whSku.getSku());
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(historySkuUuid)) {
                            String historyDate = historySkuUuid.substring(0, 6);
                            uuidCode = Integer.parseInt(historySkuUuid.substring(6));
                            if (!historyDate.equals(dateStr)) {
                                UniqueSkuUtils.del(whSku.getSku());
                                uuidCode = 0;
                            }
                        }
                        // 默认打印份数为1
                        if (printNum == null) {
                            printNum = 1;
                        }
                        String skuName = whSku.getName();
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(skuName)) {
                            int length = skuName.length() > 14 ? 14 : skuName.length();
                            skuName = skuName.substring(0, length);
                        }
                        String maxUuid = null;
                        for (int j = 0; j < printNum; j++) {
                            int incremen = j + 1 + uuidCode;
                            String incremenStr;
                            if (WarehousePropertyEnum.NN.intCode() == CacheUtils.getLocalWarehouseId() && incremen < 100000){
                                incremenStr = String.format("1%05d", incremen);// 字符串格式化.
                            } else {
                                incremenStr = String.format("%06d", incremen);// 字符串格式化.
                            }
                            code = dateStr + incremenStr;// 日期+序列号
                            String uuidSku = uuid + "=" + code;// 唯一码：SKU=日期+序列号
                            PrintCheckInSku printCheckInSku = new PrintCheckInSku();
                            printCheckInSku.setBusinessId(0);// 关联ID
                            printCheckInSku.setSku(whSku.getSku());
                            printCheckInSku.setSkuName(skuName);
                            String location = whSku.getLocationNumber();
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(location)) {
                                int length = location.length() >= 12 ? location.length() : 11;
                                if (length > 11) {
                                    location = location.substring(0, length - 6);
                                }
                            }
                            printCheckInSku.setLocation(location);
                            printCheckInSku.setWarehouseId(whSku.getWarehouseId());
                            printCheckInSku.setUuid(uuidSku);
                            printCheckInSku.setCode(code);
                            printCheckInSku.setSourceFrom(UniqueSkuFrom.SKU_REPLENISH.intCode());
                            domain.getPrintSku().add(printCheckInSku);
                            maxUuid = code;
                        }
                        UniqueSkuUtils.updateMaxUuid(whSku.getSku(), maxUuid);
                        List<WhUniqueSku> whUniqueSkus = UniqueSkuUtils.buildWhUniqueSku(domain.getPrintSku());
                        whUniqueSkus = whUniqueSkus.stream().map(whUniqueSku -> {
                            whUniqueSku.setScanFnSku(true);
                            return whUniqueSku;
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(whUniqueSkus)) {
                            try {
                                whUniqueSkuService.batchCreateWhUniqueSku(whUniqueSkus);
                            } catch (Exception e) {
                                log.error(e.getMessage());
                                domain.getPrintSku().clear();
                                return modelAndView;
                            }
                        }
                    } else {
                        return modelAndView;
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                } finally {
                    RedissonLockUtil.unlock(PrintSkuQrCodeRedisLock.EXISTS_SKU_QR_CODE.getKey() + whSku.getSku());
                }
            }
        }
        modelAndView.addObject(domain);
        return modelAndView;
    }

    @GetMapping("toPrintSkuTransfer")
    public String toPrintSkuTransfer(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id){
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            return "transfer/toPrintSkuTransfer";
        }
        domain.setWhFbaAllocation(allocationList.get(0));
        domain.setItems(allocationList.get(0).getItems());
        return "transfer/toPrintSkuTransfer";
    }

    /**
     * 装箱
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "toBox")
    public String toBox(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "transfer/toBox";
        }
        assembleBoxData(domain, id);
        return "transfer/toBox";
    }

    /**
     * 装箱
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "toBoxAsn")
    public String toBoxAsn(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "transfer/toBoxAsn";
        }
        assembleBoxData(domain, id);
        if (domain.getWhFbaAllocation() != null
                && SaleChannel.CHANNEL_SMT.equalsIgnoreCase(domain.getWhFbaAllocation().getPurposeHouse()))
            return "transfer/toBoxJitAsn";
        return "transfer/toBoxAsn";
    }

    /**
     * 装箱
     *
     * @param allocationStr
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "box")
    public ResponseJson box(@RequestParam("allocation") String allocationStr, @RequestParam("id") Integer id) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("FBA调拨单ID不能为空！");
            return response;
        }
        WhFbaAllocation allocation = JSONObject.parseObject(allocationStr, WhFbaAllocation.class);
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        List<WhFbaAllocation> allocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocations)) {
            response.setMessage("单号有误，没有查到该调拨单！");
            return response;
        }
        WhFbaAllocation fbaAllocation = allocations.get(0);
        boolean jitAsn = fbaAllocation.getIsAsn() != null && fbaAllocation.getIsAsn();
        if (!jitAsn && !AsnPrepareStatus.WAITING_BOX.intCode().equals(fbaAllocation.getStatus())
                && !AsnPrepareStatus.PICK_STOCK_OUT.intCode().equals(fbaAllocation.getStatus())) {
            response.setMessage("状态不是待装箱或拣货缺货！");
            return response;
        }
        if (jitAsn && !AsnPrepareStatus.CHECK_PRINT.intCode().equals(fbaAllocation.getStatus())
                && !AsnPrepareStatus.PICK_STOCK_OUT.intCode().equals(fbaAllocation.getStatus())) {
            response.setMessage("状态不是待包装或拣货缺货！");
            return response;
        }
        if (allocation == null || CollectionUtils.isEmpty(allocation.getItems())) {
            response.setMessage("装箱SKU数量不能为空！");
            return response;
        }
        try {
            return whFbaAllocationHandleService.doBox(allocation, fbaAllocation);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            response.setMessage("装箱失败: " + e.getMessage());
        }
        return response;
    }


    /**
     * JIT仓发装箱
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "jitBox")
    public ResponseJson jitBox(@RequestParam("boxNum") Integer boxNum, @RequestParam("id") Integer id) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("FBA调拨单ID不能为空！");
            return response;
        }
        if (boxNum == null) {
            response.setMessage("装箱数量不能为空！");
            return response;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocations)) {
            response.setMessage("单号有误，没有查到该调拨单！");
            return response;
        }
        WhFbaAllocation fbaAllocation = allocations.get(0);
        if ( !AsnPrepareStatus.WAITING_BOX.intCode().equals(fbaAllocation.getStatus())
                && !AsnPrepareStatus.PICK_STOCK_OUT.intCode().equals(fbaAllocation.getStatus())) {
            response.setMessage("状态不是待包装或拣货缺货！");
            return response;
        }

        try {
            whFbaAllocationHandleService.jitBox(boxNum, fbaAllocation);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            response.setMessage("装箱失败: " + e.getMessage());
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }


    /**
     * 打印箱号
     *
     * @param domain
     * @param boxNo
     * @return
     */
    @RequestMapping(value = "toPrintBox")
    public String toPrintBox(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("fbaNo") String fbaNo,
                             @RequestParam("boxNo") Integer boxNo, @RequestParam("boxNum") Integer boxNum, @RequestParam(required = false) Boolean isFba) {
        domain.setFbaNo(fbaNo);
        domain.setBoxNo(boxNo);
        domain.setBoxNum(boxNum);
        if (Boolean.TRUE.equals(isFba)) {
            return "transfer/printBoxFba";
        }
        return "transfer/printBox";
    }

    /**
     * 装箱审核
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "toBoxCheck")
    public String toBoxCheck(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "transfer/toBoxCheck";
        }
        assembleBoxData(domain, id);
        return "transfer/toBoxCheck";
    }

    /**
     * 装箱审核
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "toBoxCheckAsn")
    public String toBoxCheckAsn(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "transfer/toBoxCheckAsn";
        }
        assembleBoxData(domain, id);
        return "transfer/toBoxCheckAsn";
    }

    /**
     * 装箱时取消SKU校验
     * 
     * @param fbaNo
     * @return
     */
    @GetMapping(value = "cancelSkuCheck")
    @ResponseBody
    public ResponseJson cancelSkuCheck(@RequestParam("fbaNo") String fbaNo) {
        ResponseJson response = new ResponseJson();
        WhFbaChangeQueryCondition queryCondition = new WhFbaChangeQueryCondition();
        queryCondition.setFbaNo(fbaNo);
        queryCondition.setDeliveryType(FBAChangeDeliveryTypeEnum.FBA_OUT_STOCK.getCode());
        queryCondition.setOrderItemStatus(FBAChangeOrderItemStatusEnum.WAITING_DEAL.getCode());
        List<WhFbaChange> whFbaChanges = whFbaChangeService.queryWhFbaChanges(queryCondition, null);
        if (CollectionUtils.isNotEmpty(whFbaChanges)) {
            response.setLocation(JSON.toJSONString(whFbaChanges));
            response.setMessage("存在部分取消SKU需要返架，请将实物拿给主管生成返架单后再继续操作！");
            response.setStatus(StatusCode.FAIL);
            return response;
        }
        return response;
    }
    
    /**
     * 处理装箱数据
     *
     * @param domain
     * @param id
     */
    private void assembleBoxData(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(allocationList)) {
            WhFbaAllocation allocation = allocationList.get(0);
            List<WhFbaAllocationItem> itemList = allocation.getItems();
            if (!allocation.isFba())
                domain.setOrderType(PickingTaskType.ASN_FIRST.getCode());
            Map<String, WhFbaAllocationItem[]> skuAndBoxMap = new HashMap<String, WhFbaAllocationItem[]>();
            Map<String, WhFbaAllocationItem> totalItemMap = new HashMap<>();
            Map<String, WhFbaAllocationItem> boxMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(itemList)) {
                List<String> boxList = itemList.stream()
                        .map(i -> String.valueOf(Optional.ofNullable(i.getBoxNo()).orElse(1))).distinct()
                        .sorted(Comparator.comparing(Integer::valueOf)).collect(Collectors.toList());
                if (allocation.isFba()) {
                    boxList.add("合计/已播/待发");
                }
                else {
                    boxList.add("合计/已拣/待发");
                }
                domain.setBoxList(boxList);

                // 根据fnSku分组
                Map<String, List<WhFbaAllocationItem>> fnSkuMap = itemList.stream()
                        .collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku));
                domain.setFnSkuMap(fnSkuMap);
                for (Map.Entry<String, List<WhFbaAllocationItem>> entry : fnSkuMap.entrySet()) {
                    if (CollectionUtils.isEmpty(entry.getValue()) || entry.getValue() == null)
                        continue;
                    List<WhFbaAllocationItem> items = entry.getValue();
                    for (WhFbaAllocationItem item : items) {
                        String boxNo = item.getBoxNo() == null ? "1" : String.valueOf(item.getBoxNo());
                        WhFbaAllocationItem[] itemArray = skuAndBoxMap.get(item.getFnSku());
                        if (ArrayUtils.isEmpty(itemArray)) {
                            itemArray = new WhFbaAllocationItem[boxList.size()];
                        }
                        itemArray[boxList.indexOf(boxNo)] = item;

                        Integer loadNum = item.getLoadNum() == null ? 0 : item.getLoadNum();
                        Integer quantity = item.getQuantity() == null ? 0 : item.getQuantity();
                        Integer pickQuantity = item.getPickQuantity() == null ? 0 : item.getPickQuantity();
                        Integer gridQuantity = item.getGridQuantity() == null ? 0 : item.getGridQuantity();

                        // 套装
                        if (item.getSuitFlag() != null && item.getSuitFlag().equals(1)) {
                            pickQuantity = items.stream()
                                    .map(i -> Math.round((float) (i.getPickQuantity() == null ? 0 : i.getPickQuantity())
                                            / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                                    .sorted().collect(Collectors.toList()).get(0);
                            gridQuantity = items.stream()
                                    .map(i -> Math.round((float) (i.getGridQuantity() == null ? 0 : i.getGridQuantity())
                                            / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                                    .sorted().collect(Collectors.toList()).get(0);
                            if (allocation.getIsAsn() != null && allocation.getIsAsn()) {
                                quantity = item.getSkuSuitNum();
                                pickQuantity = items.stream()
                                        .map(i -> (int) Math
                                                .floor((double) (i.getPickQuantity() == null ? 0 : i.getPickQuantity())
                                                        / (i.getQuantity() == null ? 1 : i.getQuantity())))
                                        .sorted().collect(Collectors.toList()).get(0);
                                gridQuantity = items.stream()
                                        .map(i -> (int) Math
                                                .floor((double) (i.getGridQuantity() == null ? 0 : i.getGridQuantity())
                                                        / (i.getQuantity() == null ? 1 : i.getQuantity())))
                                        .sorted().collect(Collectors.toList()).get(0);
                            }
                        }
                        WhFbaAllocationItem totalItem = totalItemMap.get(item.getFnSku());
                        if (totalItem == null) {
                            totalItem = new WhFbaAllocationItem();
                            totalItem.setBoxNo(Integer.valueOf(boxNo));
                            totalItem.setFnSku(item.getFnSku());
                            totalItem.setLoadNum(loadNum);
                            totalItem.setQuantity(quantity);
                            totalItem.setPickQuantity(pickQuantity);
                            totalItem.setGridQuantity(gridQuantity);
                            totalItem.setSkuImg(item.getSkuImg());
                            totalItem.setSuitFlag(item.getSuitFlag());
                        } else if (!totalItem.getBoxNo().equals(Integer.valueOf(boxNo))) {
                            totalItem.setBoxNo(Integer.valueOf(boxNo));
                            totalItem.setLoadNum(totalItem.getLoadNum() + loadNum);
                        }
                        itemArray[boxList.size() - 1] = totalItem;
                        skuAndBoxMap.put(item.getFnSku(), itemArray);
                        totalItemMap.put(item.getFnSku(), totalItem);

                        Double weight = item.getProductWeight() == null ? 0 : item.getProductWeight();

                        WhFbaAllocationItem boxItem = boxMap.get(boxNo);
                        if (boxItem == null) {
                            boxItem = new WhFbaAllocationItem();
                            boxItem.setFnSku(item.getFnSku());
                            boxItem.setProductWeight(weight);
                            boxItem.setProductLength(item.getProductLength());
                            boxItem.setProductWidth(item.getProductWidth());
                            boxItem.setProductHeight(item.getProductHeight());
                        }
                        boxMap.put(boxNo, boxItem);
                        domain.setBoxMap(boxMap);
                    }
                }
            }
            domain.setSkuAndBoxMap(skuAndBoxMap);
            domain.setWhFbaAllocation(allocation);
        }
    }

    /**
     * 装箱审核
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "boxCheck", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson boxCheck(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("参数错误!");
            return response;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            if (CollectionUtils.isEmpty(allocationList)) {
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
            WhFbaAllocation whFbaAllocation = allocationList.get(0);
            boolean checkResult = this.toCheckAndPush(domain, whFbaAllocation, response);
            if (!checkResult) {
                return response;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 推送
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "boxPush", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson boxPush(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("参数错误!");
            return response;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            if (CollectionUtils.isEmpty(allocationList)) {
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
            WhFbaAllocation whFbaAllocation = allocationList.get(0);
            boolean checkResult = this.toCheckAndPush(domain, whFbaAllocation, response);
            if (checkResult && !whFbaAllocation.isFba()) {
                whFbaAllocationService.sendMsgToTms(whFbaAllocation, TmsSendMsgType.SEND_BOX_INFO.intCode());
            }
            if (!checkResult) {
                return response;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 装箱审核/推送共有的处理逻辑
     *
     * @param domain
     * @return true表示校验通过，false表示校验不通过
     */
    private boolean toCheckAndPush(WhFbaAllocationDo domain, WhFbaAllocation whFbaAllocation, ResponseJson response) throws Exception {
        if (!whFbaAllocation.getStatus().equals(AsnPrepareStatus.WAITING_BOX.intCode())
                && !whFbaAllocation.getStatus().equals(AsnPrepareStatus.PICK_STOCK_OUT.intCode())) {
            response.setMessage("不是拣货缺货或待装箱状态，不能推送!");
            return false;
        }
        if (!whFbaAllocation.isFba())
            domain.setOrderType(PickingTaskType.ASN_FIRST.getCode());

        List<WhFbaAllocationItem> fbaAllocationItems = whFbaAllocation.getItems().stream().filter(f -> f.getLoadNum() != null).collect(Collectors.toList());
        whFbaAllocation.setItems(fbaAllocationItems);
        // 分组合计装箱数量
        Map<String, Integer> loadingQuantityMap = whFbaAllocation.getItems().stream().collect(Collectors.groupingBy(
                WhFbaAllocationItem::getFnSku, Collectors.summingInt(WhFbaAllocationItem::getLoadNum)));

        // 分组计算套装拣货数量
        Map<String, List<WhFbaAllocationItem>> suitFnSkuMap = whFbaAllocation.getItems().stream()
                .filter(i -> i.getSuitFlag() != null && i.getSuitFlag().equals(1))
                .collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku));

        Map<String, Integer> suitFnSkuPickQuantityMap = new HashMap<>();
        Map<String, Integer> suitFnSkuGridQuantityMap = new HashMap<>();
        if (MapUtils.isNotEmpty(suitFnSkuMap)) {
            for (Map.Entry<String, List<WhFbaAllocationItem>> entry : suitFnSkuMap.entrySet()) {
                Integer pickQuantity = entry.getValue().stream()
                        .map(i -> Math.round((float) (i.getPickQuantity() == null ? 0 : i.getPickQuantity())
                                / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                        .sorted().collect(Collectors.toList()).get(0);
                suitFnSkuPickQuantityMap.put(entry.getKey(), pickQuantity);

                Integer gridQuantity = entry.getValue().stream()
                        .map(i -> Math.round((float) (i.getGridQuantity() == null ? 0 : i.getGridQuantity())
                                / (i.getSkuSuitNum() == null ? 1 : i.getSkuSuitNum())))
                        .sorted().collect(Collectors.toList()).get(0);
                suitFnSkuGridQuantityMap.put(entry.getKey(), gridQuantity);

                // 套装的重新计算装箱数量
                Map<Integer, List<WhFbaAllocationItem>> boxMap = entry.getValue().stream()
                        .collect(Collectors.groupingBy(WhFbaAllocationItem::getBoxNo));
                Integer loadNum = 0;
                for (Map.Entry<Integer, List<WhFbaAllocationItem>> boxEntry : boxMap.entrySet()) {
                    Integer boxLoadNum = boxEntry.getValue().get(0).getLoadNum() == null ? 0
                            : boxEntry.getValue().get(0).getLoadNum();
                    loadNum = loadNum + boxLoadNum;

                }
                loadingQuantityMap.put(entry.getKey(), loadNum);
            }
        }

        // 过滤装箱数量为零并且sku数件为零的明细
        if (CollectionUtils.isNotEmpty(whFbaAllocation.getItems())) {
            List<WhFbaAllocationItem> whFbaAllocationItems = whFbaAllocation.getItems().stream()
                    .filter(a -> a.getSkuQuantity() != null && a.getSkuQuantity() > 0
                            && a.getLoadingQuantity() != null && a.getLoadingQuantity() > 0)
                    .collect(Collectors.toList());
            whFbaAllocation.setItems(whFbaAllocationItems);
        }

        for (WhFbaAllocationItem item : whFbaAllocation.getItems()) {
            Integer pickQuantity = item.getPickQuantity();
            if (item.getSuitFlag() != null && item.getSuitFlag().equals(1)
                    && suitFnSkuPickQuantityMap != null) {
                pickQuantity = suitFnSkuPickQuantityMap.get(item.getFnSku()) == null ? 0
                        : suitFnSkuPickQuantityMap.get(item.getFnSku());
            }
            Integer gridQuantity = item.getGridQuantity();
            if (item.getSuitFlag() != null && item.getSuitFlag().equals(1)
                    && suitFnSkuGridQuantityMap != null) {
                gridQuantity = suitFnSkuGridQuantityMap.get(item.getFnSku()) == null ? 0
                        : suitFnSkuGridQuantityMap.get(item.getFnSku());
            }

            if (whFbaAllocation.isFba()) {
                Integer compareQuantity = gridQuantity > pickQuantity ? pickQuantity : gridQuantity;
                if (!loadingQuantityMap.get(item.getFnSku()).equals(compareQuantity)) {
                    response.setMessage(item.getProductSku() + "装箱数量和播种数量不一致！");
                    return false;
                }
            } else if (!whFbaAllocation.isFba() && !loadingQuantityMap.get(item.getFnSku()).equals(pickQuantity)) {
                response.setMessage(item.getProductSku() + "装箱数量和拣货数量不一致！");
                return false;
            }
        }
        // FBA 装箱图片必传
        if (whFbaAllocation.isFba()) {
            for (WhFbaAllocationItem item : whFbaAllocation.getItems()) {
                if (StringUtils.isBlank(item.getSkuImg())) {
                    response.setMessage(String.format("箱号:%d SKU:%s装箱图片缺失", item.getBoxNo(), item.getFnSku()));
                    return false;
                }
            }
        }
        //todo 该方法的实现存在副作用，后期需要进行优化
        whFbaAllocationHandleService.doBoxCheck(whFbaAllocation);

        // FBA另外推送FNSKU总装箱数量 TODO liurui FBA逻辑临时注释
        if (whFbaAllocation.isFba()) {
            this.pushFbaLoadingQtyToOms(whFbaAllocation);
        }
        return true;
    }

    private void pushFbaLoadingQtyToOms(WhFbaAllocation whFbaAllocation){
        try {
            // 调用产品http接口查询包材信息
            SystemParam systemParam = CacheUtils.SystemParamGet("OMS_PARAM.PUSH_FBA_LOADING_QTY_URL");
            if (null == systemParam || StringUtils.isBlank(systemParam.getParamValue())) {
                log.error("systemParam OMS_PARAM.PUSH_FBA_LOADING_QTY_URL value is null");
                return;
            }
            Map<String, Integer> loadingQtyMap = whFbaAllocation.getItems().stream().collect(Collectors.groupingBy(
                    c -> c.getProductSku() + ";" + Optional.ofNullable(c.getSellSku()).orElse(c.getProductSku()), Collectors.summingInt(WhFbaAllocationItem::getLoadingQuantity)));
            List<Map<String,Object>> items = new ArrayList<>();
            for(Map.Entry<String, Integer> entry:loadingQtyMap.entrySet()) {
                Map<String, Object> qtyMap = new HashMap<>();
                String[] split = entry.getKey().split(";");
                qtyMap.put("sku", split[0]);
                qtyMap.put("sellerSku", split[1]);
                qtyMap.put("quantity", entry.getValue());
                items.add(qtyMap);
            }
            Map<String, Object> map = new HashMap<>(8);
            map.put("accountNumber", whFbaAllocation.getAccountNumber());
            map.put("yst", whFbaAllocation.getFbaNo());
            map.put("itemList", items);
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", HttpUtils.ACCESS_TOKEN);
            headers.put("Content-Type", "application/json; charset=utf-8");
            String param = JSON.toJSONString(map);
            log.info("pushFbaLoadingQtyToOms param:" + param);
            ApiResult apiResult = HttpExtendUtils.post(systemParam.getParamValue() , headers, param,
                    ApiResult.class,300000,300000);
            log.info("pushFbaLoadingQtyToOms result:" + JSON.toJSONString(apiResult));
        } catch (Exception e) {
            log.error("pushFbaLoadingQtyToOms error:" + e.getMessage(), e);
        }
    }

    /**
     * 打印箱唛
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printXiangmai", method = {RequestMethod.GET})
    public String printXiangmai(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "transfer/printXiangmai";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(allocationList)) {
            WhFbaAllocation whFbaAllocation = allocationList.get(0);
            domain.setWhFbaAllocation(whFbaAllocation);
            domain.getWhFbaAllocation().splitReceivingCode();
            SystemLogUtils.FBAALLOCATIONLOG.log(allocationList.get(0).getId(), "打印箱唛");
            int boxTotal = allocationList.get(0).getItems().stream().map(WhFbaAllocationItem::getBoxNo).distinct()
                    .collect(Collectors.toList()).size();
            domain.setBoxNum(boxTotal);
            try {
                //JIT备货单打印箱唛
                WhAsnExtra whAsnExtra = allocationList.get(0).getWhAsnExtra();
                Integer[] packageMethods = new Integer[]{AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode(), AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()};
                if (!Objects.equals(SaleChannel.CHANNEL_SHEIN, whFbaAllocation.getPurposeHouse()) && whAsnExtra != null
                        && ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod())) {
                    Integer orderType = null;
                    Integer boxQuantity = null;
                    if (AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                        orderType = 2;
                        boxQuantity = boxTotal;
                    }
                    String jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(allocationList.get(0).getFbaNo(), 2, orderType, boxQuantity);
                    domain.setPrintUrl(jitPdfUrl);
                    return "asn/printCheckOut";
                }
                String url = whFbaAllocationHandleService.printXiangmaiFBA(allocationList.get(0));
                domain.setPrintUrl(url);
                return "asn/printCheckOut";
            } catch (Exception e) {
                domain.setErrorMsg(e.getMessage());
                return "asn/printCheckOut";
            }

        }
        return "transfer/printXiangmai";
    }

    /**
     * 打印交运条码
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printDeliverNo")
    public String printDeliverNo(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/printDeliverNo";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(allocationList)) {
            WhFbaAllocation exist = allocationList.get(0);
            domain.setWhFbaAllocation(exist);
            int boxTotal = allocationList.get(0).getItems().stream().map(WhFbaAllocationItem::getBoxNo).distinct()
                    .collect(Collectors.toList()).size();
            domain.setBoxNum(boxTotal);
            SystemLogUtils.FBAALLOCATIONLOG.log(exist.getId(), "打印交运条码");
        }
        return "asn/printDeliverNo";
    }

    /**
     * 打印标签
     *
     * @param domain
     * @param id     whAsnExtra.id
     * @param url    标签对应的url
     * @return
     */
    @RequestMapping(value = "printTag", method = {RequestMethod.GET})
    public String printCheckOut(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("url") String url,
                                @RequestParam("id") Integer id, @RequestParam("type") String type) {
        if (StringUtils.isBlank(url) || Objects.isNull(id) || StringUtils.isBlank(type)) {
            domain.setErrorMsg("参数错误");
            return "asn/printTags";
        }
        WhAsnExtra whAsnExtra = whAsnExtraService.getWhAsnExtra(id);
        SystemLogUtils.FBAALLOCATIONLOG.log(whAsnExtra.getWhAsnId(), "FBA调拨发货单打印" + type);
        domain.setPrintUrl(url);
        return "asn/printTags";
    }


    @GetMapping(value = "toPrintTemuSKU")
    public String toPrintTemuSKU(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id
            ,@RequestParam("type") String type,@RequestParam(value = "sku", required = false) String sku) {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(allocationList) && allocationList.get(0) != null
                && CollectionUtils.isNotEmpty(allocationList.get(0).getItems())) {
            WhFbaAllocation fbaAllocation = allocationList.get(0);
            List<WhFbaAllocationItem> items = fbaAllocation.getItems();
            if (StringUtils.isNotBlank(sku)) {
                items=items.stream().filter(item -> sku.equals(item.getProductSku())).collect(Collectors.toList());
            }
            boolean isAsn = fbaAllocation.getIsAsn() !=null && fbaAllocation.getIsAsn();
            if (fbaAllocation.isJit()) {
                    String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + ".pdf";
                    File file = new File(mergePdfPath);
                    if (!isAsn && !file.exists()) {
                        try {
                            String jitPdfUrl = null;
                            jitPdfUrl = jitPickupOrderService.createScItemBarcodePdf(allocationList.get(0));
                            if (StringUtils.isNotBlank(jitPdfUrl)) {
                                PdfUtils.parseAndSplitToSave(jitPdfUrl, allocationList.get(0).getFbaNo(),
                                        allocationList.get(0).getAllotNum());
                            }
                        }
                        catch (Exception e) {
                            log.error("获取jit标签异常" + e.getMessage(), e);
                        }
                    }
                    items.forEach(item -> {
                        String temuUrl = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + "_"
                                + item.getSkuBarcode() + ".pdf";
                        File skuFile = new File(temuUrl);
                        if (!skuFile.exists() && isAsn && item.getScItemId() != null){
                            temuUrl = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + "_"
                                    + item.getScItemId() + ".pdf";
                            skuFile = new File(temuUrl);
                        }
                        if (!skuFile.exists()){
                            temuUrl = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + "_"
                                    + item.getProductSku() + ".pdf";
                            skuFile=new File(temuUrl);
                        }
                        if (!skuFile.exists()){
                            temuUrl = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + "_"
                                    +"YSTD"+ item.getProductSku() + ".pdf";
                            skuFile=new File(temuUrl);
                        }
                        temuUrl = StringUtils.substringAfterLast(temuUrl, "static/");
                        // 避免单品单件解析失败，查询一个未拆分的pdf
                        if (!skuFile.exists()) {
                            try {
                                String scItemId = item.getScItemId() == null?item.getProductBarcode():item.getScItemId().toString();
                                temuUrl = jitPickupOrderService.tagPrint(allocationList.get(0).getAccountNumber(),
                                        allocationList.get(0).getWhAsnExtra().getBizType(), scItemId);
                                temuUrl = PdfUtils.convertPdfFromHttpToBase64(temuUrl);
                            }
                            catch (Exception ex) {
                                log.error("打印jit标签异常" + ex.getMessage(), ex);
                            }
                        }
                        if (StringUtils.isNotBlank(temuUrl))
                            item.setTemuCodeUrl(temuUrl);
                    });

            }
            
            List<String> temuCodeTypes = Arrays.asList("条码标签", "sku标签");
            items = items.stream().filter(f -> temuCodeTypes.contains(type) ? StringUtils.isNotBlank(f.getTemuCodeUrl()) : StringUtils.isNotBlank(f.getTemuTagUrl())).collect(Collectors.toList());
            fbaAllocation.setApvType(type);
            fbaAllocation.setItems(items);
            domain.setWhFbaAllocation(fbaAllocation);

            boolean anyMatch = allocationList.get(0).getItems().stream()
                    .anyMatch(item -> StringUtils.contains(item.getTag(), "电"));
            if (anyMatch){
                SkuTagInfoQueryCondition infoQuery = new SkuTagInfoQueryCondition();
                infoQuery.setSkuList(items.stream().map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList()));
                List<SkuTagInfo> tagInfos = skuTagInfoService.querySkuTagInfos(infoQuery, null);
                domain.setSkuTagInfos(tagInfos);
            }
        }
        return "asn/toPrintTemuTags";
    }

    /**
     * 打印标签
     *
     * @param domain
     * @param id     whAsnExtra.id
     * @param allocationStr    标签对应的url
     * @return
     */
    @RequestMapping(value = "printTemuTag", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson printTemuTag(@ModelAttribute("domain") WhFbaAllocationDo domain,  @RequestParam("allocationStr") String allocationStr,
                                @RequestParam("id") Integer id, @RequestParam("type") String type, @RequestParam(value = "sku", required = false) String sku) {
        ResponseJson responseJson=new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(allocationStr)) {
            responseJson.setMessage("参数异常请联系开发人员！");
            return responseJson;
        }

        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)) {
            responseJson.setMessage("单号不存在");
            return responseJson;
        }
        boolean smtJit = allocationList.get(0).isJit();
        if (smtJit && allocationList.get(0).getWhAsnExtra() ==  null){
            responseJson.setMessage("单号不存在");
            return responseJson;
        }
        Map<String, WhFbaAllocationItem> allocationItemMap = allocationList.get(0).getItems().stream()
                .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku, i -> i, (i1, i2) -> i1));

        WhFbaAllocation allocation = JSONObject.parseObject(allocationStr, WhFbaAllocation.class);
        List<WhFbaAllocationItem> items = allocation.getItems();

        HashMap<String, Integer> map = new HashMap<>();
        HashMap<String, String> tagMap = new HashMap<>();
        HashMap<String, String> dzMap = new HashMap<>();
        boolean isAsn = allocationList.get(0).getIsAsn() !=null && allocationList.get(0).getIsAsn();
        if (StringUtils.isNotBlank(sku)) {
            items= items.stream().filter(f -> StringUtils.isNotBlank(f.getProductSku())).collect(Collectors.toList());
        }

        for (WhFbaAllocationItem item : items) {
            WhFbaAllocationItem fbaAllocationItem = allocationItemMap.get(item.getProductSku());
            if (fbaAllocationItem==null){
                continue;
            }
            String temuUrl="";
            if ("条码标签".equals(type) || "sku标签".equals(type)) {
                if (smtJit) {
                    try {
                        temuUrl = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + "_"
                                + item.getSkuBarcode() + ".pdf";
                        File skuFile = new File(temuUrl);

                        if (!skuFile.exists() && isAsn && item.getScItemId() != null){
                            temuUrl = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + "_"
                                    + item.getScItemId() + ".pdf";
                            skuFile = new File(temuUrl);
                        }
                        temuUrl = StringUtils.substringAfterLast(temuUrl, "static/");
                        // 避免单品单件解析失败，查询一个未拆分的pdf
                        if (!skuFile.exists()) {
                            String scItemId = item.getScItemId() == null?item.getProductBarcode():item.getScItemId().toString();
                            temuUrl = jitPickupOrderService.tagPrint(allocationList.get(0).getAccountNumber(),
                                    allocationList.get(0).getWhAsnExtra().getBizType(), scItemId);
                            temuUrl = PdfUtils.convertPdfFromHttpToBase64(temuUrl);
                        }
                    }
                    catch (Exception e) {
                        log.error("打印jit标签异常" + e.getMessage(), e);

                    }
                }
                if (StringUtils.isBlank(temuUrl)){
                    temuUrl = fbaAllocationItem.getTemuCodeUrl();
                }
            }
            if ("打包标签".equals(type)){
                temuUrl=fbaAllocationItem.getTemuTagUrl();
            }
            if (StringUtils.isBlank(temuUrl)) {
                continue;
            }
            Integer quantity = map.get(temuUrl);
            if (quantity!=null) {
                quantity+=Optional.ofNullable(item.getQuantity()).orElse(0);
            }
            else {
                quantity=Optional.ofNullable(item.getQuantity()).orElse(0);
            }

            if (quantity>0){
                map.put(temuUrl,quantity);
            }
            if ("条码标签".equals(type) && StringUtils.isNotBlank(fbaAllocationItem.getTag())){
                tagMap.put(temuUrl, fbaAllocationItem.getTag());
                dzMap.put(temuUrl, fbaAllocationItem.getProductSku());
            }
        }
        if (MapUtils.isEmpty(map)) {
            responseJson.setMessage(type+"不存在!");
            return responseJson;

        }
        String warehousePdfUrl = this.genReceiveIdPdf(allocation.getWarehouseCodeStr());
        if (StringUtils.isNotBlank(warehousePdfUrl)) {
            map.put(warehousePdfUrl, 1);
        }

        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.getBody().put("temuPdfUrl", map);
        responseJson.getBody().put("tagMap", tagMap);
        responseJson.getBody().put("dzMap", dzMap);
        SystemLogUtils.FBAALLOCATIONLOG.log(id, "海外仓发货单打印" + type);
        return responseJson;
    }
    
    
    /**
     * 打印出库单
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printCheckOut", method = {RequestMethod.GET})
    public String printCheckOut(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {

        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "transfer/printCheckOut";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            if (CollectionUtils.isEmpty(allocationList) || allocationList.get(0) == null
                    || StringUtils.isEmpty(allocationList.get(0).getPdfUrl())) {
                domain.setErrorMsg("没有面单!");
                return "transfer/printCheckOut";
            }
            WhFbaAllocation exist = allocationList.get(0);
            // 自画面单
            if (exist.getPdfUrl().equals(exist.getFbaNo())) {
                // 根据批次号捞取订单
                query = new WhFbaAllocationQueryCondition();
                query.setBatNo(exist.getBatNo());
                List<WhFbaAllocation> batList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
                AtomicReference<Integer> boxNum = new AtomicReference<>(0);
                if (CollectionUtils.isNotEmpty(batList)) {
                    batList.forEach(allocation -> boxNum.set(boxNum.get() + allocation.getBoxTotal()));
                }
                List<String> boxList = new ArrayList<>();
                for (int i = 1; i <= boxNum.get(); i++) {
                    boxList.add(exist.getShippingOrderNo() + "-" + i);
                }
                domain.setBoxList(boxList);
                domain.setWhFbaAllocation(exist);
                return "transfer/fbaFailPrint";
            } else {
                domain.setPrintUrl(exist.getPdfUrl());
                return "transfer/printCheckOut";
            }
        } catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
        }
        return "transfer/printCheckOut";
    }


    /**
     * 打印面单
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "labelPrint", method = {RequestMethod.GET})
    public String labelPrint(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "transfer/printCheckOut";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        boolean isJit = false;
        try {
            if (CollectionUtils.isEmpty(allocationList) || allocationList.get(0) == null) {
                domain.setErrorMsg("没有面单!");
                return "transfer/printCheckOut";
            }
            //JIT备货单
            WhAsnExtra whAsnExtra = allocationList.get(0).getWhAsnExtra();
            if (whAsnExtra != null && (AsnPackageMethodEnum.JIT.getCode().equals(whAsnExtra.getPackageMethod())
                    || AsnPackageMethodEnum.JIT_HALF.getCode().equals(whAsnExtra.getPackageMethod()))) {
                isJit = true;
                String jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(allocationList.get(0).getFbaNo(), 3, null, null);
                domain.setPrintUrl(jitPdfUrl);
                return "transfer/printCheckOut";
            }


            String url = CacheUtils.SystemParamGet("TMS_PARAM.GET_LABEL").getParamValue();
            ApiResult apiResult = HttpUtils.get(url + allocationList.get(0).getFbaNo(), HttpUtils.ACCESS_TOKEN, ApiResult.class);
            if (apiResult.isSuccess() && apiResult.getResult() != null) {
                String pdfUrl = apiResult.getResult().toString();
                domain.setPrintUrl(pdfUrl);
                return "transfer/printCheckOut";
            }
            domain.setErrorMsg(apiResult.getErrorMsg());
        } catch (Exception e) {
            if (isJit) {
                domain.setErrorMsg(String.format("获取OMS面单失败！%s", e.getMessage()));
                log.error("获取OMS面单失败!" + e);
            } else {
                domain.setErrorMsg(String.format("调用物流面单接口失败！%s", e.getMessage()));
                log.error("调用物流面单接口失败!" + e);
            }
        }

        return "transfer/printCheckOut";

    }

    /**
     * 打印sku面单
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printJitSKU", method = {RequestMethod.GET})
    public String printJitSKU(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "transfer/printCheckOut";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            if (CollectionUtils.isEmpty(allocationList) || allocationList.get(0) == null) {
                domain.setErrorMsg("没有sku面单!");
                return "transfer/printCheckOut";
            }
            //JIT备货单
            WhAsnExtra whAsnExtra = allocationList.get(0).getWhAsnExtra();
            Integer[] packageMethods = new Integer[]{AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode(), AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()};
            if (whAsnExtra != null && ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod())) {
                Integer orderType = null;
                String jitPdfUrl = null;
                if (AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                    orderType = 2;
                    jitPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(allocationList.get(0).getFbaNo(), 1,
                            orderType, null);
                }
                else {
                    jitPdfUrl = jitPickupOrderService.createScItemBarcodePdf(allocationList.get(0));
                }
                if (StringUtils.isNotBlank(jitPdfUrl)) {
                    String mergePdfPath = PdfUtils.STATIC_FILE_PATH + "/" + allocationList.get(0).getFbaNo() + ".pdf";
                    PdfUtils.parseAndSplitToSave(jitPdfUrl, allocationList.get(0).getFbaNo(), allocationList.get(0).getAllotNum());
                    domain.setPrintUrl(StringUtils.substringAfterLast(mergePdfPath, "static"));
                }
                return "transfer/printCheckOut";
            }
        } catch (Exception e) {
            domain.setErrorMsg(String.format("调用sku面单接口失败！%s", e.getMessage()));
            log.error("调用sku面单接口失败!" + e);
        }
        return "transfer/printCheckOut";

    }

    @RequestMapping(value = "editAndDeliver", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson editAndDeliver(@RequestParam("id") Integer id,
                                       @RequestParam("receivingCode") String receivingCode, @RequestParam("type") Integer type,
                                       @RequestParam("trackingNumber") String trackingNumber,
                                       @RequestParam("shippingMethod") String shippingMethod) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("出库单编号不能为空！");
            return response;
        }
        if (StringUtils.isBlank(receivingCode)) {
            response.setMessage("出库单号不能为空！");
            return response;
        }
        if (StringUtils.isBlank(trackingNumber)) {
            response.setMessage("追踪号不能为空！");
            return response;
        }
        if (StringUtils.isBlank(shippingMethod)) {
            response.setMessage("物流公司不能为空！");
            return response;
        }

        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(receivingCode);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);

        if (CollectionUtils.isEmpty(allocationList)) {
            response.setMessage("单号有误，查无此出库单！");
            return response;
        }
        WhFbaAllocation whAsn = allocationList.get(0);
        if (type == 1) {
            if (!AsnPrepareStatus.WAITING_DELIVER.intCode().equals(whAsn.getStatus())) {
                response.setMessage("不是待发货，不允许交运！");
                return response;
            }
            WhAsnExtra whAsnExtra = whAsn.getWhAsnExtra();
            if (whAsnExtra == null || (AsnPackageMethodEnum.JIT_HALF.getCode().equals(whAsnExtra.getPackageMethod()) && StringUtils.isBlank(whAsnExtra.getPickupOrderId()))) {
                response.setMessage("当前半托管备货单未创建揽收单！");
                return response;
            }
            query.setTrackingNumber(trackingNumber);
            query.setFbaNo(null);
            List<WhFbaAllocation> exist = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isNotEmpty(exist)) {
                exist = exist.stream().filter(a -> !receivingCode.equals(a.getFbaNo())).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(exist)) {
                response.setMessage("追踪号已使用！");
                return response;
            }
            try {
                whAsn.setTrackingNumber(trackingNumber);
                whAsn.setShippingMethod(shippingMethod);
                return whFbaAllocationService.doDeliver(Arrays.asList(whAsn));
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
                response.setMessage("交运失败: " + e.getMessage());
            }
        } else {
            // 编辑
            if (AsnPrepareStatus.DELIVER.intCode().equals(whAsn.getStatus())
                    || AsnStatus.LOADED.intCode().equals(whAsn.getStatus())
                    || AsnStatus.ABANDONED.intCode().equals(whAsn.getStatus())) {
                response.setMessage("已交运或已装车或已取消状态，不允许编辑！");
                return response;
            }
            query.setTrackingNumber(trackingNumber);
            query.setFbaNo(null);
            List<WhFbaAllocation> exist = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isNotEmpty(exist) && !exist.get(0).getId().equals(whAsn.getId())) {
                response.setMessage("追踪号已使用！");
                return response;
            }
            try {
                whAsn.setTrackingNumber(trackingNumber);
                whAsn.setShippingMethod(shippingMethod);
                whFbaAllocationService.updateWhFbaAllocation(whAsn);
                SystemLogUtils.FBAALLOCATIONLOG.log(whAsn.getId(), "编辑海外仓出库单",
                        new String[][]{{"追踪号", whAsn.getTrackingNumber()}, {"物流公司", whAsn.getShippingMethod()}});
                response.setStatus(StatusCode.SUCCESS);
                whFbaAllocationService.sendMsg(whAsn);
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
                response.setMessage("编辑失败: " + e.getMessage());
            }
        }
        return response;
    }

    /**
     * 打印超重标签
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printHeavyTag", method = {RequestMethod.GET})
    public String printHeavyTag(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {

        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "transfer/printHeavyTag";
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        try {
            // 重货标签：目的仓为美国单箱重量>22.5kg打印”Team Lift“标签；欧洲>15kg打印“Heavy
            // Package”；日本>15kg打印“超重标签”
            if (CollectionUtils.isNotEmpty(allocationList)) {
                WhFbaAllocation exist = allocationList.get(0);
                List<String> europeCountryCode = CountryEnum.getEuropeCountryCode();
                WhAsnExtraQueryCondition asnExtraQuery = new WhAsnExtraQueryCondition();
                asnExtraQuery.setWhAsnId(exist.getId());
                WhAsnExtra asnExtra = whAsnExtraService.queryWhAsnExtra(asnExtraQuery);
                if (asnExtra != null && StringUtils.isNotEmpty(asnExtra.getReceiptCountry())) {
                    String country = asnExtra.getReceiptCountry();

                    if (europeCountryCode.contains(country) && exist.getItems().stream()
                            .filter(i -> i.getProductWeight() > 15).collect(Collectors.toList()).size() > 0) {
                        domain.setHeavyTag("EUR");
                    } else if (CountryEnum.US.getSite().equalsIgnoreCase(country) && exist.getItems().stream()
                            .filter(i -> i.getProductWeight() > 22.5).collect(Collectors.toList()).size() > 0) {
                        domain.setHeavyTag(CountryEnum.US.getSite());
                    } else if (CountryEnum.JP.getSite().equalsIgnoreCase(country) && exist.getItems().stream()
                            .filter(i -> i.getProductWeight() > 15).collect(Collectors.toList()).size() > 0) {
                        domain.setHeavyTag(CountryEnum.JP.getSite());
                    } else {
                        domain.setErrorMsg("没有超重货物！");
                    }
                } else {
                    domain.setErrorMsg("货件国家信息为空！");
                }
            }
        } catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
        }
        return "transfer/printHeavyTag";
    }

    /**
     * 手动推送海外仓上架数量到TMS
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "pushOverseasUpQuantityToTms", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson pushOverseasUpQuantityToTms(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids)) {
            response.setMessage("请选择要推送的数据！");
            return response;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setIds(ids);
        whFbaAllocationService.updateOverseasUpQuantity(query, true);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 复核贴标信息
     *
     * @param domain
     * @param id
     * @return
     */
    @GetMapping(value = "checkLabelInfo")
    public String checkLabelInfo(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "transfer/toLabelCheck";
        }
        assembleFnSkuMap(domain, id, null);
        return "transfer/toLabelCheck";
    }

    /**
     * 审核
     *
     * @param id
     * @param allocationStr
     * @return
     */
    @RequestMapping(value = "doCheckLabel", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson doCheckLabel(@RequestParam("id") Integer id,
                                     @RequestParam("allocationStr") String allocationStr) {
        Assert.notNull(id);
        Assert.notNull(allocationStr);
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        try {
            response = whFbaAllocationService.doCheckLabel(id, allocationStr);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            response.setMessage("提交复核意见失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 导出
     *
     * @param domain
     * @param ids
     * @param response
     */
    @RequestMapping(value = "download", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson download(@ModelAttribute("domain") WhFbaAllocationDo domain,
                         @RequestParam(value = "ids", required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        String[] HEADERS = {"编号", "海外仓发货单号", "货件单号", "店铺", "站点", "销售", "目的仓", "贴标人", "加工类型", "箱号", "FNSKU", "sku", "标题", "FNSKU件数", "SKU件数", "拣货数量", "装箱数量",
                "海外仓上架数量", "上架差异", "尺寸(CM)", "重量(KG)", "收费重(KG)", "运费单价", "其他费用", "运费合计", "头程单价", "状态",
                "运输方式/物流方式/追踪号/物流单号", "推单时间", "贴标时间", "装箱推送时间", "确认时间", "交运时间", "取消时间", "驳回原因"};

        WhFbaAllocationQueryCondition query = domain.getQuery();
        if (query == null || CollectionUtils.isNotEmpty(ids)) {
            query = new WhFbaAllocationQueryCondition();
            query.setIds(ids);
        }
        domain.getPage().setPageNo(-1);
        domain.setQuery(query);
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        String fileName = "FBA调拨发货单管理" + System.currentTimeMillis() + ".xlsx";

        whDownloadCenterService.downloading(fileName,HEADERS, WhDownloadContentEnum.FBA_ALLOCATION_ORDER,isAll,domain.getPage(),(page) -> {
            domain.setPage(page);
            domain.setWhFbaAllocations(null);
            queryWhFbaAllocations(domain);
            List<WhFbaAllocation> whFbaAllocations = domain.getWhFbaAllocations();
            return getExportList(whFbaAllocations,HEADERS,domain.getOrderType());
        });
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;

    }
    private List<List<String>> getExportList(List<WhFbaAllocation> whFbaAllocations,String[] HEADERS,String orderType ) {
        List<List<String>> dataList = new ArrayList<>();
        for (WhFbaAllocation whFbaAllocation : whFbaAllocations) {
            if (StringUtils.equalsIgnoreCase(orderType,PickingTaskType.ASN_FIRST.getCode())) {
                asnOutputItems(dataList,whFbaAllocation,HEADERS);
            }else if (StringUtils.equalsIgnoreCase(orderType,PickingTaskType.ASN_TRANSFER.getCode())){
                asnTransferOutputItems(dataList,whFbaAllocation,HEADERS);
            }else if (StringUtils.equalsIgnoreCase(orderType,PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())){
                jitAsnOutputItems(dataList,whFbaAllocation,HEADERS);
            } else {
                fbaOutputItems(dataList,whFbaAllocation,HEADERS);
            }
        }
        return dataList;
    }
    private void fbaOutputItems(List<List<String>> data, WhFbaAllocation whFbaAllocation,String[] HEADERS ) {
        if (Objects.isNull(whFbaAllocation)) {
            return;
        }
        for (WhFbaAllocationItem item : whFbaAllocation.getItems()) {
            List<String> batchReturnList = new ArrayList<String>(HEADERS.length);
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getFbaNo()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getShipmentId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getAccountNumber()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getAmazonSite()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getSalesperson()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getPurposeHouse()));
            batchReturnList.add(POIUtils.transferObj2Str(TaglibUtils.getEmployeeNameByUserId(item.getTagBy())));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProcessType() == null ? "" : ProcessType.getNameByCode(item.getProcessType().toString())));
            batchReturnList.add(POIUtils.transferObj2Str(item.getBoxNo()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getFnSku()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductSku()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getName()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getSkuQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getPickQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getLoadNum()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getPutawayQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getPutawayDiff()));
            String size = null;
            if (item.getProductLength() != null || item.getProductWidth() != null
                    || item.getProductHeight() != null) {
                size = item.getProductLength() + "*" + item.getProductWidth() + "*" + item.getProductHeight();
            }
            batchReturnList.add(POIUtils.transferObj2Str(size));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductWeight()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getTollWeight()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getFreight()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getOtherPrice()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getBoxCost()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductPrice()));
            batchReturnList.add(
                    POIUtils.transferObj2Str(AsnPrepareStatus.getNameByCode(whFbaAllocation.getStatus() + "")));
            String shippingMethod = null;
            if (StringUtils.isNotBlank(whFbaAllocation.getSmCode())
                    || StringUtils.isNotBlank(whFbaAllocation.getShippingMethod())
                    || StringUtils.isNotBlank(whFbaAllocation.getTrackingNumber())) {
                shippingMethod = StringUtils.isEmpty(whFbaAllocation.getSmCode()) ? "null"
                        : SmCodeStatus.getNameByCode(whFbaAllocation.getSmCode()) + "/"
                        + whFbaAllocation.getShippingMethod() + "/"
                        + whFbaAllocation.getTrackingNumber() + "/"
                        + whFbaAllocation.getShippingOrderNo();
            }
            batchReturnList.add(POIUtils.transferObj2Str(shippingMethod));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            batchReturnList.add(POIUtils.transferObj2Str(
                    whFbaAllocation.getPushTime() == null ? "" : sdf.format(whFbaAllocation.getPushTime())));
            batchReturnList.add(POIUtils.transferObj2Str(item.getTagTime() == null ? ""
                    : sdf.format(item.getTagTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getBoxPushTime() == null ? ""
                    : sdf.format(whFbaAllocation.getBoxPushTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getConfirmTime() == null ? ""
                    : sdf.format(whFbaAllocation.getConfirmTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getDeliverTime() == null ? ""
                    : sdf.format(whFbaAllocation.getDeliverTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getCancelTime() == null ? ""
                    : sdf.format(whFbaAllocation.getCancelTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getRejectReason()));
            data.add(batchReturnList);
        }
    }

    /**
     * 导出
     *
     * @param domain
     * @param ids
     * @param response
     */
    @RequestMapping(value = "asnDownload", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson ansFirstOrderDownload(@ModelAttribute("domain") WhFbaAllocationDo domain,
                                      @RequestParam(value = "ids", required = false) List<Integer> ids) {

        String[] HEADERS = {"编号", "出库单号", "店铺","平台", "箱号", "sku", "标题","件数", "已分配", "已拣", "装箱数量", "海外仓上架数量", "尺寸(CM)", "重量(KG)",
                "状态", "追踪号", "发货地址", "推单时间", "装箱推送时间", "确认时间", "交运时间", "取消时间", "合单时间"};
        ResponseJson response = new ResponseJson();
        WhFbaAllocationQueryCondition query = domain.getQuery();
        if (query == null || CollectionUtils.isNotEmpty(ids)) {
            query = new WhFbaAllocationQueryCondition();
            query.setIds(ids);
        }
        domain.getPage().setPageNo(-1);
        domain.setQuery(query);
        domain.setOrderType(PickingTaskType.ASN_FIRST.getCode());
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        String fileName = "海外仓发货单管理" + System.currentTimeMillis() + ".xlsx";

        whDownloadCenterService.downloading(fileName,HEADERS, WhDownloadContentEnum.ASN_ALLOCATION_ORDER,isAll,domain.getPage(),(page) -> {
            domain.setPage(page);
            domain.setWhFbaAllocations(null);
            queryWhFbaAllocations(domain);
            List<WhFbaAllocation> whFbaAllocations = domain.getWhFbaAllocations();
            return getExportList(whFbaAllocations,HEADERS,domain.getOrderType());
        });
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;
    }

    private void asnOutputItems(List<List<String>> data, WhFbaAllocation whFbaAllocation,String[] HEADERS ) {
        if (Objects.isNull(whFbaAllocation)) {
            return;
        }
        for (WhFbaAllocationItem item : whFbaAllocation.getItems()) {
            List<String> batchReturnList = new ArrayList<String>(HEADERS.length);
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getFbaNo()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getAccountNumber()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getPurposeHouse()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getBoxNo()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductSku()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getName()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getAllotQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getPickQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getLoadNum()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getPutawayQuantity()));
            String size = null;
            if (item.getProductLength() != null || item.getProductWidth() != null
                    || item.getProductHeight() != null) {
                size = item.getProductLength() + "*" + item.getProductWidth() + "*" + item.getProductHeight();
            }
            batchReturnList.add(POIUtils.transferObj2Str(size));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductWeight()));

            batchReturnList.add(
                    POIUtils.transferObj2Str(AsnPrepareStatus.getNameByCode(whFbaAllocation.getStatus() + "")));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getWhAsnExtra() == null ? ""
                    : whFbaAllocation.getWhAsnExtra().getReceiptAddress()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getTrackingNumber()));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            batchReturnList.add(POIUtils.transferObj2Str(
                    whFbaAllocation.getPushTime() == null ? "" : sdf.format(whFbaAllocation.getPushTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getBoxPushTime() == null ? ""
                    : sdf.format(whFbaAllocation.getBoxPushTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getConfirmTime() == null ? ""
                    : sdf.format(whFbaAllocation.getConfirmTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getDeliverTime() == null ? ""
                    : sdf.format(whFbaAllocation.getDeliverTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getCancelTime() == null ? ""
                    : sdf.format(whFbaAllocation.getCancelTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getMergeTime() == null ? ""
                    : sdf.format(whFbaAllocation.getMergeTime())));
            data.add(batchReturnList);
        }
    }

    /**
     * 导出
     *
     * @param domain
     * @param ids
     * @param response
     */
    @RequestMapping(value = "asnJitDownload", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson ansJitDownload(@ModelAttribute("domain") WhFbaAllocationDo domain,
                                      @RequestParam(value = "ids", required = false) List<Integer> ids) {

        ResponseJson response = new ResponseJson();
        String[] HEADERS = {"编号", "YST", "备货单号","店铺", "销售", "备货类型", "SKU","标题", "库位", "件数", "拣货数量", "包装数量", "状态", "揽收方式","是否拆包",
                "发货单号", "揽收单号/自寄单号","揽收分摊费用", "分拣框号", "LBX号", "服务商侧运单号","推单时间","拣货时间", "播种人", "播种时间", "包装人", "包装时间", "交运人", "交运时间","装车时间","取消时间", "订单类型"};

        WhFbaAllocationQueryCondition query = domain.getQuery();
        if (query == null || CollectionUtils.isNotEmpty(ids)) {
            query = new WhFbaAllocationQueryCondition();
            query.setIds(ids);
            domain.setQuery(query);
        }
        domain.getPage().setPageNo(-1);
        domain.setQuery(query);
        domain.setOrderType(PickingTaskType.JIT_ASN_SINGLESINGLE.getCode());
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        String fileName = "SMT仓发海外仓发货单" + System.currentTimeMillis() + ".xlsx";

        whDownloadCenterService.downloading(fileName,HEADERS, WhDownloadContentEnum.JIT_ASN_ALLOCATION_ORDER,isAll,domain.getPage(),(page) -> {
            domain.setPage(page);
            domain.setWhFbaAllocations(null);
            queryWhFbaAllocations(domain);
            List<WhFbaAllocation> whFbaAllocations = domain.getWhFbaAllocations();
            return getExportList(whFbaAllocations,HEADERS,domain.getOrderType());
        });
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;
    }
    private void jitAsnOutputItems(List<List<String>> data, WhFbaAllocation whFbaAllocation,String[] HEADERS ) {
        if (Objects.isNull(whFbaAllocation)) {
            return;
        }
        for (WhFbaAllocationItem item : whFbaAllocation.getItems()) {
            List<String> batchReturnList = new ArrayList<String>(HEADERS.length);
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getFbaNo()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getShipmentId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getAccountNumber()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getSalesperson()));
            if (StringUtils.isBlank(whFbaAllocation.getTags())) {
                batchReturnList.add(POIUtils.transferObj2Str(null));
            } else {
                List<String> tagList = Arrays.asList(whFbaAllocation.getTags().split(","));
                if (tagList.contains(AsnPackageMethodEnum.URGENT_BACKUP.getCode().toString())) {
                    batchReturnList.add(
                            POIUtils.transferObj2Str(AsnPackageMethodEnum.URGENT_BACKUP.getName()));
                }else if (tagList.contains(AsnPackageMethodEnum.NORMAL_BACKUP.getCode().toString())) {
                    batchReturnList.add(
                            POIUtils.transferObj2Str(AsnPackageMethodEnum.NORMAL_BACKUP.getName()));
                }else {
                    batchReturnList.add(POIUtils.transferObj2Str(null));
                }
            }
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductSku()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getName()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getAllotLocationNumbers()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getPickQuantity()));
            if (ApvTypeEnum.MM.getCode().equalsIgnoreCase(whFbaAllocation.getApvType())) {
                batchReturnList.add(POIUtils.transferObj2Str(item.getGridQuantity()));
            }else {
                batchReturnList.add(POIUtils.transferObj2Str(item.getPickQuantity()));
            }
            batchReturnList.add(
                    POIUtils.transferObj2Str(AsnPrepareStatus.getNameByCode(whFbaAllocation.getStatus() + "")));
            batchReturnList.add(
                    POIUtils.transferObj2Str(whFbaAllocation.getWhAsnExtra() == null ? ""
                            : CollectMethodEnum.getNameByCode(whFbaAllocation.getWhAsnExtra().getCollectMethod() + "")));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getTransitType() != null && whFbaAllocation.getTransitType() == 1 ? "是":"否"));
            batchReturnList.add(POIUtils.transferObj2Str(item.getTag()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getWhAsnExtra() == null ? ""
                    : whFbaAllocation.getWhAsnExtra().getPickupOrderId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getSettleAmount()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getAsnPickBox() == null ? ""
                    : whFbaAllocation.getAsnPickBox().getNumber()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getTemuTagUrl()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getShippingOrderNo()));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            batchReturnList.add(POIUtils.transferObj2Str(
                    whFbaAllocation.getPushTime() == null ? "" : sdf.format(whFbaAllocation.getPushTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getPickTime() == null ? ""
                    : sdf.format(whFbaAllocation.getPickTime())));
            batchReturnList.add(whFbaAllocation.getSowUser() == null? "" : POIUtils.transferObj2Str(
                    TaglibUtils.getEmployeeNameByUserId(whFbaAllocation.getSowUser())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getSowTime() == null ? ""
                    : sdf.format(whFbaAllocation.getSowTime())));
            batchReturnList.add(whFbaAllocation.getPackUser() == null? "" : POIUtils.transferObj2Str(
                    TaglibUtils.getEmployeeNameByUserId(whFbaAllocation.getPackUser())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getPackTime() == null ? ""
                    : sdf.format(whFbaAllocation.getPackTime())));
            batchReturnList.add(whFbaAllocation.getDeliverBy() == null? "" : POIUtils.transferObj2Str(
                    TaglibUtils.getEmployeeNameByUserId(whFbaAllocation.getDeliverBy())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getDeliverTime() == null ? ""
                    : sdf.format(whFbaAllocation.getDeliverTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getLoadTime() == null ? ""
                    : sdf.format(whFbaAllocation.getLoadTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getCancelTime() == null ? ""
                    : sdf.format(whFbaAllocation.getCancelTime())));
            batchReturnList.add(
                    POIUtils.transferObj2Str(ApvTypeEnum.getNameByCode(whFbaAllocation.getApvType())));
            data.add(batchReturnList);
        }
    }

    /**
     * 导出
     *
     * @param domain
     * @param ids
     * @param response
     */
    @RequestMapping(value = "transferDownload", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson transferOrderDownload(@ModelAttribute("domain") WhFbaAllocationDo domain,
                                      @RequestParam(value = "ids", required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        String[] HEADERS = {"编号", "发货单号", "平台单号", "平台", "销售", "店铺", "包装方式", "SKU", "库位", "标题", "件数",
                "包装数量","是否分拣", "尺寸（CM）", "重量（g）", "状态", "追踪号", "接单时间", "推单时间", "包装时间", "交运时间",
                "装车时间","合单时间","单据类型"};

        WhFbaAllocationQueryCondition query = domain.getQuery();
        if (query == null || CollectionUtils.isNotEmpty(ids)) {
            query = new WhFbaAllocationQueryCondition();
            query.setIds(ids);
        }
        domain.getPage().setPageNo(-1);
        domain.setQuery(query);
        domain.setOrderType(PickingTaskType.ASN_TRANSFER.getCode());
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        String fileName = "中转仓发货单" + System.currentTimeMillis() + ".xlsx";

        whDownloadCenterService.downloading(fileName,HEADERS, WhDownloadContentEnum.ASN_TRANSFER_ALLOCATION_ORDER,isAll,domain.getPage(),(page) -> {
            domain.setPage(page);
            domain.setWhFbaAllocations(null);
            queryWhFbaAllocations(domain);
            List<WhFbaAllocation> whFbaAllocations = domain.getWhFbaAllocations();
            return getExportList(whFbaAllocations,HEADERS,domain.getOrderType());
        });
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;
    }

    private void asnTransferOutputItems(List<List<String>> data, WhFbaAllocation whFbaAllocation,String[] HEADERS ) {
        if (Objects.isNull(whFbaAllocation)) {
            return;
        }
        for (WhFbaAllocationItem item : whFbaAllocation.getItems()) {
            List<String> batchReturnList = new ArrayList<String>(HEADERS.length);
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getFbaNo()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getShipmentId()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getPurposeHouse()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getSalesperson()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getAccountNumber()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getWhAsnExtra() == null ? ""
                    : whFbaAllocation.getWhAsnExtra().getPackageMethodStr()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductSku()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getAllotLocationNumbers()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getName()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getQuantity()));
            batchReturnList.add(POIUtils.transferObj2Str(item.getLoadNum()));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getPickBoxStatusStr()));
            String size = null;
            if (item.getProductLength() != null || item.getProductWidth() != null
                    || item.getProductHeight() != null) {
                size = item.getProductLength() + "*" + item.getProductWidth() + "*" + item.getProductHeight();
            }
            batchReturnList.add(POIUtils.transferObj2Str(size));
            batchReturnList.add(POIUtils.transferObj2Str(item.getProductWeight()));
            batchReturnList.add(
                    POIUtils.transferObj2Str(AsnPrepareStatus.getNameByCode(whFbaAllocation.getStatus() + "")));

            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getTrackingNumber()));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getReceiveTime() == null ? ""
                    : sdf.format(whFbaAllocation.getReceiveTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getPushTime() == null ? ""
                    : sdf.format(whFbaAllocation.getPushTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getBoxPushTime() == null ? ""
                    : sdf.format(whFbaAllocation.getBoxPushTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getDeliverTime() == null ? ""
                    : sdf.format(whFbaAllocation.getDeliverTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getLoadTime() == null ? ""
                    : sdf.format(whFbaAllocation.getLoadTime())));
            batchReturnList.add(POIUtils.transferObj2Str(whFbaAllocation.getMergeTime() == null ? ""
                    : sdf.format(whFbaAllocation.getMergeTime())));
            batchReturnList.add(
                    POIUtils.transferObj2Str(ApvTypeEnum.getNameByCode(whFbaAllocation.getApvType())));
            data.add(batchReturnList);
        }
    }

    /**
     * fba部分sku取消
     *
     * @return
     */
    @RequestMapping(value = "cancelPortionFbaSku", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson synchronizationSkuStock(@RequestBody WhFbaAllocation allocation) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            responseJson = whFbaAllocationService.doCancelPortionFbaSku(allocation);
        } catch (Exception e) {
            responseJson.setMessage(e.getMessage());
            log.error(e.getMessage(), e);
        }
        return responseJson;
    }


    // 上传图片
    @ResponseBody
    @PostMapping(value = "uploadImage")
    public ResponseJson uploadImage(@RequestParam(value = "fbaId") Integer fbaId,
                                    @RequestParam(value = "fnSku") String fnSku, HttpServletRequest request) {
        log.info(" fba/allocation/uploadImage [" + fbaId + "][" + fnSku + "]");
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        if (StringUtils.isEmpty(fnSku) || fbaId == null) {
            response.setMessage("参数为空！");
            return response;
        }

        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(fbaId);
        List<WhFbaAllocation> fbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(fbaAllocations) || CollectionUtils.isEmpty(fbaAllocations.get(0).getItems())) {
            response.setMessage("没有找到FBA调拨发货单");
            return response;
        }
        List<WhFbaAllocationItem> fnSkuList = fbaAllocations.get(0).getItems().stream()
                .filter(i -> i.getFnSku().equals(fnSku)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fnSkuList)) {
            response.setMessage("发货单没有fnSku：" + fnSku);
            return response;
        }

        try {
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> fileMap = multiRequest.getFileMap();
            String[] extensions = {"jpg", "gif", "png", "jpeg"};
            MultipartFile file = fileMap.values().stream().collect(Collectors.toList()).get(0);

            String extension = POIUtils.getFileExtensionName(file.getOriginalFilename());
            if (ArrayUtils.contains(extensions, extension)) {
                String fileName = fnSku + "." + extension;

                // String url = SeaWeedFSUtils.URLTEST;// 测试环境
                String url = SeaweedFSUtils.getJoinUrl(null);
                String dateStr = String.valueOf(new Date().getTime());
                String filePath = "/fbaAllocation/" + dateStr + "/";
                String result = SeaWeedFSUtils.uploadFile(url, filePath, fileName, file.getBytes());
                if (StringUtils.isNotBlank(result) && result.contains("size")) {
                    String skuImg = url + filePath + fileName;
                    List<WhFbaAllocationItem> updateList = new ArrayList<>();
                    fnSkuList.forEach(i -> {
                        WhFbaAllocationItem item = new WhFbaAllocationItem();
                        item.setId(i.getId());
                        item.setSkuImg(skuImg);
                        updateList.add(item);
                    });
                    whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateList);
                    response.setMessage(skuImg);

                    boolean allTag = fbaAllocations.get(0).getItems().stream().allMatch(
                            i -> i.getTagBy() != null);
                    // 更新状态仓库审核(待复核）
                    // 单据从待贴标的判断规则为：单据类所有的FNSKU都已有图片、贴标人和加工类型
                    if (allTag) {
                        WhFbaAllocation fbaAllocation = new WhFbaAllocation();
                        fbaAllocation.setId(fnSkuList.get(0).getFbaId());
                        fbaAllocation.setStatus(AsnPrepareStatus.WAITING_CHECK.intCode());
                        whFbaAllocationService.updateWhFbaAllocation(fbaAllocation);
                        SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), "PDA贴标",
                                new String[][]{{"历史状态", AsnPrepareStatus.WAITING_LABEL.getName()},
                                        {"更改状态", AsnPrepareStatus.WAITING_CHECK.getName()}});
                        fbaAllocations.get(0).setStatus(AsnPrepareStatus.WAITING_CHECK.intCode());
                        whFbaAllocationService.sendMsg(fbaAllocations.get(0));
                    }
                }
            }

        } catch (Exception e) {
            log.warn(e.getMessage());
            response.setMessage(e.getMessage());
            return response;
        }

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 修改重量和体积
    @ResponseBody
    @RequestMapping(value = "updateActualWeight", method = { RequestMethod.POST })
    public ApiResult<?> updateActualWeightForFba(@RequestBody WhFbaAllocationItem param) {
        String apvNo = param.getFbaNo();
        Double actualWeight = param.getProductWeight();
        log.warn("编辑中转仓包裹重量 apvNo:" + apvNo + ", actualWeight:" + actualWeight);
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setIsTransferOrder(true);
        query.setFbaNo(apvNo);
        List<WhFbaAllocation> fbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(fbaAllocations)) {
            return ApiResult.newError("未找到中转仓发货单信息");
        }
        WhFbaAllocation fbaAllocation = fbaAllocations.get(0);
        if (!AsnPrepareStatus.DELIVER.intCode().equals(fbaAllocation.getStatus())
                && !AsnPrepareStatus.LOADED.intCode().equals(fbaAllocation.getStatus())){
            return ApiResult.newError("中转仓发货单交运后才能编辑尺寸重量");
        }
        List<WhFbaAllocationItem> items = fbaAllocation.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return ApiResult.newError("未找到中转仓发货单明细");
        }
        List<WhFbaAllocationItem> updateItems = new ArrayList<>();
        for (WhFbaAllocationItem item : items) {
            WhFbaAllocationItem update = new WhFbaAllocationItem();
            update.setId(item.getId());
            update.setProductWeight(actualWeight);
            update.setProductLength(param.getProductLength());
            update.setProductWidth(param.getProductWidth());
            update.setProductHeight(param.getProductHeight());
            updateItems.add(update);
        }
        whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(updateItems);
        WhFbaAllocationItem item = items.get(0);
        String logContent = String.format("编辑尺寸重量信息 原尺寸：%s*%s*%s 原重量：%s(KG) ",
                item.getProductLength(), item.getProductWidth(), item.getProductHeight(), item.getProductWeight());
        SystemLogUtils.FBAALLOCATIONLOG.log(fbaAllocation.getId(), logContent);
        return ApiResult.newSuccess();
    }


    public String genReceiveIdPdf(String receiveId){
        if (StringUtils.isBlank(receiveId)) {
            return null;
        }
        String filename = MD5Utils.encrypt(receiveId); // 中文名会导致前端LODOP组件无法打印
        String filePath = STATIC_FILE_PATH + "/file/pdf/pocketcard/temu/" + filename + ".pdf";
        String localip = "http://************/wms";
        Environment env = SpringUtils.getBean(Environment.class);
        if(!StringUtils.contains(env.getProperty("spring.profiles.active"),"prod")){
            localip = "http://************/wms";
//            localip = "http://localhost:8181/wms";
        }
        File file = new File(filePath);
        if (file.exists()) {
            return localip + "/file/pdf/pocketcard/temu/" + filename + ".pdf";
        }
        try {
            ByteArrayOutputStream os = printBagNoTag(receiveId);
            FileUtils.writeByteArrayToFile(new File(filePath), os.toByteArray());
            return localip + "/file/pdf/pocketcard/temu/" + filename + ".pdf";
        } catch (IOException e) {
            log.warn("genReceiveIdPdf error:" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 打印结袋卡号
     */
    public ByteArrayOutputStream printBagNoTag(String bagNo) {
        ByteArrayOutputStream ba = new ByteArrayOutputStream();
        try {
            float width = mmToPound(100d);
            float height = mmToPound(100d);
            Document document = new Document(new Rectangle(width, height));
            PdfWriter writer = PdfWriter.getInstance(document, ba);
            float margin = mmToPound(1);
            document.setMargins(margin, margin, 50, margin);
            document.open();
            PdfPTable table = new PdfPTable(new float[] { width / 5 });
            table.setTotalWidth(width - 2 * margin);
            table.setLockedWidth(true);
            table.setSpacingBefore(0f);
            table.setSpacingAfter(0f);
            table.getDefaultCell().setBackgroundColor(null);
            table.getDefaultCell().setNoWrap(false);
            table.getDefaultCell().setBorder(0);
            table.getDefaultCell().setMinimumHeight(height / 7 - 1);
            table.getDefaultCell().setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            table.getDefaultCell().setHorizontalAlignment(PdfPCell.ALIGN_CENTER);

            BaseFont baseFont = this.getBaseFont();
            Font myFont = new Font(baseFont, 20, Font.NORMAL);


            PdfPCell temuCell = new PdfPCell(new Paragraph(bagNo, myFont));
            temuCell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
            temuCell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            temuCell.setColspan(6);
            temuCell.setMinimumHeight(height / 8 - 5);
            temuCell.disableBorderSide(PdfPCell.LEFT);
            temuCell.disableBorderSide(PdfPCell.RIGHT);
            temuCell.disableBorderSide(PdfPCell.BOTTOM);
            temuCell.disableBorderSide(PdfPCell.TOP);
            table.addCell(temuCell);

            document.add(table);

            if (writer != null) {
                writer.flush();
            }
            if (document != null) {
                document.close();
            }
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
        return ba;
    }

    public int mmToPound(double milimeter) {
        double inch = milimeter / 25.4;
        double pound = inch * 72;
        return (int) pound;
    }

    private BaseFont getBaseFont() throws Exception {
        String fontFileName = "arialuni.ttf";
        String osName = System.getProperty("os.name");
        if ("Windows 7".equals(osName) || "Windows 8".equals(osName)) {
            fontFileName = "MSYHBD.TTF";
        }
        return BaseFont.createFont(STATIC_FILE_PATH + "/fonts/" + fontFileName, BaseFont.IDENTITY_H, BaseFont.EMBEDDED,
                BaseFont.NOT_CACHED, null, null);
    }

    /**
     * 导入修改SKU条码信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "importSkuBarcode", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson importSkuBarcode(HttpServletRequest request) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        ResultModel<WhFbaAllocation> resultModel = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();

        String[] titles = new String[]{"发货单号","SKU","SKU条码"};
        for (MultipartFile multiPartFile : fileMap.values()) {
            try {
                resultModel = POIUtils.readExcel(titles, multiPartFile, row -> {
                    int cellnum = 0;
                    WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
                    String fbaNo = POIUtils.cellValue2Str(row.getCell(cellnum++));
                    if (StringUtils.isBlank(fbaNo)) {
                        return null;
                    }
                    whFbaAllocation.setFbaNo(fbaNo);
                    whFbaAllocation.setShipmentId(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                    whFbaAllocation.setTaskNo(POIUtils.cellValue2Str(row.getCell(cellnum++)));

                    return whFbaAllocation;
                }, false);
                if (resultModel.getList().size() > 100000) {
                    response.setMessage("最多只能导入10万条！");
                    response.setStatus(StatusCode.FAIL);
                    return response;
                }
                List<WhFbaAllocationItem> whFbaAllocationItems=new ArrayList<>();
                if (!resultModel.isSuccess() || resultModel.getList().isEmpty()) {
                    response.setMessage(resultModel.getMsg());
                    return response;
                }
                List<WhFbaAllocation> list = resultModel.getList();
                List<String> fbaNoList = list.stream().map(f -> f.getFbaNo()).collect(Collectors.toList());
                WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
                queryCondition.setFbaNo(StringUtils.join(fbaNoList, ","));
                queryCondition.setQueryWhAsnExtra(true);
                List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);
                Map<String, String> skuMap = list.stream().collect(Collectors.toMap(f -> f.getFbaNo() + f.getShipmentId(), WhFbaAllocation::getTaskNo, (v1, v2) -> v1));
                for (WhFbaAllocation whFbaAllocation : whFbaAllocationList) {
                    for (WhFbaAllocationItem item : whFbaAllocation.getItems()) {
                        WhFbaAllocationItem whFbaAllocationItem =new WhFbaAllocationItem();
                        whFbaAllocationItem.setId(item.getId());
                        String skuBarcode = skuMap.get(whFbaAllocation.getFbaNo() + item.getProductSku());
                        if(StringUtils.isBlank(skuBarcode)){
                            continue;
                        }
                        whFbaAllocationItem.setSkuBarcode(skuBarcode);
                        whFbaAllocationItems.add(whFbaAllocationItem);
                    }
                }
                if (CollectionUtils.isNotEmpty(whFbaAllocationItems)) {
                    whFbaAllocationItemService.batchUpdateWhFbaAllocationItem(whFbaAllocationItems);
                }
            } catch (Exception e) {
                response.setMessage(e.getMessage());
                return response;
            }
        }
        response.setMessage("成功");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 导入修改采购单号
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "importPurchaseOrderNo", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson importPurchaseOrderNo(HttpServletRequest request) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        ResultModel<WhFbaAllocation> resultModel = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();

        String[] titles = new String[]{"采购单号","发货单号"};
        for (MultipartFile multiPartFile : fileMap.values()) {
            try {
                resultModel = POIUtils.readExcel(titles, multiPartFile, row -> {
                    int cellnum = 0;
                    WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
                    String purchaseOrderNo = POIUtils.cellValue2Str(row.getCell(cellnum++));
                    if (StringUtils.isBlank(purchaseOrderNo)) {
                        return null;
                    }
                    String fbaNo = POIUtils.cellValue2Str(row.getCell(cellnum++));
                    if (StringUtils.isBlank(fbaNo)) {
                        return null;
                    }
                    whFbaAllocation.setFbaNo(fbaNo);
                    whFbaAllocation.setShippingMethod(purchaseOrderNo);

                    return whFbaAllocation;
                }, false);
                if (resultModel.getList().size() > 100000) {
                    response.setMessage("最多只能导入10万条！");
                    response.setStatus(StatusCode.FAIL);
                    return response;
                }
                List<WhAsnExtra> whAsnExtras=new ArrayList<>();
                if (!resultModel.isSuccess() || resultModel.getList().isEmpty()) {
                    response.setMessage(resultModel.getMsg());
                    return response;
                }
                List<WhFbaAllocation> list = resultModel.getList();
                List<String> fbaNoList = list.stream().map(f -> f.getFbaNo()).collect(Collectors.toList());
                WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
                queryCondition.setFbaNo(StringUtils.join(fbaNoList, ","));
                queryCondition.setQueryWhAsnExtra(true);
                List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);
                Map<String, String> skuMap = list.stream().collect(Collectors.toMap(f -> f.getFbaNo(), WhFbaAllocation::getShippingMethod, (v1, v2) -> v1));
                for (WhFbaAllocation whFbaAllocation : whFbaAllocationList) {
                    WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
                    String purchaseOrderNo = skuMap.get(whFbaAllocation.getFbaNo());
                    if (whAsnExtra==null || StringUtils.isBlank(purchaseOrderNo)) {
                        continue;
                    }
                    WhAsnExtra update = new WhAsnExtra();
                    update.setId(whAsnExtra.getId());
                    update.setPurchaseOrderNo(purchaseOrderNo);
                    whAsnExtras.add(update);
                }
                if (CollectionUtils.isNotEmpty(whAsnExtras)) {
                    whAsnExtraService.batchUpdateWhAsnExtra(whAsnExtras);
                }
            } catch (Exception e) {
                response.setMessage(e.getMessage());
                return response;
            }
        }
        response.setMessage("成功");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }



    /**
     * 导入修改SKU信息
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "transferReturnOrder", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson importTransferReturnOrder(HttpServletRequest request) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        ResultModel<String> resultModel = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();

        String[] titles = new String[]{"物流单号"};
        for (MultipartFile multiPartFile : fileMap.values()) {
            try {
                resultModel = POIUtils.readExcel(titles, multiPartFile, row -> {
                    int cellnum = 0;
                    String trackingNumber = POIUtils.cellValue2Str(row.getCell(cellnum++));
                    if (StringUtils.isBlank(trackingNumber)) {
                        return null;
                    }
                    return trackingNumber;
                }, false);
                if (resultModel.getList().size() > 100000) {
                    response.setMessage("最多只能导入10万条！");
                    response.setStatus(StatusCode.FAIL);
                    return response;
                }
                if (resultModel.isSuccess() && !resultModel.getList().isEmpty()) {
                    response = whFbaAllocationService.importTransferReturnOrder(resultModel.getList());
                } else {
                    response.setMessage(resultModel.getMsg());
                    return response;
                }
            } catch (Exception e) {
                response.setMessage(e.getMessage());
                return response;
            }
        }
        return response;
    }
    @RequestMapping(value = "pushTransferReturnOrder", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson pushTransferReturnOrder() {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        String pushOmsPacDataJson = StringRedisUtils.get(RedisConstant.TRANSFER_RETURN_ORDER_PUSH_FAIL);
        if (StringUtils.isBlank(pushOmsPacDataJson)){
            response.setMessage("没有需要推送的中转仓退仓单据");
           return response;
        }
        List<PushOmsPacData>  pushOmsPacDataList = JSON.parseArray(pushOmsPacDataJson, PushOmsPacData.class);
        whFbaAllocationService.pushTransferReturnOrder(pushOmsPacDataList);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }
    /**
     * 打印SKU标签
     *
     * @param domain
     * @param id
     * @return
     */
    @GetMapping(value = "toPrintXiangMai")
    public String toPrintXiangMai(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("id") Integer id) {
        assembleBoxNoMap(domain, id);
        return "transfer/toPrintXiangMai";
    }

    private void assembleBoxNoMap(WhFbaAllocationDo domain, Integer id) {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setIsAsn(true);
        query.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(allocationList) && allocationList.get(0) != null
                && CollectionUtils.isNotEmpty(allocationList.get(0).getItems())) {
            WhFbaAllocation fbaAllocation = allocationList.get(0);

            fbaAllocation.getItems().forEach(i->{
                    if (i.getBoxNo()==null) {
                        i.setBoxNo(1);
                    }
            });

            // 根据fnSku分组,然后根据sku去重
            Map<String, WhFbaAllocationItem> boxMap = fbaAllocation.getItems().stream().collect(Collectors.toMap(i -> i.getBoxNo().toString(), i -> i, (k1, k2) -> k2));
            domain.setBoxMap(boxMap);
        }
    }

    @RequestMapping(value = "localPrintXiangmai", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson localPrintXiangmai(@RequestParam("id") Integer id
            ,@RequestParam(required = false,value = "consignOrderNo") String consignOrderNo) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (Objects.isNull(id) || id == 0){
            responseJson.setMessage("订单ID为空");
            return responseJson;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        query.setQueryWhAsnExtra(true);
        query.setQueryAsnPickBoxNumber(true);
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(whFbaAllocations)) {
            responseJson.setMessage("找不到发货单！");
            return responseJson;
        }
        try {
            WhFbaAllocation whFbaAllocation = whFbaAllocations.get(0);
            WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
            /*if (Objects.isNull(whAsnExtra) || StringUtils.isBlank(whAsnExtra.getConsignOrderNo())){
                responseJson.setMessage("发货单号为空！");
                return responseJson;
            }*/
            consignOrderNo=StringUtils.isNotBlank(consignOrderNo) ? consignOrderNo : whFbaAllocation.getItems().stream().map(WhFbaAllocationItem::getTag).findFirst().orElse(null);
            String pdfUrl = aliExpressCallService.createShippingMarkPdf(whFbaAllocation,consignOrderNo);
            String basePdfUrl = PdfUtils.convertPdfFromHttpToBase64(pdfUrl);
            responseJson.getBody().put("pdfUrl", basePdfUrl);
            //查询的箱唛存redis,后面结袋时需要用到
            StringRedisUtils.set(RedisConstant.SHIPPING_MARK_PDF_URL_BASE + consignOrderNo, basePdfUrl,5 * 24 * 60 * 60L );
            try {
                AsnPickBox asnPickBox = whFbaAllocation.getAsnPickBox();
                Integer boxNumber = null;
                if (Objects.nonNull(asnPickBox)){
                    boxNumber = asnPickBox.getNumber();
                }
                if (Objects.isNull(boxNumber) && StringUtils.isNotBlank(whAsnExtra.getWarehouseCode())){
                    Integer collectMethod = whAsnExtra.getCollectMethod();
                    String warehouseCode = whAsnExtra.getWarehouseCode();
                    if (collectMethod!=null){
                        warehouseCode+=collectMethod;
                    }
                    asnPickBox = asnPickBoxService.allotAsnPickBox(warehouseCode);
                    if (Objects.nonNull(asnPickBox)){
                        boxNumber = asnPickBox.getNumber();
                    }else{
                        responseJson.setMessage("分配分拣筐失败！");
                        return responseJson;
                    }
                }
                responseJson.setLocation(String.valueOf(boxNumber == null ? "" :boxNumber));
            }
            catch (Exception e) {
                log.error("获取分拣框失败：" + e.getMessage(), e);
            }
            responseJson.setMessage(pdfUrl);
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }
        catch (Exception e) {
            responseJson.setMessage("打印面单失败! " + e.getMessage());
            return responseJson;
        }
    }

    // 创建揽收单
    @RequestMapping(value = "createPickupOrder", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson createPickupOrder(@RequestParam(value = "ids",required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            String errorMsg = null;//whFbaAllocationService.createPickupOrderV2(ids);
            if (StringUtils.isNotBlank(errorMsg)) {
                response.setMessage(errorMsg);
                return response;
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("生成揽收单！"+e.getMessage());
            return response;
        }
    }

    // 同步揽收
    @RequestMapping(value = "syncWhFbaAllocationPickupOrderNo", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson syncWhFbaAllocationPickupOrderNo(@RequestParam(value = "ids",required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            query.setIds(ids);
            query.setIsAsn(true);
            query.setQueryWhAsnExtra(true);
            query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            for (WhFbaAllocation whFbaAllocation:allocationList) {
                whFbaAllocationService.syncWhFbaAllocationPickupOrderNo(whFbaAllocation.getFbaNo(),false);
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("生成揽收单！"+e.getMessage());
            return response;
        }
    }

    // 同步揽收
    @RequestMapping(value = "syncWhFbaAllocationPickupOrderNos2", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson syncWhFbaAllocationPickupOrderNo2(@RequestParam(value = "fbaNo") String fbaNo) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            query.setFbaNo(fbaNo);
            query.setIsAsn(true);
            query.setQueryWhAsnExtra(true);
            query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            for (WhFbaAllocation whFbaAllocation:allocationList) {
                aliExpressCallService.getLogisticsNoByConsignOrderNoV2(whFbaAllocation);
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("生成揽收单！"+e.getMessage());
            return response;
        }
    }

    //查询返回单同步LBX、揽收单号
    @RequestMapping(value = "syncJitFbaPickupOrderNos", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson syncJitFbaPickupOrderNos(@RequestParam(value = "ids",required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            query.setIds(ids);
            query.setIsAsn(true);
            query.setQueryWhAsnExtra(true);
            query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            for (WhFbaAllocation whFbaAllocation:allocationList) {
                boolean bool = whFbaAllocationService.syncJitFbaPickupOrderNos(whFbaAllocation.getFbaNo());
                if (!bool){
                    SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(),  "查询CO发货单失败！");
                }
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("查询发货单重试失败！"+e.getMessage());
            return response;
        }
    }

    // 手动交运
    @RequestMapping(value = "deliverOrder", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deliverOrder(@RequestParam(value = "ids",required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            if (CollectionUtils.isNotEmpty(ids)) {
                query.setIds(ids);
            }
            query.setIsAsn(true);
            query.setQueryWhAsnExtra(true);
            query.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
            query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isEmpty(allocationList)) {
                throw new RuntimeException("符合操作条件的数据为0！");
            }
            // 过滤出有揽收单号的单据
            allocationList = allocationList.stream().filter(a -> StringUtils.isNotBlank(a.getShippingOrderNo())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allocationList)) {
                throw new RuntimeException("待发货，有揽收单号的的数据为0！");
            }
            boolean failedFlag = false;
            Map<Integer,String> resultMap = new HashMap<>();
            for (WhFbaAllocation whFbaAllocation:allocationList) {
                try {
                    whFbaAllocationService.doDeliver(Arrays.asList(whFbaAllocation));
                    resultMap.put(whFbaAllocation.getId(),"交运成功");
                } catch (Exception e) {
                    log.error(e.getMessage(),e);
                    resultMap.put(whFbaAllocation.getId(),e.getMessage());
                    failedFlag=true;
                }
            }
            for (Map.Entry<Integer, String> entrie : resultMap.entrySet()) {
                SystemLogUtils.FBAALLOCATIONLOG.log(entrie.getKey(), entrie.getValue());
            }
            if (failedFlag) {
                response.setMessage("部分交运失败，详情查看日志");
                return response;
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("部分交运失败！"+e.getMessage());
            return response;
        }
    }

    // 取消拆包
    @RequestMapping(value = "cancelSplitOrder", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson cancelSplitOrder(@RequestParam(value = "ids",required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            if (CollectionUtils.isNotEmpty(ids)) {
                query.setIds(ids);
            }
            query.setIsAsn(true);
            query.setQueryWhAsnExtra(true);
            query.setStatusList(Arrays.asList(AsnPrepareStatus.CHECK_PRINT.intCode(),AsnPrepareStatus.PICK_STOCK_OUT.intCode()));
            query.setPackageMethodList(AsnPackageMethodEnum.getJitEnumCodeList());
            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isEmpty(allocationList)) {
                throw new RuntimeException("符合操作条件的数据为0！");
            }
            boolean failedFlag = false;
            Map<Integer,String> resultMap = new HashMap<>();
            List<WhFbaAllocation> updateList = new ArrayList<>();
            for (WhFbaAllocation whFbaAllocation:allocationList) {
                if (whFbaAllocation.getTransitType() == null || whFbaAllocation.getTransitType() != 1) {
                    resultMap.put(whFbaAllocation.getId(),"取消拆包失败，非拆包状态！");
                    failedFlag = true;
                    continue;
                }
                if (whFbaAllocation.getItems().stream().anyMatch(i -> i.getBoxNo() != null)) {
                    resultMap.put(whFbaAllocation.getId(),"取消拆包失败，已开始装箱，不能取消！");
                    failedFlag = true;
                    continue;
                }
                WhFbaAllocation updateWhFbaAllocation = new WhFbaAllocation();
                updateWhFbaAllocation.setId(whFbaAllocation.getId());
                updateWhFbaAllocation.setTransitType(0);
                updateList.add(updateWhFbaAllocation);
                resultMap.put(whFbaAllocation.getId(),"取消拆包成功！");
            }
            whFbaAllocationService.batchUpdateWhFbaAllocation(updateList);
            for (Map.Entry<Integer, String> entrie : resultMap.entrySet()) {
                SystemLogUtils.FBAALLOCATIONLOG.log(entrie.getKey(), entrie.getValue());
            }
            if (failedFlag) {
                response.setMessage("部分取消失败，详情查看日志");
                return response;
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("取消拆包失败！"+e.getMessage());
            return response;
        }
    }

    // 监听队列 组装发货单
    @PostConstruct
    public void smtQueryOrderRDelayedQueueListener() {
        WmsMqConfig wmsMqConfig = SpringUtils.getBean(WmsMqConfig.class);
        Environment env = SpringUtils.getBean(Environment.class);
        // 指定203.162执行

        String envConfig = StringUtils.contains(env.getProperty("spring.profiles.active"),"prod")?"160":"151";
        if(wmsMqConfig.isRabbitMqSwitchListener()
                && StringUtils.contains(env.getProperty("spring.profiles.active"),envConfig)){
            new Thread(() -> {
                while (true) {
                    String orderNo = null;
                    try {
                        orderNo = rBlockingDeque.take();
                        // 组装发货单
                        log.info("smtQueryOrderRDelayedQueueListener orderNos:{}", orderNo);
                        // 查询揽收单号并更新
                        if(StringUtils.isNotBlank(orderNo))
                            whFbaAllocationService.syncWhFbaAllocationPickupOrderNo(orderNo,true);
                    } catch (Exception e) {
                        log.error(String.format("smtQueryOrderRDelayedQueueListener error,orders[%s]", orderNo), e);
                      //  Thread.currentThread().interrupt();
                    }
                }
            }).start();
        }
    }


    // 创建发货单单
    @RequestMapping(value = "createJitCoOrder", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson createJitCoOrder(@RequestParam(value = "ids",required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            String errorMsg = whFbaAllocationService.jitCreateCoOrderv2(ids);
            if (StringUtils.isNotBlank(errorMsg)) {
                response.setMessage(errorMsg);
                return response;
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("创建发货单失败！"+e.getMessage());
            return response;
        }
    }

    // 打印揽收
    @ResponseBody
    @RequestMapping(value = "printAsnLanShou", method = {RequestMethod.GET})
    public ResponseJson printAsnLanShou(@RequestParam Integer id) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            query.setId(id);
            query.setIsAsn(true);
            query.setQueryWhAsnExtra(true);
            List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isEmpty(allocationList)) {
                response.setMessage("未找到出库单信息!");
                return response;
            }
            WhFbaAllocation whAsn = allocationList.get(0);
            if (whAsn.getWhAsnExtra() == null || StringUtils.isBlank(whAsn.getWhAsnExtra().getPickupOrderId())) {
                response.setMessage("揽收单号缺失!");
                return response;
            }
            String url = aliExpressCallService.createPickupShippingMarkPdf(whAsn);
            log.info("海外仓揽收单 id："+id+" url：" + url);
            response.setMessage(url);
            response.setLocation(convertPdfFromHttpToBase64(url));
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            log.error("printAsnLanShou error:" + e.getMessage(), e);
            response.setMessage("获取海外仓揽收单失败！"+e.getMessage());
            return response;
        }
    }

    @GetMapping(value = "printSKUGpsr")
    @ResponseBody
    public ResponseJson printSKUGpsr(@RequestParam("id") Integer id,
            @RequestParam(value = "fnSku", required = false) String fnSku) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("参数错误！");
            return response;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(id);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        
        if (CollectionUtils.isEmpty(allocationList) || CollectionUtils.isEmpty(allocationList.get(0).getItems())) {
            response.setMessage("未找到出库单信息！");
            return response;
        }
        List<WhFbaAllocationItem> items = allocationList.get(0).getItems();
        if (StringUtils.isNotBlank(fnSku)){
            items.removeIf(i->!StringUtils.equalsIgnoreCase(i.getFnSku(),fnSku));
        }
        if (CollectionUtils.isEmpty(items)){
            response.setMessage("未找到出库单对应的fnSku信息！");
            return response;
        }
        List<String> gpsrSiteList = Arrays.asList("DE", "IT", "FR", "ES", "PL", "NL", "SE");
        WhFbaAllocation allocation = allocationList.get(0);

        if (!gpsrSiteList.contains(allocation.getAmazonSite())){
            response.setMessage("不属于欧洲店铺，请打印普通SKU标签！");
            return response;
        }

        String accountNumber = allocation.getAmazonSite() + "-" + allocation.getAccountNumber();
        //获取店铺制造商信息
        AmazonAccountRelationQueryCondition relationQuery = new AmazonAccountRelationQueryCondition();
        relationQuery.setAccountNumber(accountNumber);
        AmazonAccountRelation accountRelation = amazonAccountRelationService.queryAmazonAccountRelation(relationQuery);
        Map<String, Object> resultMap = new HashMap<>();
        if (accountRelation != null) {
            Map<String, Object> relationMap = JSONObject.parseObject(JSONObject.toJSONString(accountRelation),
                    new TypeReference<Map<String, Object>>() {
                    });
            resultMap.putAll(relationMap);
        }
        try {
            // 获取SKU警告信息
            Map<String, WhFbaAllocationItem> itemMap = items.stream()
                    .collect(Collectors.toMap(i -> i.getProductSku().toUpperCase(), i -> i, (i1, i2) -> i1));

            Map<String, WhFbaAllocationItem> fnSkuMap = items.stream()
                    .collect(Collectors.toMap(i -> i.getFnSku().toUpperCase(), i -> i, (i1, i2) -> i1));
            ApiResult<List<SkuWaringDTO>> gpsrWarningBySku = whSkuService
                    .getGpsrWarningBySku(new ArrayList<>(itemMap.keySet()));
            Map<String, SkuWaringDTO> waringMap = new HashMap<>();
            if (gpsrWarningBySku.isSuccess() && CollectionUtils.isNotEmpty(gpsrWarningBySku.getResult())) {
                waringMap = gpsrWarningBySku.getResult().stream()
                        .collect(Collectors.toMap(s -> s.getSku().toUpperCase(), warning -> warning, (w1, w2) -> w1));
            }
            Map<String, SkuWaringDTO> finalWaringMap = waringMap;
            List<SkuWaringDTO> gpsrWarningList = new ArrayList<>();
            fnSkuMap.forEach((skuFn, item) -> {
                SkuWaringDTO warning = finalWaringMap.get(item.getProductSku().toUpperCase());
                if (warning == null)
                    warning = new SkuWaringDTO();
                warning.setSku(item.getProductSku());
                warning.setFnSku(item.getFnSku());
                warning.setSerialNumber(item.getSellSku());
                warning.setSkuName(item.getSellSkuName());
                warning.setQuantity(item.getQuantity());
                gpsrWarningList.add(warning);

            });
            resultMap.put("gpsrWarningList", gpsrWarningList);
        }
        catch (Exception e) {
            response.setMessage("获取SKU警告信息失败！" + e.getMessage());
            return response;
        }
        response.setBody(resultMap);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @GetMapping(value = "toLocalJitPrint")
    public String toLocalJitPrint(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam("apvId") Integer apvId) {
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(apvId);
        List<WhFbaAllocation> apvList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(apvList) || CollectionUtils.isEmpty(apvList.get(0).getWhApvItems()))
            return "asn/toPrintJItAsnTags";
        // 根据fnSku分组,然后根据sku去重
        Map<String, List<WhFbaAllocationItem>> fnSkuMap = apvList.get(0).getItems().stream()
                .collect(Collectors.groupingBy(i -> i.getFnSku(),
                        Collectors.collectingAndThen(Collectors.toCollection(
                                () -> new TreeSet<WhFbaAllocationItem>(Comparator.comparing(o -> o.getProductSku()))),
                                ArrayList::new)));
        domain.setFnSkuMap(fnSkuMap);
        return "asn/toPrintJItAsnTags";
    }

    @GetMapping(value = "fbaDetail")
    public String fbaDetail(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam Integer fbaId, @RequestParam(required = false) String sku){
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(fbaId);
        query.setIsAmazonFba(true);
        query.setQueryShipmentIds(true);
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(whFbaAllocations)) {
            return "transfer/fbaAllocationDetail";
        }
        WhFbaAllocation fbaAllocation = whFbaAllocations.get(0);
        if (StringUtils.isNotBlank(sku)) {
            List<String> skuList = CommonUtils.splitList(sku, ",");
            List<WhFbaAllocationItem> items = fbaAllocation.getItems().stream()
                    .filter(c -> skuList.contains(c.getFnSku()) || skuList.contains(c.getProductSku())).collect(Collectors.toList());
            fbaAllocation.setItems(items);
            domain.setStatusJson(sku);
        }
        fbaAllocation.buildFbaDetail();
        domain.setWhFbaAllocation(fbaAllocation);
        return "transfer/fbaAllocationDetail";
    }

    @GetMapping(value = "asnDetail")
    public String asnDetail(@ModelAttribute("domain") WhFbaAllocationDo domain, @RequestParam Integer fbaId, @RequestParam(required = false) String sku){
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setId(fbaId);
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(whFbaAllocations)) {
            return "transfer/fbaAllocationDetail";
        }
        WhFbaAllocation fbaAllocation = whFbaAllocations.get(0);
        if (StringUtils.isNotBlank(sku)) {
            List<String> skuList = CommonUtils.splitList(sku, ",");
            List<WhFbaAllocationItem> items = fbaAllocation.getItems().stream()
                    .filter(c -> skuList.contains(c.getFnSku()) || skuList.contains(c.getProductSku())).collect(Collectors.toList());
            fbaAllocation.setItems(items);
            domain.setStatusJson(sku);
        }
        fbaAllocation.buildAsnDetail();
        domain.setWhFbaAllocation(fbaAllocation);
        return "transfer/asnOrderDetail";
    }


    public String convertPdfFromHttpToBase64(String pdfUrl) {
        try {
            URL url = new URL(pdfUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            InputStream inputStream = connection.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            byte[] pdfBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(pdfBytes);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }


    @RequestMapping(value = "getBoxList", method = {RequestMethod.GET})
    @ResponseBody
    public ApiResult<?> getBoxList(@RequestParam("id") Integer id) {
        if (id==null) {
            return ApiResult.newError("参数错误");
        }
        WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
        queryCondition.setId(id);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            return ApiResult.newError("数据不存在");
        }
        return ApiResult.newSuccess(whFbaAllocationList.get(0).getBoxNumList());
    }


    /**
     * 打印箱号
     *
     */
    @RequestMapping(value = "printBoxList", method = {RequestMethod.GET})
    public String printBoxList(@ModelAttribute("domain") WhFbaAllocationDo domain,@RequestParam("id") Integer id, @RequestParam(value = "boxNo", required = false) Integer boxNo) {
        if (id==null) {
            return "transfer/printBoxFbaList";
        }
        WhFbaAllocationQueryCondition queryCondition=new WhFbaAllocationQueryCondition();
        queryCondition.setId(id);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            return "transfer/printBoxFbaList";
        }
        List<WhFbaAllocationItem> boxNumList = whFbaAllocationList.get(0).getBoxNumList();
        domain.setBoxNum(boxNumList.size());
        domain.setItems(boxNumList);
        if (boxNo!=null){
            List<WhFbaAllocationItem> fbaAllocationItems = boxNumList.stream().filter(item -> item.getBoxNo().equals(boxNo)).collect(Collectors.toList());
            domain.setItems(fbaAllocationItems);
        }
        return "transfer/printBoxFbaList";
    }

}