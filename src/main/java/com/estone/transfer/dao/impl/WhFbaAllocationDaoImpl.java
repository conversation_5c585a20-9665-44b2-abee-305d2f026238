package com.estone.transfer.dao.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.dao.mapper.WhApvDBField;
import com.estone.apv.dao.mapper.WhApvOutStockChainDBField;
import com.estone.apv.enums.ApvTypeEnum;
import com.estone.apv.util.ApvTransferUtils;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.asn.enums.OrderType;
import com.estone.common.SaleChannel;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.DateUtils;
import com.estone.common.util.DbTemplateUtils;
import com.estone.common.util.SqlerTemplate;
import com.estone.picking.enums.PickingTaskType;
import com.estone.temu.enums.TemuOrderStatus;
import com.estone.transfer.bean.TransferOrderMegerDto;
import com.estone.transfer.bean.TransferOrderMegerVo;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.dao.WhFbaAllocationDao;
import com.estone.transfer.dao.mapper.*;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;

@Repository("whFbaAllocationDao")
public class WhFbaAllocationDaoImpl implements WhFbaAllocationDao {

    private void setQueryCondition(SqlerRequest request, WhFbaAllocationQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly()) {
            request.setReadOnly(true);
        }

        request.addDataParam(WhFbaAllocationDBField.ID, DataType.INT, query.getId());

        request.addDataParam(WhFbaAllocationDBField.STATUS, DataType.INT, query.getStatus());
        //request.addDataParam(WhFbaAllocationDBField.SHIPMENT_ID, DataType.STRING, query.getShipmentId());
        request.addDataParam(WhFbaAllocationDBField.TASK_NO, DataType.STRING, query.getTaskNo());
        request.addDataParam(WhFbaAllocationDBField.BAT_NO, DataType.STRING, query.getBatNo());
        request.addDataParam(WhFbaAllocationDBField.SITE, DataType.STRING, query.getSite());
        request.addDataParam(WhFbaAllocationDBField.PICK_OUT, DataType.BOOLEAN, query.getPickOut());
        request.addDataParam(WhFbaAllocationDBField.SPLIT_REGION_FLAG, DataType.BOOLEAN, query.getSplitRegionFlag());
        request.addDataParam(WhFbaAllocationDBField.IS_ASN, DataType.BOOLEAN, query.getIsAsn());
        request.addDataParam("from_push_time", DataType.STRING, query.getStartTime());
        request.addDataParam("to_push_time", DataType.STRING, query.getEndTime());
        request.addDataParam("from_receive_time", DataType.STRING, query.getReceiveStartTime());
        request.addDataParam("to_receive_time", DataType.STRING, query.getReceiveEndTime());
        request.addDataParam("from_up_time", DataType.STRING, query.getUpStartTime());
        request.addDataParam("to_up_time", DataType.STRING, query.getUpEndTime());
        request.addDataParam("from_cancel_time", DataType.STRING, query.getCancelStartTime());
        request.addDataParam("to_cancel_time", DataType.STRING, query.getCancelEndTime());
        request.addDataParam("from_box_push_time", DataType.STRING, query.getBoxPushStartTime());
        request.addDataParam("to_box_push_time", DataType.STRING, query.getBoxPushEndTime());
        request.addDataParam("upStartDeliverTime", DataType.STRING, query.getUpStartDeliverTime());
        request.addDataParam("upEndDeliverTime", DataType.STRING, query.getUpEndDeliverTime());
        request.addDataParam("loadStartTime", DataType.STRING, query.getLoadStartTime());
        request.addDataParam("loadEndTime", DataType.STRING, query.getLoadEndTime());
        request.addDataParam("fromConfirmTime", DataType.STRING, query.getFromConfirmTime());
        request.addDataParam("toConfirmTime", DataType.STRING, query.getToConfirmTime());
        request.addDataParam(WhFbaAllocationDBField.PICK_BOX_STATUS,DataType.INT,query.getPickBoxStatus());
        request.addDataParam("collectMethod", DataType.INT, query.getCollectMethod());

        request.addDataParam(WhFbaAllocationDBField.LOAD_ID,DataType.INT,query.getLoadId());

        request.addDataParam("from_merge_time", DataType.STRING, query.getFromMergeTime());
        request.addDataParam("to_merge_time", DataType.STRING, query.getToMergeTime());
        if (StringUtils.isNotBlank(query.getStatusStr())){
            if (StringUtils.contains(query.getStatusStr(), ",")){
                request.addDataParam("status_list", DataType.INT, CommonUtils.splitIntList(query.getStatusStr(), ","));
            }else{
                request.addDataParam(WhFbaAllocationDBField.STATUS, DataType.INT, Integer.valueOf(query.getStatusStr()));
            }
        }

        if (StringUtils.isNotEmpty(query.getLbxNo()) && StringUtils.contains(query.getLbxNo(), ",")) {
            request.addDataParam("lbxNoList", DataType.STRING, CommonUtils.splitList(query.getLbxNo(), ","));
        }
        else {
            request.addDataParam("lbxNo", DataType.STRING, query.getLbxNo());
        }
        request.addDataParam("loadBy",DataType.INT,query.getLoadBy());
        if (query.isLoaded()) {
            request.addSqlDataParam("QUEST_LOAD_SQL"," AND (fa.status=17 or (fa.status=18 AND fa.load_id IS NULL ))");
        }

        // 查询海外仓的分拣箱号，就必须关联这个表
        if (Objects.nonNull(query.getQueryAsnPickBoxNumber()) && query.getQueryAsnPickBoxNumber()) {
            request.addSqlDataParam("QUERY_ASN_PICK_BOX_COLUMN", " ,(SELECT apb.number FROM asn_pick_box apb LEFT JOIN wh_asn_extra wae ON (CASE " +
                    "        WHEN wae.package_method in (4,7) THEN apb.pick_up_order_no = CONCAT(wae.warehouse_code,IFNULL(wae.collect_method,'') ) " +
                    "        ELSE apb.pick_up_order_no = wae.pick_up_order_id " +
                    "    END)  WHERE wae.wh_asn_id = fa.id ) AS 'apb.number'");
        }
        // 分拣框号
        if (StringUtils.isNotEmpty(query.getPickBoxNumberStr()) && StringUtils.contains(query.getPickBoxNumberStr(), ",")) {
            request.addDataParam("ASN_PICK_BOX_NUMBER_LIST", DataType.STRING, CommonUtils.splitList(query.getPickBoxNumberStr(), ","));
        }
        else if(StringUtils.isNotEmpty(query.getPickBoxNumberStr())) {
            request.addDataParam("ASN_PICK_BOX_NUMBER", DataType.STRING, query.getPickBoxNumberStr());
        }

        if (CollectionUtils.isNotEmpty(query.getStatusList())){
            request.addDataParam("statusList", DataType.STRING, query.getStatusList());
        }
        if (CollectionUtils.isNotEmpty(query.getIds())){
            request.addDataParam("idList", DataType.INT, query.getIds());
        }
        // 调拨单号
        if (StringUtils.isNotEmpty(query.getFbaNo()) && StringUtils.contains(query.getFbaNo(), ",")) {
            request.addDataParam("fbaNoList", DataType.STRING, CommonUtils.splitList(query.getFbaNo(), ","));
        }
        else {
            request.addDataParam(WhFbaAllocationDBField.FBA_NO, DataType.STRING, query.getFbaNo());
        }
        // 目的仓
        if (StringUtils.isNotEmpty(query.getPurposeHouse()) && StringUtils.contains(query.getPurposeHouse(), ",")) {
            request.addDataParam("purposeHouseForPage", DataType.STRING,
                    CommonUtils.splitList(query.getPurposeHouse(), ","));
        }
        else if (StringUtils.isNotEmpty(query.getPurposeHouse())){
            request.addDataParam(WhFbaAllocationDBField.PURPOSE_HOUSE, DataType.STRING, query.getPurposeHouse());
        }
        // 跟踪号右模糊查询
        if (StringUtils.isNotBlank(query.getRightLikeTrackingNumber())) {
            request.addDataParam("right_like_tracking_number", DataType.STRING, query.getRightLikeTrackingNumber() + "%");
        }else {
            // 追踪号
            if (StringUtils.isNotEmpty(query.getTrackingNumber()) && StringUtils.contains(query.getTrackingNumber(), ",")) {
                request.addDataParam("trackingNumberList", DataType.STRING,
                        CommonUtils.splitList(query.getTrackingNumber(), ","));
            }
            else {
                request.addDataParam(WhFbaAllocationDBField.TRACKING_NUMBER, DataType.STRING, query.getTrackingNumber());
            }
        }
        // 服务商侧运单号
        if (StringUtils.isNotEmpty(query.getShippingOrderNo()) && StringUtils.contains(query.getShippingOrderNo(), ",")) {
            request.addDataParam("shippingOrderNoList", DataType.STRING,
                    CommonUtils.splitList(query.getShippingOrderNo(), ","));
        }
        else {
            request.addDataParam(WhFbaAllocationDBField.SHIPPING_ORDER_NO, DataType.STRING, query.getShippingOrderNo());
        }
        // 账号
        if (StringUtils.isNotEmpty(query.getAccountNumber()) && StringUtils.contains(query.getAccountNumber(), ",")) {
            request.addDataParam("accountNumberList", DataType.STRING,
                    Arrays.asList(StringUtils.split(query.getAccountNumber(), ",")));
        }
        else {
            request.addDataParam(WhFbaAllocationDBField.ACCOUNT_NUMBER, DataType.STRING, query.getAccountNumber());
        }
        //库位
        if (StringUtils.isNotEmpty(query.getAllotLocationNumbers()) && StringUtils.contains(query.getAllotLocationNumbers(), ",")) {
            request.addDataParam("allotLocationNumberList", DataType.STRING,
                    Arrays.asList(StringUtils.split(query.getAllotLocationNumbers(), ",")));
        }
        else {
            request.addDataParam(WhFbaAllocationItemDBField.ALLOT_LOCATION_NUMBERS, DataType.STRING, query.getAllotLocationNumbers());
        }
        // sku
        if (StringUtils.isNotEmpty(query.getSku())) {
            if (StringUtils.contains(query.getSku(), ",")) {
                request.addDataParam("skuList", DataType.STRING, CommonUtils.splitList(query.getSku(), ","));
            }
            else {
                request.addDataParam("sku", DataType.STRING, query.getSku());
            }
        }
        // fnSku
        if (StringUtils.isNotEmpty(query.getFnSku())) {
            if (StringUtils.contains(query.getFnSku(), ",")) {
                request.addDataParam("fnSkuList", DataType.STRING, Arrays.asList(StringUtils.split(query.getFnSku(), ",")));
            }
            else {
                request.addDataParam("fnSku", DataType.STRING, query.getFnSku());
            }
        }
        // 上架差异
        if (StringUtils.isNotEmpty(query.getUpDiff())) {

            String sql = "";
            if (StringUtils.equalsIgnoreCase(query.getUpDiff(), "diff")) {
                sql = " AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE putaway_quantity > 0 AND putaway_diff != 0)";
            }
            else if (StringUtils.equalsIgnoreCase(query.getUpDiff(), "finish")) {
                sql = " AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE putaway_quantity > 0 AND putaway_diff = 0)";
            }
            request.addSqlDataParam("QUERY_DIFF", sql);
        }
        // 货件id查询 唯一货件
        if (StringUtils.isNotBlank(query.getShipmentId())) {
            if (StringUtils.contains(query.getShipmentId(), ",")) {
                request.addSqlDataParam("SHIPMENT_ID_SQL", " AND fa.shipment_id in ('" + StringUtils.join(StringUtils.split(query.getShipmentId(),","),  "','" )+ "')");
            }else{
                request.addSqlDataParam("SHIPMENT_ID_SQL", " AND fa.shipment_id='" + query.getShipmentId() + "' AND fa.status != 20");

            }
        } else if (StringUtils.isNotBlank(query.getSplitShipmentId())) {
            if (StringUtils.contains(query.getSplitShipmentId(), ",")) {
                request.addSqlDataParam("SHIPMENT_ID_SQL", " AND fa.shipment_id in ('"
                        + StringUtils.join(StringUtils.split(query.getSplitShipmentId().replaceAll(" ",""), ","), "','") + "')");
            }
            else {
                request.addSqlDataParam("SHIPMENT_ID_SQL", " AND fa.shipment_id='" + query.getSplitShipmentId() + "'");
            }
        }

        if (CollectionUtils.isNotEmpty(query.getShipmentIdList())){
            request.addDataParam("shipmentIdList",DataType.STRING,query.getShipmentIdList());
        }

        if(StringUtils.isNotBlank(query.getApvType())){
            if (StringUtils.contains(query.getApvType(),",")){
                request.addDataParam("apvTypeList", DataType.STRING, Arrays.asList(StringUtils.split(query.getApvType(), ",")));
            } else {
                request.addDataParam("apv_type", DataType.STRING, query.getApvType());
            }
        }

        // 支持APV类型列表查询
        if (CollectionUtils.isNotEmpty(query.getApvTypeList())) {
            request.addDataParam("apvTypeList", DataType.STRING, query.getApvTypeList());
        }

        if (query.getExceptionOrder() != null) {
            if (Boolean.TRUE.equals(query.getExceptionOrder())){
                request.addDataParam(WhFbaAllocationDBField.EXCEPTION_ORDER, DataType.BOOLEAN, query.getExceptionOrder());
            } else {
                request.addSqlDataParam("NOT_EXCEPTION_ORDER", " AND (fa.exception_order=0 or fa.exception_order is null ) ");
            }
        }

        if (query.getShippingOrderNoNotNull() != null && query.getShippingOrderNoNotNull()) {
            request.addSqlDataParam("NOT_NULL_TRACKING_NUMBER", " AND (fa.shipping_order_no is not null ) ");
        }
        if (query.getLbxNotNull() != null && query.getLbxNotNull()) {
            request.addSqlDataParam("NOT_NULL_LBX", " AND fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE temu_tag_url IS NOT NULL) ");
        }
        if (StringUtils.isNotBlank(query.getTags())) {
            //request.addDataParam(WhApvDBField.BUYER_CHECKOUT, DataType.STRING, query.getBuyerCheckout());
            request.addSqlDataParam("TAGS_SQL", "AND INSTR( fa.tags , '" + query.getTags() + "' ) > 0");
        }

        String transferOrderTypes = "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.URGENT.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.BACKUP.getCode()+")";

        List<String> transferOrderChannels = Arrays.asList(SaleChannel.CHANNEL_SMT,SaleChannel.CHANNEL_SHEIN);
        //查询非FBA数据
        if (query.getIsAmazonFba() != null && !query.getIsAmazonFba()) {
            request.addDataParam("purposeHouseList", DataType.STRING, SaleChannel.saleChannels);
            //smt 海外仓
            String SMT_ASN_SQL = null;
            if (StringUtils.equals(query.getOrderType(),"22")) {
                SMT_ASN_SQL = " AND CASE WHEN fa.purpose_house='"+SaleChannel.CHANNEL_SMT +"' THEN fa.is_asn is null ELSE 1=1 END";
            }else if (StringUtils.equals(query.getOrderType(),"21")){
                SMT_ASN_SQL = " AND  fa.purpose_house!='"+SaleChannel.CHANNEL_SMT +"'";
            }else  {
                SMT_ASN_SQL = " AND CASE WHEN fa.purpose_house='"+SaleChannel.CHANNEL_SMT +"' THEN fa.is_asn=true ELSE 1=1 END";
            }

            // 排除中转仓订单的数据
            request.addSqlDataParam("TRANSFER_ORDER_FILTER"," AND fa.id IN (SELECT wh_asn_id FROM wh_asn_extra ae WHERE (fa.purpose_house,ae.package_method) NOT IN ("+transferOrderTypes+") " +
                    " OR ae.package_method IS NULL ) "+SMT_ASN_SQL);
        } else if (query.getIsAmazonFba() != null && query.getIsAmazonFba()) {
            request.addDataParam("purposeHouseNotInList", DataType.STRING, SaleChannel.saleChannels);
        }
        if (query.getIsEurope() != null){
            if (Boolean.TRUE.equals(query.getIsEurope())){
                request.addSqlDataParam("IS_EUROPE_SQL", " AND site in ('DE','IT','FR','ES','PL','NL','SE') ");
            } else {
                request.addSqlDataParam("IS_EUROPE_SQL", " AND site not in ('DE','IT','FR','ES','PL','NL','SE') ");
            }
        }
        if (Boolean.TRUE.equals(query.isQueryShipmentIds())) {
            request.addSqlDataParam("SHIPMENTID_COLUMN", " , (select GROUP_CONCAT(shipment_id) from wh_fba_shipment where fba_id = fa.id) AS 'fa.shipmentIdStr' ");
        }

       /* String transferOrderTypes2 = "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT_HALF.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.URGENT.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.BACKUP.getCode()+")";*/
        // 查询中转仓订单数据
        if (query.getIsTransferOrder() != null && query.getIsTransferOrder()){
            request.addDataParam("purposeHouseList", DataType.STRING, transferOrderChannels);
            //smt 海外仓
            String SMT_ASN_SQL = null;
            if (StringUtils.equals(query.getOrderType(),"22")) {
                SMT_ASN_SQL = " AND CASE WHEN fa.purpose_house='"+SaleChannel.CHANNEL_SMT +"' THEN fa.is_asn is null ELSE 1=1 END";
            }else if (StringUtils.equals(query.getOrderType(),"21")){
                SMT_ASN_SQL = " AND  fa.purpose_house!='"+SaleChannel.CHANNEL_SMT +"'";
            }else {
                if (StringUtils.equals(query.getOrderType(),PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())) {
                    request.addDataParam("purposeHouseList", DataType.STRING, SaleChannel.CHANNEL_SMT);
                    // 是否拆包查询
                    if (query.getTransitType() != null) {
                        if (query.getTransitType() == 1) {
                            request.addDataParam(WhFbaAllocationDBField.TRANSIT_TYPE, DataType.INT, query.getTransitType());
                        } else {
                            request.addSqlDataParam("NOT_TRANSIT_TYPE", "  AND (fa.transit_type=0 or fa.transit_type is null ) ");
                        }
                    }
                    if (StringUtils.isNotBlank(query.getConsignOrderNoStr())) {
                        request.addDataParam("tags", DataType.STRING, CommonUtils.splitList(query.getConsignOrderNoStr(), ","));
                    }
                }
                SMT_ASN_SQL="";
                transferOrderTypes = transferOrderTypes + ",('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT.getCode()+"),"
                        + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT_HALF.getCode()+"),"
                        + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()+"),"
                        + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.SMT_ABROAD_WAREHOUSE.getCode()+")";
               // SMT_ASN_SQL = " AND CASE WHEN fa.purpose_house='"+SaleChannel.CHANNEL_SMT +"' THEN fa.is_asn=true ELSE 1=1 END";
            }
            request.addSqlDataParam("TRANSFER_ORDER_FILTER"," AND fa.id IN (SELECT wh_asn_id FROM wh_asn_extra ae WHERE (fa.purpose_house,ae.package_method) IN ("+transferOrderTypes+") " +
                    " OR ae.package_method IS NULL )"+ SMT_ASN_SQL);
        }

        if (query.getQueryWhAsnExtra() != null && query.getQueryWhAsnExtra()) {
            String packageMethodSql = "";
            if (query.getPackageMethod() != null){
                packageMethodSql = " AND package_method = " + query.getPackageMethod();
            }
            if (CollectionUtils.isNotEmpty(query.getPackageMethodList())) {
                packageMethodSql = " AND package_method IN (" + StringUtils.join(query.getPackageMethodList(), ",")
                        + ")";
            }
            if (StringUtils.isNotBlank(query.getWarehouseCode())){
                packageMethodSql +=" AND warehouse_code = '" + query.getWarehouseCode()+"'";
            }

            if (StringUtils.isNotBlank(query.getPickupOrderId())){
                String pickupOrderId = query.getPickupOrderId();
                if( StringUtils.contains(pickupOrderId,",")){
                    pickupOrderId = "'" + pickupOrderId.replaceAll(" ", "").replaceAll(",", "','") + "'";
                    packageMethodSql +=" AND pick_up_order_id IN (" +  pickupOrderId+ ")";
                }else {
                    packageMethodSql +=" AND pick_up_order_id = '" + pickupOrderId+"'";
                }
            }

            /*if (StringUtils.isNotBlank(query.getConsignOrderNoStr())){
                String consignOrderNoStr = query.getConsignOrderNoStr();
                if( StringUtils.contains(consignOrderNoStr,",")){
                    consignOrderNoStr = "'" + consignOrderNoStr.replaceAll(" ", "").replaceAll(",", "','") + "'";
                    packageMethodSql +=" AND consign_order_no IN (" +  consignOrderNoStr+ ")";
                }else {
                    packageMethodSql +=" AND consign_order_no = '" + consignOrderNoStr+"'";
                }
            }*/

            request.addSqlDataParam("ORDER_TYPE", " and fa.id IN (SELECT wh_asn_id FROM wh_asn_extra WHERE  type !="+OrderType.BOUTIQUE_ORDER.intCode()+packageMethodSql+" )");
        }
        if (query.getNumber()!=null) {
            request.addSqlDataParam("QUERY_JIT_PICK_BOX", " and fa.id IN (SELECT jpbi.fba_id FROM  jit_pick_box jpb LEFT JOIN  jit_pick_box_item jpbi on jpb.id=jpbi.box_id  WHERE jpb.number ="+query.getNumber()+" )");
        }

        if (query.getIsReturn() != null) {
            if (query.getIsReturn()) {
                request.addDataParam(WhFbaAllocationDBField.IS_RETURN, DataType.BOOLEAN, query.getIsReturn());
            } else {
                request.addSqlDataParam("notReturn", "  AND (fa.is_return=0 or fa.is_return is null ) ");
            }
        }

        // 构建时间参数
        builderApvTrackCondition(request,query);

        //库区
        if (org.apache.commons.lang.StringUtils.isNotBlank(query.getArea()) || org.apache.commons.lang.StringUtils.isNotBlank(query.getAccess())){
            String areaSql = builderArea(query);
            if (org.apache.commons.lang.StringUtils.isBlank(areaSql)){
                return;
            }
            String areaAndAccessCondition = " AND fa.id IN (SELECT inner_item.fba_id from wh_fba_allocation_item inner_item\n" +
                    " LEFT JOIN wh_apv_out_stock_chain inner_sku ON inner_item.product_sku = inner_sku.sku   \n" +
                    " LEFT JOIN wh_transfer_stock ws ON inner_sku.stock_id = ws.id  \n" +
                    " LEFT JOIN wh_stock wls ON ws.stock_id = wls.id \n" +
                    " where fa.fba_no = inner_sku.relevant_no AND ws.stock_id is not null AND wls.location_number \n" +
                    " IN ( SELECT location FROM wh_location WHERE 1=1  " + areaSql + ") ) ";
            request.addSqlDataParam("areaAndAccessCondition", areaAndAccessCondition);
        }

        if (Objects.nonNull(query.getFromRemainTime()) || Objects.nonNull(query.getToRemainTime())) {
            String dateNow = DateUtils.formatDate(new Date(), DateUtils.STANDARD_DATE_PATTERN);
            String stopDateSql = " DATE_ADD(fa.receive_time, INTERVAL " + query.SURVEILLANCE_TIME + " HOUR) ";
            String loadTimeSql = " (SELECT max(wsc.load_date) FROM wh_scan_shipment_to_apv wscta " +
                    "INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id " +
                    "WHERE is_transfer_house = true AND wscta.apv_no = fa.fba_no)  ";
            String needSMTSql = " AND fa.purpose_house = '" + SaleChannel.CHANNEL_SMT + "' ";
            // 剩余时间计算有三种情况
            // 1. 状态不为已装车或者已取消的，剩余时间即为截止时间减去当前时间
            // 2. 状态为已装车的，剩余时间即为截止时间减去装车时间
            // 2. 状态为已取消的，剩余时间即为即为截止时间减去取消时间
            if (Objects.nonNull(query.getFromRemainTime())) {
                String querySql = " AND (" +
                        "(fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND TIMESTAMPDIFF(HOUR, '" + dateNow + "'," + stopDateSql + ") >= " + query.getFromRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND TIMESTAMPDIFF(HOUR, " + loadTimeSql + ", " + stopDateSql + ") >= " + query.getFromRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND TIMESTAMPDIFF(HOUR, fa.cancel_time, " + stopDateSql + ") >= " + query.getFromRemainTime() + needSMTSql + ")" +
                        ") ";
                if (StringUtils.isNotBlank(querySql)) {
                    request.addSqlDataParam("FROM_REMAIN_TIME_SQL", querySql);
                }
            }
            if (Objects.nonNull(query.getToRemainTime())) {
                String querySql = " AND (" +
                        "(fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND TIMESTAMPDIFF(HOUR, '" + dateNow + "'," + stopDateSql + ") <= " + query.getToRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND TIMESTAMPDIFF(HOUR, " + loadTimeSql + ", " + stopDateSql + ") <= " + query.getToRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND TIMESTAMPDIFF(HOUR, fa.cancel_time, " + stopDateSql + ") <= " + query.getToRemainTime() + needSMTSql + ")" +
                        ") ";
                if(StringUtils.isNotBlank(querySql)){
                    request.addSqlDataParam("TO_REMAIN_TIME_SQL", querySql);
                }
            }
        }

        if (Objects.nonNull(query.getOverTime())) {
            String dateNow = DateUtils.formatDate(new Date(), DateUtils.STANDARD_DATE_PATTERN);
            String stopDateSql = " DATE_ADD(fa.receive_time, INTERVAL " + query.SURVEILLANCE_TIME + " HOUR) ";
            String loadTimeSql = " (SELECT max(wsc.load_date) FROM wh_scan_shipment_to_apv wscta " +
                    "INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id " +
                    "WHERE is_transfer_house = true AND wscta.apv_no = fa.fba_no)  ";
            String needSMTSql = " AND fa.purpose_house = '" + SaleChannel.CHANNEL_SMT + "' ";
            String querySql = "";
            if (query.getOverTime()) {
                // 超时有3种情况，
                // 1. 状态不为已装车或者已取消的，其当前时间大于截止时间
                // 2. 状态为已装车的，其装车时间大于截止时间
                // 2. 状态为已取消的，其取消时间大于截止时间
                querySql = " AND ( " +
                        " (fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND '" + dateNow + "' > " + stopDateSql + needSMTSql + ") " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND " + loadTimeSql + " > " + stopDateSql + needSMTSql + " ) " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND fa.cancel_time > " + stopDateSql + needSMTSql + ") " +
                        ") ";
            } else {
                // 不超时的有3种情况，
                // 1. 状态不为已装车或者已取消的，其当前时间小于截止时间
                // 2. 状态为已装车的，其装车时间小于等于截止时间
                // 2. 状态为已取消的，其取消时间小于等于截止时间
                querySql = " AND ( " +
                        " (fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND '" + dateNow + "' < " + stopDateSql + needSMTSql + ") " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND " + loadTimeSql + " <= " + stopDateSql + needSMTSql + " ) " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND fa.cancel_time <= " + stopDateSql + needSMTSql + ") " +
                        ") ";
            }
            if(StringUtils.isNotBlank(querySql)){
                request.addSqlDataParam("OVERTIME_SQL", querySql);
            }
        }

        if (Objects.nonNull(query.getCanPrint())){
            List<String> printOrderChannels = Arrays.asList(SaleChannel.CHANNEL_SMT,SaleChannel.CHANNEL_TEMU);
            String channelSql = StringUtils.wrap(StringUtils.join(printOrderChannels,"','"),"'");
            String sqlStr = " AND fa.purpose_house IN ("+channelSql+") ";
            if (query.getCanPrint()){
                sqlStr += " AND fa.id IN (" +
                        " SELECT wfai.fba_id " +
                        " FROM wh_fba_allocation_item wfai " +
                        " GROUP BY wfai.fba_id " +
                        " HAVING COUNT(1) = SUM(IF(wfai.temu_code_url IS NULL, 0, 1)) " +
                        " ) " +
                        " AND (" +
                        "   fa.id IN (" +
                        "   SELECT wae.wh_asn_id " +
                        "   FROM wh_asn_extra wae " +
                        "   WHERE wae.box_mark_url IS NOT NULL " +
                        "   ) " +
                        "   OR fa.transit_type = 1 )";
            }else{
                sqlStr += " AND ( fa.id IN (" +
                        " SELECT wfai.fba_id " +
                        " FROM wh_fba_allocation_item wfai " +
                        " GROUP BY wfai.fba_id " +
                        " HAVING COUNT(1) > SUM(IF(wfai.temu_code_url IS NULL, 0, 1)) " +
                        " ) " +
                        " OR (" +
                        "   fa.id IN (" +
                        "   SELECT wae.wh_asn_id " +
                        "   FROM wh_asn_extra wae " +
                        "   WHERE wae.box_mark_url IS NULL " +
                        "   ) " +
                        "   AND fa.transit_type != 1 ) " +
                        ")";
            }
            request.addSqlDataParam("CAN_PRINT", sqlStr);
        }
    }

    // 仓发根据时间查询
    private void builderApvTrackCondition (SqlerRequest request, WhFbaAllocationQueryCondition query) {
        if (StringUtils.equals(query.getOrderType(),PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())){
            request.addSqlDataParam("QUERY_APV_TRACK", " LEFT JOIN apv_track wt ON (wt.apv_no = fa.fba_no ) ");
            // request.addSqlDataParam("ORDER_TYPE", " and fa.id IN (SELECT wh_asn_id FROM wh_asn_extra WHERE  type !="+OrderType.BOUTIQUE_ORDER.intCode()+")");
            request.addSqlDataParam("QUERY_APV_TRACK_COLUMN", ",wt.pick_finish_time as 'fa.pickTime',wt.sow_finish_time as 'fa.sowTime'," +
                    "wt.pack_finish_time as 'fa.packTime', wt.sow_user as 'fa.sowUser', wt.pack_user as 'fa.packUser' ");
            StringBuilder apvTrackCondition = new StringBuilder();
            // 拣货时间筛选
            if (StringUtils.isNotBlank(query.getStartPickTime()) || StringUtils.isNotBlank(query.getEndPickTime())) {
                if (StringUtils.isNotBlank(query.getStartPickTime())) {
                    apvTrackCondition.append(" and wt.pick_finish_time >= '" + query.getStartPickTime() + "'");
                }
                if (StringUtils.isNotBlank(query.getEndPickTime())) {
                    apvTrackCondition.append(" and wt.pick_finish_time <= '" + query.getEndPickTime() + "'");
                }
            }
            // 根据播种完成时间查询
            if (StringUtils.isNotBlank(query.getStartSowTime()) || StringUtils.isNotBlank(query.getEndSowTime())) {
                if (StringUtils.isNotBlank(query.getStartSowTime())) {
                    apvTrackCondition.append(" and wt.sow_finish_time >= '" + query.getStartSowTime() + "'");
                }
                if (StringUtils.isNotBlank(query.getEndSowTime())) {
                    apvTrackCondition.append(" and wt.sow_finish_time <= '" + query.getEndSowTime() + "'");
                }
            }
            // 根据包装完成时间查询
            if (StringUtils.isNotBlank(query.getStartPackTime()) || StringUtils.isNotBlank(query.getEndPackTime())) {
                if (StringUtils.isNotBlank(query.getStartPackTime())) {
                    apvTrackCondition.append(" and wt.pack_finish_time >= '" + query.getStartPackTime() + "'");
                }
                if (StringUtils.isNotBlank(query.getEndPackTime())) {
                    apvTrackCondition.append(" and wt.pack_finish_time <= '" + query.getEndPackTime() + "'");
                }
            }
            // 根据拣货完成时间
            if (StringUtils.isNotBlank(apvTrackCondition.toString()))
                request.addSqlDataParam("APV_TRACK_CONDITION",
                   "AND fa.fba_no IN (select wt.apv_no from apv_track wt where 1 = 1  " + apvTrackCondition + ")");

        }
    }


    private static String builderArea(WhFbaAllocationQueryCondition query) {
        // 区域查询
        String area = query.getArea();
        // 通道查询
        String access = query.getAccess();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(area) || org.apache.commons.lang.StringUtils.isNotEmpty(access)) {
            String areaSql = "";
            String accessSql = "";
            if (org.apache.commons.lang.StringUtils.isNotEmpty(area)){
                String areaSplit = area.replaceAll(",", "','");
                areaSql = " AND location_region IN ('" + areaSplit + "')";
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(access)){
                String accessSplit = access.replaceAll(",", "','");
                accessSql = " AND location_aisle IN ('" + accessSplit + "')";
            }
            // 区域和通道都选了，忽略区域条件
            if (org.apache.commons.lang.StringUtils.isNotEmpty(area) && org.apache.commons.lang.StringUtils.isNotEmpty(access)){
                areaSql = "";
                List<String> areaList =  CommonUtils.splitList(area, ",");
                List<String> accessList =  CommonUtils.splitList(access, ",");
                Map<String, List<String>> map = new HashMap<>();
                for (String areaEntry :areaList){
                    List<String> list = map.get(areaEntry);
                    if (list == null){
                        list = new ArrayList<>();
                    }
                    for (String accessEntry :accessList){
                        if (accessEntry.startsWith(areaEntry)){
                            String value = accessEntry.replaceFirst(areaEntry, "");
                            list.add(value);
                        }
                    }
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)){
                        map.put(areaEntry, list);
                    }
                }
                accessSql = " AND (";
                int i = 0;
                for (String key: map.keySet()){
                    i++;
                    List<String> list = map.get(key);
                    if (i==1){
                        accessSql += "(";
                    }else {
                        accessSql += " OR (";
                    }
                    accessSql += " location_region ='" +key+ "'  AND location_aisle IN (";
                    for (int j=0;j<list.size();j++){
                        accessSql +=  "'" + list.get(j) + "'";
                        if (j!= list.size()-1){
                            accessSql += ",";
                        }else {
                            accessSql +=  ")";
                        }
                    }
                    accessSql +=  ")";
                }
                accessSql += " )";
            }
            return areaSql + accessSql;

        }
        return null;
    }


    @Override
    public int queryWhFbaAllocationCount(WhFbaAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhFbaAllocation> queryWhFbaAllocationList() {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationList");
        return SqlerTemplate.query(request, new WhFbaAllocationMapper());
    }

    @Override
    public List<WhFbaAllocation> queryWhFbaAllocationList(WhFbaAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhFbaAllocationMapper());
    }

    @Override
    public WhFbaAllocation queryWhFbaAllocation(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationByPrimaryKey");
        request.addDataParam(WhFbaAllocationDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhFbaAllocationMapper());
    }

    @Override
    public WhFbaAllocation queryWhFbaAllocation(WhFbaAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocation");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhFbaAllocationMapper());
    }

    @Override
    public void createWhFbaAllocation(WhFbaAllocation entity) {
        SqlerRequest request = new SqlerRequest("createWhFbaAllocation");
        request.addDataParam(WhFbaAllocationDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
        request.addDataParam(WhFbaAllocationDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
        request.addDataParam(WhFbaAllocationDBField.PURPOSE_HOUSE, DataType.STRING, entity.getPurposeHouse());
        request.addDataParam(WhFbaAllocationDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(WhFbaAllocationDBField.SITE, DataType.STRING, entity.getSite());
        request.addDataParam(WhFbaAllocationDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhFbaAllocationDBField.SM_CODE, DataType.STRING, entity.getSmCode());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_METHOD, DataType.STRING, entity.getShippingMethod());
        request.addDataParam(WhFbaAllocationDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_COMPANY, DataType.STRING, entity.getShippingCompany());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_ORDER_NO, DataType.STRING, entity.getShippingOrderNo());
        request.addDataParam(WhFbaAllocationDBField.PUSH_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhFbaAllocationDBField.BOX_PUSH_BY, DataType.INT, entity.getBoxPushBy());
        request.addDataParam(WhFbaAllocationDBField.BOX_PUSH_TIME, DataType.TIMESTAMP, entity.getBoxPushTime());
        request.addDataParam(WhFbaAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP, entity.getConfirmTime());
        request.addDataParam(WhFbaAllocationDBField.DELIVER_BY, DataType.INT, entity.getDeliverBy());
        request.addDataParam(WhFbaAllocationDBField.DELIVER_TIME, DataType.TIMESTAMP, entity.getDeliverTime());
        request.addDataParam(WhFbaAllocationDBField.CANCEL_TIME, DataType.TIMESTAMP, entity.getCancelTime());
        request.addDataParam(WhFbaAllocationDBField.DEPARTURE_TIME, DataType.TIMESTAMP, entity.getDepartureTime());
        request.addDataParam(WhFbaAllocationDBField.OVERSEAS_UP_TIME, DataType.TIMESTAMP, entity.getOverseasUpTime());
        request.addDataParam(WhFbaAllocationDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(WhFbaAllocationDBField.PDF_URL, DataType.STRING, entity.getPdfUrl());
        request.addDataParam(WhFbaAllocationDBField.BAT_NO, DataType.STRING, entity.getBatNo());
        request.addDataParam(WhFbaAllocationDBField.TAGS, DataType.STRING, entity.getTags());
        request.addDataParam(WhFbaAllocationDBField.SALESPERSON, DataType.STRING, entity.getSalesperson());
        request.addDataParam(WhFbaAllocationDBField.REMARK, DataType.STRING, entity.getRemark());
        request.addDataParam(WhFbaAllocationDBField.REJECT_REASON, DataType.STRING, entity.getRejectReason());
        request.addDataParam(WhFbaAllocationDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
        request.addDataParam(WhFbaAllocationDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
        request.addDataParam(WhFbaAllocationDBField.PICK_OUT, DataType.BOOLEAN, entity.getPickOut() == null ? false : entity.getPickOut());
        request.addDataParam(WhFbaAllocationDBField.SPLIT_REGION_FLAG, DataType.BOOLEAN, entity.getSplitRegionFlag());
        request.addDataParam(WhFbaAllocationDBField.LOGISTICS_AGING, DataType.STRING, entity.getLogisticsAging());
        request.addDataParam(WhFbaAllocationDBField.APV_TYPE, DataType.STRING, entity.getApvType());
        request.addDataParam(WhFbaAllocationDBField.RECEIVE_TIME, DataType.TIMESTAMP, entity.getReceiveTime());
        request.addDataParam(WhFbaAllocationDBField.MERGE_TIME, DataType.TIMESTAMP, entity.getMergeTime());
        request.addDataParam(WhFbaAllocationDBField.IS_ASN, DataType.BOOLEAN, entity.getIsAsn() == null ? false : entity.getIsAsn());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public int updateWhFbaAllocation(WhFbaAllocation entity) {
        SqlerRequest request = new SqlerRequest("updateWhFbaAllocationByPrimaryKey");
        request.addDataParam(WhFbaAllocationDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(WhFbaAllocationDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
        request.addDataParam(WhFbaAllocationDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
        request.addDataParam(WhFbaAllocationDBField.PURPOSE_HOUSE, DataType.STRING, entity.getPurposeHouse());
        request.addDataParam(WhFbaAllocationDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(WhFbaAllocationDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhFbaAllocationDBField.SM_CODE, DataType.STRING, entity.getSmCode());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_METHOD, DataType.STRING, entity.getShippingMethod());
        request.addDataParam(WhFbaAllocationDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_COMPANY, DataType.STRING, entity.getShippingCompany());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_ORDER_NO, DataType.STRING, entity.getShippingOrderNo());
        request.addDataParam(WhFbaAllocationDBField.PUSH_TIME, DataType.TIMESTAMP, entity.getPushTime());
        request.addDataParam(WhFbaAllocationDBField.BOX_PUSH_BY, DataType.INT, entity.getBoxPushBy());
        request.addDataParam(WhFbaAllocationDBField.BOX_PUSH_TIME, DataType.TIMESTAMP, entity.getBoxPushTime());
        request.addDataParam(WhFbaAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP, entity.getConfirmTime());
        request.addDataParam(WhFbaAllocationDBField.DELIVER_BY, DataType.INT, entity.getDeliverBy());
        request.addDataParam(WhFbaAllocationDBField.DELIVER_TIME, DataType.TIMESTAMP, entity.getDeliverTime());
        request.addDataParam(WhFbaAllocationDBField.CANCEL_TIME, DataType.TIMESTAMP, entity.getCancelTime());
        request.addDataParam(WhFbaAllocationDBField.DEPARTURE_TIME, DataType.TIMESTAMP, entity.getDepartureTime());
        request.addDataParam(WhFbaAllocationDBField.OVERSEAS_UP_TIME, DataType.TIMESTAMP, entity.getOverseasUpTime());
        request.addDataParam(WhFbaAllocationDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(WhFbaAllocationDBField.PDF_URL, DataType.STRING, entity.getPdfUrl());
        request.addDataParam(WhFbaAllocationDBField.BAT_NO, DataType.STRING, entity.getBatNo());
        request.addDataParam(WhFbaAllocationDBField.TRANSIT_TYPE, DataType.INT, entity.getTransitType());
        request.addDataParam(WhFbaAllocationDBField.TAGS, DataType.STRING, entity.getTags());
        request.addDataParam(WhFbaAllocationDBField.SALESPERSON, DataType.STRING, entity.getSalesperson());
        request.addDataParam(WhFbaAllocationDBField.REMARK, DataType.STRING, entity.getRemark());
        request.addDataParam(WhFbaAllocationDBField.REJECT_REASON, DataType.STRING, entity.getRejectReason());
        request.addDataParam(WhFbaAllocationDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
        request.addDataParam(WhFbaAllocationDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
        request.addDataParam(WhFbaAllocationDBField.PICK_OUT, DataType.BOOLEAN, entity.getPickOut());
        request.addDataParam(WhFbaAllocationDBField.SPLIT_REGION_FLAG, DataType.BOOLEAN, entity.getSplitRegionFlag());
        request.addDataParam(WhFbaAllocationDBField.LOGISTICS_AGING, DataType.STRING, entity.getLogisticsAging());
        request.addDataParam(WhFbaAllocationDBField.MERGE_TIME, DataType.TIMESTAMP, entity.getMergeTime());
        request.addDataParam(WhFbaAllocationDBField.IS_RETURN, DataType.BOOLEAN, entity.getIsReturn());
        request.addDataParam(WhFbaAllocationDBField.EXCEPTION_ORDER, DataType.BOOLEAN, entity.getExceptionOrder());
        request.addDataParam(WhFbaAllocationDBField.LOAD_ID, DataType.INT, entity.getLoadId());
        request.addSqlDataParam("RESET_SHIPPING_INFO", entity.getResetShippingInfoSql());
        return SqlerTemplate.execute(request);
    }

    @Override public int updateWhFbaAllocationByIdAndStatus(WhFbaAllocation entity, Integer status) {
        SqlerRequest request = new SqlerRequest("updateWhFbaAllocationByIdAndStatus");
        request.addDataParam(WhFbaAllocationDBField.ID, DataType.INT, entity.getId());
        request.addDataParam(WhApvDBField.CUR_STATUS, DataType.INT, status);

        request.addDataParam(WhFbaAllocationDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
        request.addDataParam(WhFbaAllocationDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
        request.addDataParam(WhFbaAllocationDBField.PURPOSE_HOUSE, DataType.STRING, entity.getPurposeHouse());
        request.addDataParam(WhFbaAllocationDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(WhFbaAllocationDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhFbaAllocationDBField.SM_CODE, DataType.STRING, entity.getSmCode());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_METHOD, DataType.STRING, entity.getShippingMethod());
        request.addDataParam(WhFbaAllocationDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_COMPANY, DataType.STRING, entity.getShippingCompany());
        request.addDataParam(WhFbaAllocationDBField.SHIPPING_ORDER_NO, DataType.STRING, entity.getShippingOrderNo());
        request.addDataParam(WhFbaAllocationDBField.PUSH_TIME, DataType.TIMESTAMP, entity.getPushTime());
        request.addDataParam(WhFbaAllocationDBField.BOX_PUSH_BY, DataType.INT, entity.getBoxPushBy());
        request.addDataParam(WhFbaAllocationDBField.BOX_PUSH_TIME, DataType.TIMESTAMP, entity.getBoxPushTime());
        request.addDataParam(WhFbaAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP, entity.getConfirmTime());
        request.addDataParam(WhFbaAllocationDBField.DELIVER_BY, DataType.INT, entity.getDeliverBy());
        request.addDataParam(WhFbaAllocationDBField.DELIVER_TIME, DataType.TIMESTAMP, entity.getDeliverTime());
        request.addDataParam(WhFbaAllocationDBField.CANCEL_TIME, DataType.TIMESTAMP, entity.getCancelTime());
        request.addDataParam(WhFbaAllocationDBField.DEPARTURE_TIME, DataType.TIMESTAMP, entity.getDepartureTime());
        request.addDataParam(WhFbaAllocationDBField.OVERSEAS_UP_TIME, DataType.TIMESTAMP, entity.getOverseasUpTime());
        request.addDataParam(WhFbaAllocationDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(WhFbaAllocationDBField.PDF_URL, DataType.STRING, entity.getPdfUrl());
        request.addDataParam(WhFbaAllocationDBField.BAT_NO, DataType.STRING, entity.getBatNo());
        request.addDataParam(WhFbaAllocationDBField.TAGS, DataType.STRING, entity.getTags());
        request.addDataParam(WhFbaAllocationDBField.SALESPERSON, DataType.STRING, entity.getSalesperson());
        request.addDataParam(WhFbaAllocationDBField.REJECT_REASON, DataType.STRING, entity.getRejectReason());
        request.addDataParam(WhFbaAllocationDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
        request.addDataParam(WhFbaAllocationDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
        request.addDataParam(WhFbaAllocationDBField.PICK_OUT, DataType.BOOLEAN, entity.getPickOut());
        request.addDataParam(WhFbaAllocationDBField.SPLIT_REGION_FLAG, DataType.BOOLEAN, entity.getSplitRegionFlag());
        request.addDataParam(WhFbaAllocationDBField.LOGISTICS_AGING, DataType.STRING, entity.getLogisticsAging());
        request.addDataParam(WhFbaAllocationDBField.MERGE_TIME, DataType.TIMESTAMP, entity.getMergeTime());
        request.addDataParam(WhFbaAllocationDBField.LOAD_ID, DataType.INT, entity.getLoadId());
        return SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhFbaAllocation(List<WhFbaAllocation> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhFbaAllocation");
            for (WhFbaAllocation entity : entityList) {
                request.addBatchDataParam(WhFbaAllocationDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
                request.addBatchDataParam(WhFbaAllocationDBField.PURPOSE_HOUSE, DataType.STRING,
                        entity.getPurposeHouse());
                request.addBatchDataParam(WhFbaAllocationDBField.ACCOUNT_NUMBER, DataType.STRING,
                        entity.getAccountNumber());
                request.addBatchDataParam(WhFbaAllocationDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhFbaAllocationDBField.SM_CODE, DataType.STRING, entity.getSmCode());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPPING_METHOD, DataType.STRING,
                        entity.getShippingMethod());
                request.addBatchDataParam(WhFbaAllocationDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());
                request.addBatchDataParam(WhFbaAllocationDBField.PUSH_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhFbaAllocationDBField.BOX_PUSH_BY, DataType.INT, entity.getBoxPushBy());
                request.addBatchDataParam(WhFbaAllocationDBField.BOX_PUSH_TIME, DataType.TIMESTAMP,
                        entity.getBoxPushTime());
                request.addBatchDataParam(WhFbaAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP,
                        entity.getConfirmTime());
                request.addBatchDataParam(WhFbaAllocationDBField.DELIVER_BY, DataType.INT, entity.getDeliverBy());
                request.addBatchDataParam(WhFbaAllocationDBField.DELIVER_TIME, DataType.TIMESTAMP,
                        entity.getDeliverTime());
                request.addBatchDataParam(WhFbaAllocationDBField.CANCEL_TIME, DataType.TIMESTAMP,
                        entity.getCancelTime());
                request.addBatchDataParam(WhFbaAllocationDBField.DEPARTURE_TIME, DataType.TIMESTAMP, entity.getDepartureTime());
                request.addBatchDataParam(WhFbaAllocationDBField.OVERSEAS_UP_TIME, DataType.TIMESTAMP, entity.getOverseasUpTime());
                request.addBatchDataParam(WhFbaAllocationDBField.TASK_NO, DataType.STRING,
                        entity.getTaskNo());
                request.addBatchDataParam(WhFbaAllocationDBField.PDF_URL, DataType.STRING,
                        entity.getPdfUrl());
                request.addBatchDataParam(WhFbaAllocationDBField.BAT_NO, DataType.STRING, entity.getBatNo());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPPING_COMPANY, DataType.STRING, entity.getShippingCompany());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPPING_ORDER_NO, DataType.STRING, entity.getShippingOrderNo());
                request.addBatchDataParam(WhFbaAllocationDBField.TAGS, DataType.STRING, entity.getTags());
                request.addBatchDataParam(WhFbaAllocationDBField.SALESPERSON, DataType.STRING, entity.getSalesperson());
                request.addBatchDataParam(WhFbaAllocationDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatchDataParam(WhFbaAllocationDBField.REJECT_REASON, DataType.STRING, entity.getRejectReason());
                request.addBatchDataParam(WhFbaAllocationDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
                request.addBatchDataParam(WhFbaAllocationDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
                request.addBatchDataParam(WhFbaAllocationDBField.PICK_OUT, DataType.BOOLEAN, entity.getPickOut() == null ? false : entity.getPickOut());
                request.addBatchDataParam(WhFbaAllocationDBField.LOGISTICS_AGING, DataType.STRING, entity.getLogisticsAging());
                request.addBatchDataParam(WhFbaAllocationDBField.APV_TYPE, DataType.STRING, entity.getApvType());
                request.addBatchDataParam(WhFbaAllocationDBField.RECEIVE_TIME, DataType.TIMESTAMP, entity.getReceiveTime());
                request.addBatchDataParam(WhFbaAllocationDBField.MERGE_TIME, DataType.TIMESTAMP, entity.getMergeTime());
                request.addBatchDataParam(WhFbaAllocationDBField.SPLIT_REGION_FLAG, DataType.BOOLEAN, entity.getSplitRegionFlag());
                request.addBatchDataParam(WhFbaAllocationDBField.IS_ASN, DataType.BOOLEAN, entity.getIsAsn() == null ? false : entity.getIsAsn());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhFbaAllocation(List<WhFbaAllocation> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhFbaAllocationByPrimaryKey");
            for (WhFbaAllocation entity : entityList) {
                request.addBatchDataParam(WhFbaAllocationDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(WhFbaAllocationDBField.FBA_NO, DataType.STRING, entity.getFbaNo());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPMENT_ID, DataType.STRING, entity.getShipmentId());
                request.addBatchDataParam(WhFbaAllocationDBField.PURPOSE_HOUSE, DataType.STRING,
                        entity.getPurposeHouse());
                request.addBatchDataParam(WhFbaAllocationDBField.ACCOUNT_NUMBER, DataType.STRING,
                        entity.getAccountNumber());
                request.addBatchDataParam(WhFbaAllocationDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhFbaAllocationDBField.SM_CODE, DataType.STRING, entity.getSmCode());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPPING_METHOD, DataType.STRING,
                        entity.getShippingMethod());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPPING_METHOD_BY_TMS,DataType.STRING,
                        entity.getShippingMethodByTms());
                request.addBatchDataParam(WhFbaAllocationDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());
                request.addBatchDataParam(WhFbaAllocationDBField.TRACKING_NUMBER_BY_TMS,DataType.STRING,
                        entity.getTrackingNumberByTms());
                request.addBatchDataParam(WhFbaAllocationDBField.PUSH_TIME, DataType.TIMESTAMP, entity.getPushTime());
                request.addBatchDataParam(WhFbaAllocationDBField.BOX_PUSH_BY, DataType.INT, entity.getBoxPushBy());
                request.addBatchDataParam(WhFbaAllocationDBField.BOX_PUSH_TIME, DataType.TIMESTAMP,
                        entity.getBoxPushTime());
                request.addBatchDataParam(WhFbaAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP,
                        entity.getConfirmTime());
                request.addBatchDataParam(WhFbaAllocationDBField.DELIVER_BY, DataType.INT, entity.getDeliverBy());
                request.addBatchDataParam(WhFbaAllocationDBField.DELIVER_TIME, DataType.TIMESTAMP,
                        entity.getDeliverTime());
                request.addBatchDataParam(WhFbaAllocationDBField.CANCEL_TIME, DataType.TIMESTAMP,
                        entity.getCancelTime());
                request.addBatchDataParam(WhFbaAllocationDBField.DEPARTURE_TIME, DataType.TIMESTAMP, entity.getDepartureTime());
                request.addBatchDataParam(WhFbaAllocationDBField.OVERSEAS_UP_TIME, DataType.TIMESTAMP, entity.getOverseasUpTime());
                request.addBatchDataParam(WhFbaAllocationDBField.TASK_NO, DataType.STRING,
                        entity.getTaskNo());
                request.addBatchDataParam(WhFbaAllocationDBField.PDF_URL, DataType.STRING,
                        entity.getPdfUrl());
                request.addBatchDataParam(WhFbaAllocationDBField.BAT_NO, DataType.STRING, entity.getBatNo());
                request.addBatchDataParam(WhFbaAllocationDBField.TRANSIT_TYPE, DataType.INT, entity.getTransitType());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPPING_COMPANY, DataType.STRING, entity.getShippingCompany());
                request.addBatchDataParam(WhFbaAllocationDBField.SHIPPING_ORDER_NO, DataType.STRING, entity.getShippingOrderNo());
                request.addBatchDataParam(WhFbaAllocationDBField.TAGS, DataType.STRING, entity.getTags());
                request.addBatchDataParam(WhFbaAllocationDBField.SALESPERSON, DataType.STRING, entity.getSalesperson());
                request.addBatchDataParam(WhFbaAllocationDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatchDataParam(WhFbaAllocationDBField.REJECT_REASON, DataType.STRING, entity.getRejectReason());
                request.addBatchDataParam(WhFbaAllocationDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
                request.addBatchDataParam(WhFbaAllocationDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
                request.addBatchDataParam(WhFbaAllocationDBField.PICK_OUT, DataType.BOOLEAN, entity.getPickOut());
                request.addBatchDataParam(WhFbaAllocationDBField.LOGISTICS_AGING, DataType.STRING, entity.getLogisticsAging());
                request.addBatchDataParam(WhFbaAllocationDBField.MERGE_TIME, DataType.TIMESTAMP, entity.getMergeTime());
                request.addBatchDataParam(WhFbaAllocationDBField.SPLIT_REGION_FLAG, DataType.BOOLEAN, entity.getSplitRegionFlag());
                request.addBatchDataParam(WhFbaAllocationDBField.IS_RETURN, DataType.BOOLEAN, entity.getIsReturn());
                request.addBatchDataParam(WhFbaAllocationDBField.EXCEPTION_ORDER, DataType.BOOLEAN, entity.getExceptionOrder());
                request.addBatchDataParam(WhFbaAllocationDBField.LOAD_ID, DataType.INT, entity.getLoadId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhFbaAllocation(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhFbaAllocationByPrimaryKey");
        request.addDataParam(WhFbaAllocationDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public int queryWhFbaAllocationAndItemCount(WhFbaAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationAndItemCount");
        setQueryCondition(request, query);
        request.addDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, query.getProcessType());
        request.addDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, query.getReProcess());
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhFbaAllocation> queryWhFbaAllocationAndItems(WhFbaAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationAndItems");
        setQueryCondition(request, query);
        if (query.getQueryWhAsnExtra() != null && query.getQueryWhAsnExtra()){
            request.addSqlDataParam("QUERY_WH_ASN_EXTRA", " LEFT JOIN wh_asn_extra ae ON (ae.wh_asn_id = fa.id and ae.type!=1) ");
           // request.addSqlDataParam("ORDER_TYPE", " and fa.id IN (SELECT wh_asn_id FROM wh_asn_extra WHERE  type !="+OrderType.BOUTIQUE_ORDER.intCode()+")");
            request.addSqlDataParam("QUERY_WH_ASN_EXTRA_COLUMN", ",ae.* ");
        }
        // 装车时间
        String loadTimeSql = ",(SELECT max(wsc.load_date) FROM wh_scan_shipment_to_apv wscta " +
                                            "INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id " +
                                            "WHERE is_transfer_house = true AND wscta.apv_no = fa.fba_no) as 'fa.load_time' ";
        request.addSqlDataParam("QUERY_LOAD_TIME", loadTimeSql);
        request.addDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, query.getProcessType());
        request.addDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, query.getReProcess());
        if (pager != null) {
            SQLDialect dial = DialectFactory.createDialect(null);

            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

            long end = start + pager.getPageSize() - 1L;

            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        if (query.getQueryWhAsnExtra() != null && query.getQueryWhAsnExtra()) {
            if (StringUtils.equals(query.getOrderType(),PickingTaskType.JIT_ASN_SINGLESINGLE.getCode())) {
                return SqlerTemplate.query(request, new WhFbaAllocationMapper(true, true, true,true, query));
            }
            return SqlerTemplate.query(request, new WhFbaAllocationMapper(true, true, true,false, query));
        }
        else {
            return SqlerTemplate.query(request, new WhFbaAllocationMapper(true, false, true, false, query));
        }
    }

    @Override
    public List<WhFbaAllocation> queryAllocationByPickingTaskId(Integer taskId) {
        SqlerRequest request = new SqlerRequest("queryAllocationByPickingTaskId");
        request.addDataParam("task_id", DataType.INT, taskId);
        return SqlerTemplate.query(request, new WhFbaAllocationAndItemForPickingMapper());
    }

    @Override
    public int queryGridQuantity(Integer taskId) {
        if (taskId == null)
            return 0;
        SqlerRequest request = new SqlerRequest("queryGridQuantity");
        request.addDataParam("task_id", DataType.INT, taskId);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhFbaAllocation> scanMaxPriorityGoodsPrintFba(WhFbaAllocationQueryCondition queryCondition) {
        SqlerRequest request = new SqlerRequest("queryMaxPriorityGoodsPrintFba");
        request.addDataParam("sku", DataType.STRING, queryCondition.getSku());

        if (StringUtils.isNotBlank(queryCondition.getTaskNo())) {
            request.addSqlDataParam("QUERY_BY_TASK_NO",   " AND fba.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item  LEFT JOIN wh_picking_task task ON task.id = task_item.task_id where task.task_status = 2 AND task.task_no= '"+queryCondition.getTaskNo()+"' )");
        }
        else{
            request.addSqlDataParam("QUERY_BY_TASK_NO",   " AND fba.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item  LEFT JOIN wh_picking_task task ON task.id = task_item.task_id where task.task_status = 2 AND task.task_type= "+ PickingTaskType.TRANSFER_SINGLESINGLE.intCode()+" )");
        }
        if (queryCondition.getPackCheckSplit() != null && !queryCondition.getPackCheckSplit()) {
            request.addSqlDataParam("QUERY_BY_TRANSIT_TYPE",
                    " AND (fba.transit_type IS NULL OR fba.transit_type != 1)");
        }

        if (queryCondition.getQueryAsnFirst() != null && queryCondition.getQueryAsnFirst()) {
            request.addDataParam("purposeHouseList", DataType.STRING, SaleChannel.saleChannels);
            String asn_first_sql = " AND fba.purpose_house!='" + SaleChannel.CHANNEL_SMT + "' AND fba.purpose_house!='"
                    + SaleChannel.CHANNEL_SHEIN + "'";
            request.addSqlDataParam("ASN_FIRST", asn_first_sql);
        }
        if (Boolean.TRUE.equals(queryCondition.getPackPriority())) {
            request.addSqlDataParam("CHANNEL_SORT", " fba.purpose_house DESC, ");
        }

        request.addDataParam("statusList", DataType.INT, queryCondition.getStatusList());
        request.addDataParam("fbaNo", DataType.STRING, queryCondition.getFbaNo());
        request.addDataParam("apvType", DataType.STRING, queryCondition.getApvType());

        return SqlerTemplate.query(request, new RowMapper<WhFbaAllocation>() {
            @Override
            public WhFbaAllocation mapRow(ResultSet rs, int i) throws SQLException {
                WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
                whFbaAllocation.setId(rs.getObject("fba.id") == null ? null : rs.getInt("fba.id"));
                whFbaAllocation.setStatus(rs.getObject("fba.status") == null ? null : rs.getInt("fba.status"));
                whFbaAllocation.setPurposeHouse(rs.getString("fba.purpose_house"));
                whFbaAllocation.setFbaNo(rs.getString("fba.fba_no"));
                whFbaAllocation.setAccountNumber(rs.getString("fba.account_number"));
                whFbaAllocation.setPdfUrl(rs.getString("fba.pdf_url"));
                whFbaAllocation.setTaskNo(rs.getString("fba.task_no"));
                whFbaAllocation.setTrackingNumber(rs.getString("fba.tracking_number"));
                whFbaAllocation.setIsAsn(rs.getBoolean("fba.is_asn"));
                whFbaAllocation.setTransitType(rs.getObject("fba.transit_type") == null ? null : rs.getInt("fba.transit_type"));
                WhAsnExtra whAsnExtra = new WhAsnExtra();
                whAsnExtra.setBoxMarkUrl(rs.getString("box_mark_url"));
                whAsnExtra.setPackageMethod(rs.getObject("package_method") == null ? null : rs.getInt("package_method"));
                whFbaAllocation.setWhAsnExtra(whAsnExtra);
                return whFbaAllocation;
            }
        });
    }

    @Override
    public void clearMergeTimeByFbaId(List<Integer> fbaIdList) {
        SqlerRequest request = new SqlerRequest("clearMergeTimeByFbaId");
        request.addDataParam("fba_id_list", DataType.INT, fbaIdList);
        SqlerTemplate.execute(request);
    }

    @Override
    public int unBoxCountInTask(String fbaNo) {
        if (StringUtils.isBlank(fbaNo))
            return 0;
        SqlerRequest request = new SqlerRequest("unBoxCountInTask");
        request.addDataParam("apv_no", DataType.STRING, fbaNo);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public int unPackCountInTask(String fbaNo) {
        if (StringUtils.isBlank(fbaNo))
            return 0;
        SqlerRequest request = new SqlerRequest("unPackCountInTask");
        request.addDataParam("apv_no", DataType.STRING, fbaNo);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhApvOutStockChain> queryToDeleteList(String endDeliverDate) {
        if (StringUtils.isBlank(endDeliverDate))
            endDeliverDate = DateUtils.dateToString(DateUtils.getBeforeDate(new Date(), 1), DateUtils.DEFAULT_FORMAT);

        String startDeliverDate = DateUtils.dateToString(
                DateUtils.getBeforeDate(DateUtils.stringToDate(endDeliverDate, DateUtils.DEFAULT_FORMAT), 1),
                DateUtils.DEFAULT_FORMAT);
        SqlerRequest request = new SqlerRequest("queryToDeleteList");

        request.addDataParam("to_deliver_date", DataType.STRING, endDeliverDate);
        request.addDataParam("from_deliver_date", DataType.STRING, startDeliverDate);
        request.addSqlDataParam("CANCEL_QUERY", " AND a.`status` = 20 AND a.push_time >= '" + startDeliverDate + "'");
        request.setReadOnly(true);
        List<WhApvOutStockChain> resultList = SqlerTemplate.query(request, new RowMapper<WhApvOutStockChain>() {
            @Override
            public WhApvOutStockChain mapRow(ResultSet rs, int i) throws SQLException {
                WhApvOutStockChain entity = new WhApvOutStockChain();
                entity.setId(rs.getInt(WhApvOutStockChainDBField.ID));
                entity.setRelevantNo(rs.getString(WhApvOutStockChainDBField.RELEVANT_NO));
                entity.setSku(rs.getString(WhApvOutStockChainDBField.SKU));
                entity.setSkuBarcode(rs.getString("skuBarcode"));
                return entity;
            }
        });

        List<WhApvOutStockChain> localApvList = queryLocalJitToDeleteList(endDeliverDate, startDeliverDate);

        if (CollectionUtils.isEmpty(localApvList))
            return resultList;

        if (CollectionUtils.isEmpty(resultList))
            return localApvList;

        resultList.addAll(localApvList);
        return resultList;
    }

    @Override
    public List<String> queryToDeletePocketCardList(String startTime, String endTime) {
        if (StringUtils.isBlank(endTime))
            endTime = DateUtils.dateToString(DateUtils.getBeforeDate(new Date(), 2), DateUtils.DEFAULT_FORMAT);
        if (StringUtils.isBlank(startTime))
            startTime = DateUtils.dateToString(
                    DateUtils.getBeforeDate(DateUtils.stringToDate(endTime, DateUtils.DEFAULT_FORMAT), 1),
                    DateUtils.DEFAULT_FORMAT);
        String sql = "SELECT DISTINCT bag_no FROM `wh_scan_shipment` WHERE `status` IN (7,20) AND `load_date` >= '"
                + startTime + "' AND `load_date` < '" + endTime + "'";
        List<Map<String, Object>> bagNoMap = DbTemplateUtils.executeSqlScript(sql);
        if (CollectionUtils.isEmpty(bagNoMap))
            return null;
        return bagNoMap.stream().map(map -> map.get("bag_no").toString()).collect(Collectors.toList());
    }

    // 取消置空
    @Override
    public void updateWhFbaAllocationByCancel(WhFbaAllocation whFbaAllocation, boolean isCo) {
        if (isCo) {
            DbTemplateUtils.executeSqlScriptUpdate("update wh_fba_allocation_item set tag = null,temu_tag_url=null where fba_id= " + whFbaAllocation.getId());
        } else {
            DbTemplateUtils.executeSqlScriptUpdate("update wh_fba_allocation wa INNER JOIN wh_asn_extra we on wa.id = we.wh_asn_id set wa.shipping_order_no = null,we.pick_up_order_id=null where wa.id= " + whFbaAllocation.getId());
        }
    }

    private List<WhApvOutStockChain> queryLocalJitToDeleteList(String endDeliverDate,String startDeliverDate){
        SqlerRequest request = new SqlerRequest("queryLocalJitToDeleteList");
        request.addDataParam("to_deliver_date", DataType.STRING, endDeliverDate);
        request.addDataParam("from_deliver_date", DataType.STRING, startDeliverDate);
        request.addSqlDataParam("CANCEL_QUERY", " AND apv.`status` = 20 AND apv.creation_date >= '" + startDeliverDate + "'");
        request.setReadOnly(true);
        List<Map<String, Object>> mapList = SqlerTemplate.queryForList(request);
        if (CollectionUtils.isEmpty(mapList))
            return null;
        List<WhApvOutStockChain> resultList = new ArrayList<>();
        mapList.forEach(map->{
            String apvNo = map.get("apv_no").toString();
            String sku = map.get("sku").toString();
            String buyerCheckout = map.get("buyer_checkout") == null ? null
                    : map.get("buyer_checkout").toString();
            if (StringUtils.isNotBlank(buyerCheckout)) {
                List<ApvTransferUtils.SkuInfo> skuInfoList = JSON
                        .parseObject(buyerCheckout, new TypeReference<List<ApvTransferUtils.SkuInfo>>() {
                        }).stream().sorted(Comparator.comparing(ApvTransferUtils.SkuInfo::getSaleSuiteArticleNumber,
                                Comparator.nullsFirst(Comparator.naturalOrder())).reversed())
                        .collect(Collectors.toList());
                skuInfoList.forEach(skuInfo -> {
                    WhApvOutStockChain apv = new WhApvOutStockChain();
                    apv.setRelevantNo(apvNo);
                    apv.setSku(sku);
                    apv.setSkuBarcode(skuInfo.getSkuBarcode());
                    resultList.add(apv);
                });
            }
        });

        return resultList;
    }


    // 查询合单
    @Override
    public int queryTransferOrderMegerCount(TransferOrderMegerDto query) {
        SqlerRequest request = new SqlerRequest("queryTransferOrderMegerCount");
        setTransferOrderMegerQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }
    // 查询合单列表
    @Override
    public List<TransferOrderMegerVo> queryTransferOrderMegers(TransferOrderMegerDto query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryTransferOrderMegers");
        setTransferOrderMegerQueryCondition(request, query);
        request.addSqlDataParam("QUERY_WH_ASN_EXTRA", " LEFT JOIN wh_asn_extra ae ON (ae.wh_asn_id = fa.id and ae.type!=1) ");
        request.addSqlDataParam("QUERY_WH_ASN_EXTRA_COLUMN", ",ae.package_method  as packageMethod");
        // 装车时间
        String loadTimeSql = ",(SELECT max(wsc.load_date) FROM wh_scan_shipment_to_apv wscta " +
                "INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id " +
                "WHERE is_transfer_house = true AND wscta.apv_no = fa.fba_no) as loadTime ";
        request.addSqlDataParam("QUERY_LOAD_TIME", loadTimeSql);

        List<TransferOrderMegerVo> transferOrderMegerVo = SqlerTemplate.query(request, new TransferOrderMegerMapper());
        if (pager != null ) {
            pager.setTotalCount(transferOrderMegerVo.size());
            if (CollectionUtils.isNotEmpty(transferOrderMegerVo)) {
                int page = pager.getPageNo();
                int pageSize = pager.getPageSize();
                int fromIndex = (page - 1) * pageSize;
                if (fromIndex >= transferOrderMegerVo.size()) {
                    return List.of(); // Return an empty list if the page is out of range
                }
                int toIndex = Math.min(fromIndex + pageSize, transferOrderMegerVo.size());
                return transferOrderMegerVo.subList(fromIndex, toIndex);
            }
        }
        return transferOrderMegerVo;
    }

    private void setTransferOrderMegerQueryCondition(SqlerRequest request, TransferOrderMegerDto query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            request.addDataParam("idList", DataType.INT, query.getIds());
        }
        if (CollectionUtils.isNotEmpty(query.getOrderNos())) {
            request.addDataParam("orderNoList", DataType.STRING, query.getOrderNos());
        }
        if (StringUtils.isNotBlank(query.getApvType())) {
            request.addDataParam("apv_type", DataType.STRING, query.getApvType());
            if (!StringUtils.equalsIgnoreCase(ApvTypeEnum.MM.getCode(),query.getApvType())) {
               String  APV_TYPE_TEMU_SQL=  StringUtils.equalsIgnoreCase(ApvTypeEnum.SS.getCode(),query.getApvType()) ? " tpoi.real_quantity=1" :  " tpoi.real_quantity>1";
               request.addSqlDataParam("APV_TYPE_TEMU"," AND "+APV_TYPE_TEMU_SQL);
            }
        }
        // 状态
        request.addDataParam("status", DataType.STRING,AsnPrepareStatus.WAITING_GEN.getCode());
        request.addDataParam("packageStatus", DataType.STRING, TemuOrderStatus.WAIT_MERGED.getCode());

        //库区
        if (StringUtils.isNotBlank(query.getArea()) || StringUtils.isNotBlank(query.getAccess())){
            WhFbaAllocationQueryCondition whFbaAllocationQueryCondition = new WhFbaAllocationQueryCondition();
            whFbaAllocationQueryCondition.setArea(query.getArea());
            whFbaAllocationQueryCondition.setAccess(query.getAccess());
            String areaSql = builderArea(whFbaAllocationQueryCondition);
            if (org.apache.commons.lang.StringUtils.isBlank(areaSql)){
                return;
            }

            String total_locations_sql = ",(SELECT COUNT(*) FROM wh_apv_out_stock_chain WHERE relevant_no = fa.fba_no)AS total_locations";

            String areaAndAccessCondition = ",( SELECT COUNT(*) FROM wh_apv_out_stock_chain c " +
                    "LEFT JOIN wh_transfer_stock ws ON ws.id = c.stock_id " +
                    "LEFT JOIN wh_stock wls ON ws.stock_id = wls.id" +
                    " WHERE relevant_no = fa.fba_no AND ws.stock_id IS NOT NULL " +
                    "AND wls.location_number IN ( SELECT location FROM wh_location WHERE 1=1 "+areaSql+") GROUP BY relevant_no )AS region_locations ";
            request.addSqlDataParam("areaAndAccessCondition", areaAndAccessCondition);
            request.addSqlDataParam("TOTAL_LOCATIONS_SQL", total_locations_sql);
            request.addSqlDataParam("LOCATIONS_HAVING", " HAVING region_locations = total_locations");
            request.addSqlDataParam("QUERY_COLUMN", " ,NULL AS region_locations,NULL AS total_locations");


            String areaAndAccessConditiontemu = ",( SELECT COUNT(*) FROM wh_apv_out_stock_chain c " +
                    "LEFT JOIN wh_transfer_stock ws ON ws.id = c.stock_id " +
                    "LEFT JOIN wh_stock wls ON ws.stock_id = wls.id" +
                    " WHERE relevant_no = tpo.prepare_order_no AND c.sku = tpoi.sku AND ws.stock_id IS NOT NULL " +
                    "AND wls.location_number IN ( SELECT location FROM wh_location WHERE 1=1 "+areaSql+") GROUP BY relevant_no )AS region_locations ";
            request.addSqlDataParam("areaAndAccessConditionTemu", areaAndAccessConditiontemu);

            String total_locations_temu_sql = ",(SELECT COUNT(*) FROM wh_apv_out_stock_chain WHERE relevant_no = tpo.prepare_order_no AND sku = tpoi.sku)AS total_locations";
            request.addSqlDataParam("TOTAL_LOCATIONS_TEMU_SQL", total_locations_temu_sql);
        }

        // sku
        if (StringUtils.isNotEmpty(query.getSku())) {
            if (StringUtils.contains(query.getSku(), ",")) {
                request.addDataParam("skuList", DataType.STRING, CommonUtils.splitList(query.getSku(), ","));
            }
            else {
                request.addDataParam("sku", DataType.STRING, query.getSku());
            }
        }

        request.addDataParam(WhFbaAllocationDBField.SPLIT_REGION_FLAG, DataType.BOOLEAN, query.getSplitRegionFlag());
        // 查询中转仓订单数据
        String transferOrderTypes = "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.URGENT.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.BACKUP.getCode()+")";
        transferOrderTypes = transferOrderTypes + ",('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT_HALF.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.SMT_ABROAD_WAREHOUSE.getCode()+")";

        request.addSqlDataParam("TRANSFER_ORDER_FILTER"," AND fa.id IN (SELECT wh_asn_id FROM wh_asn_extra ae WHERE (fa.purpose_house,ae.package_method) IN ("+transferOrderTypes+") )");
        request.addSqlDataParam("NOT_NULL_LBX", " AND case when fa.purpose_house ='"+SaleChannel.CHANNEL_SMT+"' " +
                "then fa.id IN (SELECT fba_id FROM wh_fba_allocation_item WHERE temu_tag_url IS NOT NULL) else 1=1 end");



        // 热销条件只查询temu
        String NOT_QUERY = null, NOT_QUERY_TEMU = null;
        if (query.getHotSale() != null) {
            request.addDataParam("hot_sale", DataType.BOOLEAN, query.getHotSale());
            NOT_QUERY = " AND 1=0";
        }
        // 多品多件不查询temu
        if (query.getApvType() != null && StringUtils.equalsIgnoreCase(ApvTypeEnum.MM.getCode(),query.getApvType())) {
            NOT_QUERY_TEMU = " AND 1=0";
        }
        if (query.getTag() != null) {
            request.addSqlDataParam("TAGS_SQL", " AND case when fa.purpose_house ='" + SaleChannel.CHANNEL_SMT + "' " +
                    "then INSTR( fa.tags, '" + query.getTag() + "' ) > 0  else fa.id IN (SELECT wh_asn_id FROM wh_asn_extra WHERE package_method = " + query.getTag() + ") end");
            if (AsnPackageMethodEnum.URGENT_BACKUP.getCode().equals(query.getTag())) {
                request.addDataParam("type", DataType.INT, 3);
            } else if (AsnPackageMethodEnum.NORMAL_BACKUP.getCode().equals(query.getTag())) {
                request.addDataParam("type", DataType.INT, 1);
            } else {
                NOT_QUERY_TEMU = " AND 1=0";
            }
        }
        // 平台
        if (StringUtils.isNotBlank(query.getSaleChannel())) {
            // 只查询temu
            if (StringUtils.equalsIgnoreCase(query.getSaleChannel(),SaleChannel.CHANNEL_TEMU)) {
                NOT_QUERY = " AND 1=0";
            } else {
                if (StringUtils.contains(query.getSaleChannel(), ",")) {
                    request.addDataParam("saleChannelList", DataType.STRING, CommonUtils.splitList(query.getSaleChannel(), ","));
                }
                else {
                    request.addDataParam("saleChannel", DataType.STRING,query.getSaleChannel());
                }
                if (!StringUtils.contains(query.getSaleChannel(),SaleChannel.CHANNEL_TEMU)){
                    NOT_QUERY_TEMU = " AND 1=0";
                }
            }
        }
        // SMT是否超时\SMT剩余时间(小时) 只查询smt
        if (Objects.nonNull(query.getFromRemainTime()) || Objects.nonNull(query.getToRemainTime())) {
            String dateNow = DateUtils.formatDate(new Date(), DateUtils.STANDARD_DATE_PATTERN);
            String stopDateSql = " DATE_ADD(fa.receive_time, INTERVAL " + WhFbaAllocation.SURVEILLANCE_TIME + " HOUR) ";
            String loadTimeSql = " (SELECT max(wsc.load_date) FROM wh_scan_shipment_to_apv wscta " +
                    "INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id " +
                    "WHERE is_transfer_house = true AND wscta.apv_no = fa.fba_no)  ";
            String needSMTSql = " AND fa.purpose_house = '" + SaleChannel.CHANNEL_SMT + "' ";
            // 剩余时间计算有三种情况
            // 1. 状态不为已装车或者已取消的，剩余时间即为截止时间减去当前时间
            // 2. 状态为已装车的，剩余时间即为截止时间减去装车时间
            // 2. 状态为已取消的，剩余时间即为即为截止时间减去取消时间
            if (Objects.nonNull(query.getFromRemainTime())) {
                String querySql = " AND (" +
                        "(fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND TIMESTAMPDIFF(HOUR, '" + dateNow + "'," + stopDateSql + ") >= " + query.getFromRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND TIMESTAMPDIFF(HOUR, " + loadTimeSql + ", " + stopDateSql + ") >= " + query.getFromRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND TIMESTAMPDIFF(HOUR, fa.cancel_time, " + stopDateSql + ") >= " + query.getFromRemainTime() + needSMTSql + ")" +
                        ") ";
                if (StringUtils.isNotBlank(querySql)) {
                    request.addSqlDataParam("FROM_REMAIN_TIME_SQL", querySql);
                }
            }
            if (Objects.nonNull(query.getToRemainTime())) {
                String querySql = " AND (" +
                        "(fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND TIMESTAMPDIFF(HOUR, '" + dateNow + "'," + stopDateSql + ") <= " + query.getToRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND TIMESTAMPDIFF(HOUR, " + loadTimeSql + ", " + stopDateSql + ") <= " + query.getToRemainTime() + needSMTSql + ")" +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND TIMESTAMPDIFF(HOUR, fa.cancel_time, " + stopDateSql + ") <= " + query.getToRemainTime() + needSMTSql + ")" +
                        ") ";
                if(StringUtils.isNotBlank(querySql)){
                    request.addSqlDataParam("TO_REMAIN_TIME_SQL", querySql);
                }
            }
            // 不查询temu
            NOT_QUERY_TEMU = " AND 1=0";
        }

        if (Objects.nonNull(query.getOverTime())) {
            String dateNow = DateUtils.formatDate(new Date(), DateUtils.STANDARD_DATE_PATTERN);
            String stopDateSql = " DATE_ADD(fa.receive_time, INTERVAL " + WhFbaAllocation.SURVEILLANCE_TIME + " HOUR) ";
            String loadTimeSql = " (SELECT max(wsc.load_date) FROM wh_scan_shipment_to_apv wscta " +
                    "INNER JOIN wh_scan_shipment wsc ON wscta.scan_shipment_id = wsc.id " +
                    "WHERE is_transfer_house = true AND wscta.apv_no = fa.fba_no)  ";
            String needSMTSql = " AND fa.purpose_house = '" + SaleChannel.CHANNEL_SMT + "' ";
            String querySql = "";
            if (query.getOverTime()) {
                // 超时有3种情况，
                // 1. 状态不为已装车或者已取消的，其当前时间大于截止时间
                // 2. 状态为已装车的，其装车时间大于截止时间
                // 2. 状态为已取消的，其取消时间大于截止时间
                querySql = " AND ( " +
                        " (fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND '" + dateNow + "' > " + stopDateSql + needSMTSql + ") " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND " + loadTimeSql + " > " + stopDateSql + needSMTSql + " ) " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND fa.cancel_time > " + stopDateSql + needSMTSql + ") " +
                        ") ";
            } else {
                // 不超时的有3种情况，
                // 1. 状态不为已装车或者已取消的，其当前时间小于截止时间
                // 2. 状态为已装车的，其装车时间小于等于截止时间
                // 2. 状态为已取消的，其取消时间小于等于截止时间
                querySql = " AND ( " +
                        " (fa.status NOT IN (" + StringUtils.join(Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode()), ",") + ")  AND '" + dateNow + "' < " + stopDateSql + needSMTSql + ") " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.LOADED.intCode() + " AND " + loadTimeSql + " <= " + stopDateSql + needSMTSql + " ) " +
                        " OR " +
                        " (fa.status = " + AsnPrepareStatus.CANCEL.intCode() + " AND fa.cancel_time <= " + stopDateSql + needSMTSql + ") " +
                        ") ";
            }
            if(StringUtils.isNotBlank(querySql)){
                request.addSqlDataParam("OVERTIME_SQL", querySql);
            }
            // 不查询temu
            NOT_QUERY_TEMU = " AND 1=0";
        }
        if (StringUtils.isNotBlank(NOT_QUERY))
            request.addSqlDataParam("NOT_QUERY",NOT_QUERY);
        if (StringUtils.isNotBlank(NOT_QUERY_TEMU))
            request.addSqlDataParam("NOT_QUERY_TEMU",NOT_QUERY_TEMU);
        request.addDataParam("from_push_time", DataType.STRING, query.getStartTime());
        request.addDataParam("to_push_time", DataType.STRING, query.getEndTime());
        request.addDataParam("from_receive_time", DataType.STRING, query.getReceiveStartTime());
        request.addDataParam("to_receive_time", DataType.STRING, query.getReceiveEndTime());
    }


}