package com.estone.checkin.action;

import static java.util.stream.Collectors.toList;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.warehouse.enums.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.estone.apv.util.ApvPackUtils;
import com.estone.checkin.bean.*;
import com.estone.checkin.domain.WhCheckInDo;
import com.estone.checkin.domain.WhPurchaseExpressRecordDo;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.*;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.checkin.utils.TranslateWhPurchaseOrderToPurchaseOrderUtils;
import com.estone.common.SelectJson;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.util.*;
import com.estone.multiplelocation.bean.AllocateLocationMatchRecord;
import com.estone.multiplelocation.bean.LocationMatchRecordQueryCondition;
import com.estone.multiplelocation.enums.AllocatePhaseEnum;
import com.estone.multiplelocation.service.LocationMatchRecordService;
import com.estone.sku.bean.*;
import com.estone.sku.enums.PrintSkuQrCodeRedisLock;
import com.estone.sku.enums.ProcessType;
import com.estone.sku.enums.PromptPageEnum;
import com.estone.sku.enums.SpecialTypeEnums;
import com.estone.sku.service.*;
import com.estone.statistics.service.WhInvoicingChangeMonthCountLocalService;
import com.estone.system.rabbitmq.model.PushCheckInException;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.skuverifyweightdiff.util.DirectTimeSettingUtils;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.service.*;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;
import com.estone.common.util.HttpUtils;
import com.estone.common.util.model.ApiResult;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * @ClassName: CheckInScanController
 * @Description: 入库扫描
 * <AUTHOR>
 * @date 2018年8月15日
 * @version 0.0.1
 *
 *
 */
@Controller
@RequestMapping(value = "checkin/scans")
@Slf4j
public class CheckInScanController {

    private static Logger logger = LoggerFactory.getLogger(CheckInScanController.class);

    private static String[] orderTypeArr = new String[] { "CGSC", "CGYP", "CGZS", "CGCW", "NCGSC", "NCGYP", "NCGZS",
            "NCGCW" };

    public static String shippingCpnStr = "中通快递,极兔快递,申通快递,圆通快递,韵达快递,邮政快递,优速快递,加运美快递,德邦快递,联昊通快递,平安达快递,京东快递,顺丰快递,信丰快递,速腾快递,中通快运,百世快运,安能物流,壹米滴答,顺心物流,韵达快运,德邦物流,京东物流,圆通-委派";
    @Resource
    private WhCheckInService whCheckInService;

    @Resource
    private WhCheckInItemService whCheckInItemService;

    @Resource
    private WhWarehouseService whWarehouseService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhPurchaseExpressRecordService whPurchaseExpressRecordService;

    @Resource
    private WhAllocateLocationRuleService whAllocateLocationRuleService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private WhBoxItemService whBoxItemService;

    @Resource
    private HistoryCheckInExceptionCountService historyCheckInExceptionCountService;

    @Resource
    private WhSkuQcCategoryService whSkuQcCategoryService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhPurchaseOrderService whPurchaseOrderService;

    @Resource
    private WhSkuExpandAttrService whSkuExpandAttrService;

    @Resource
    private WhSkuExtendService whSkuExtendService;

    @Autowired
    private MoveLocationTaskItemService moveLocationTaskItemService;

    @Resource
    private LocationMatchRecordService locationMatchRecordService;

    @Resource
    private WhInvoicingChangeMonthCountLocalService whInvoicingChangeMonthCountLocalService;
    
    @Resource
    private WhPackagingMaterialManagementService whPackagingMaterialManagementService;

    @Resource
    private WhSkuSpecialGoodsService whSkuSpecialGoodsService;

    @Resource
    private SkuAttributeTagService skuAttributeTagService;

    @Resource
    private PurchaseApvOutStockMatchService purchaseApvOutStockMatchService;

    @Resource
    private OutStockMatchHandelService outStockMatchHandelService;

    @Resource
    private ExemptionQcConfigurationService qcConfigurationService;

    @Resource
    private WhCheckInExcessService whCheckInExcessService;

    private void initFormData(@ModelAttribute("domain") WhCheckInDo domain) {
        /*ExceptionType[] exceptionTypes = ExceptionType.values();
        if (exceptionTypes.length > 0) {
            for (int i = 0; i < exceptionTypes.length; i++) {
                if (exceptionTypes[i].intCode() == 11 || exceptionTypes[i].intCode() == 18
                        || exceptionTypes[i].intCode() == 25 || exceptionTypes[i].intCode() == 27) {
                    exceptionTypes[i] = null;
                }
            }
        }*/
        domain.setExceptionTypes(ExceptionType.getSelectJson(true, List.of("11", "18", "25", "27")));
        domain.setCheckInPackageAttrs(SelectJson.getList(CheckInPackageAttrEnum.values()));
    }

    /**
     * 快递收货
     */
    @RequestMapping(value = "toExpressScan", method = { RequestMethod.GET })
    public String toExpresss(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain) {
        logger.info("checkin/scans/toExpressScan");
        WhWarehouse warehouse = whWarehouseService.queryOriginalWhWarehouse(true);
        domain.setWarehouseId(warehouse.getId());// 仓库
        domain.setShippingMethodList(Arrays.asList(StringUtils.split(shippingCpnStr, ",")));
        return "checkin/receive_express_scan_view";
    }

    /**
     * 快递收货
     */
    @RequestMapping(value = "query/express", method = { RequestMethod.GET })
    public String queryExpress(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
            @RequestParam("expressId") String expressId, @RequestParam("warehouseId") Integer warehouseId,
            @RequestParam("weight") Double weight, @RequestParam("quantity") Integer quantity,
            @RequestParam("boxNo") String boxNo) {
        Map<String, Object> validateMap = new HashMap<>();
        // 查询是否有未完成的物流收货
        if (CollectionUtils.isNotEmpty(isExpressLogUndone())) {
            validateMap.put("isError", true);
            validateMap.put("errorMsg", "还有物流子单号未完成称重!");
            domain.setValidateMap(validateMap);
            return "checkin/receive_express_scan";
        }
        WhBoxQueryCondition boxQuery = new WhBoxQueryCondition();
        boxQuery.setBoxNo(boxNo);
        WhBox box = whBoxService.queryWhBox(boxQuery);
        if (null == box) {
            validateMap.put("isError", true);
            validateMap.put("errorMsg", "当前周转框不存在");
            domain.setValidateMap(validateMap);
            return "checkin/receive_express_scan";
        }
        if (box.getStatus().equals(BoxStatus.NOT_USED.intCode())) {
            validateMap.put("isError", true);
            validateMap.put("errorMsg", "签收失败，当前周转框状态异常，请重新扫描周转框");
            domain.setValidateMap(validateMap);
            return "checkin/receive_express_scan";
        }
        if (box.getStatus().equals(BoxStatus.ALREADY_USED.intCode()) && null != box.getPresentUser()) {
            validateMap.put("isError", true);
            validateMap.put("errorMsg", "签收失败，当前周转框已被点数领取");
            domain.setValidateMap(validateMap);
            return "checkin/receive_express_scan";
        }

        logger.info("checkin/scans/query/express: expressId[" + expressId + "], warehouseId[" + warehouseId + "]");

        return whPurchaseExpressRecordService.receiveExpressRecord(domain, expressId, warehouseId, weight, quantity,
                boxNo);
    }


    @RequestMapping(value = "check/express", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson checkExpress(@RequestParam("expressId") String expressId) {
        logger.info("checkin/scans/check/express: expressId[" + expressId + "]");
        ResponseJson responseJson = new ResponseJson();
        List<String> expressIds = isExpressLogUndone();
        if (CollectionUtils.isEmpty(expressIds)) {
            StringRedisUtils.del(StringRedisUtils.CHECK_SCAN_LOG_KEY + DataContextHolder.getOperationId());
        }
        else if (!expressIds.contains(expressId)) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("还有物流子单号未完成称重!");
            return responseJson;
        }

        // 先校验是否有签收记录
        WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
        query.setTrackingNumber(expressId);
        List<WhPurchaseExpressRecord> records = whPurchaseExpressRecordService.queryWhPurchaseExpressRecords(query,
                null);
        if (CollectionUtils.isNotEmpty(records)) {
            if (PurchaseExpressRecordStatus.REJECT.intCode().equals(records.get(0).getStatus())) {
                responseJson.setMessage("该快递已拒收:" + records.get(0).getComment());
            }
            else {
                responseJson.setMessage("重复操作，该快递已签收");
            }
            responseJson.setStatus(StatusCode.FAIL);
            return responseJson;
        }

        String oldExpressId = null;
        if (expressId.indexOf("=") > 0) {
            oldExpressId = expressId.split("=")[0];
        }

        WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
        queryCondition.setTrackingNumber(expressId);
        if (StringUtils.isNotBlank(oldExpressId)) {
            // 物流子单号截取
            queryCondition.setTrackingNumber(oldExpressId);
        }
        List<WhPurchaseOrder> orders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(queryCondition, null);
        List<PurchaseOrder> purchaseOrders = TranslateWhPurchaseOrderToPurchaseOrderUtils
                .translateWhPurchaseOrderToPurchaseOrder(orders);

        if (CollectionUtils.isNotEmpty(purchaseOrders) && StringUtils
                .equalsIgnoreCase(PurchaseOrderType.NCGHC.getCode(), purchaseOrders.get(0).getPurchaseOrderType())) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("耗材订单直接在耗材入库页面入库，不用收货!");
            return responseJson;
        }

        int existTj = Optional.ofNullable(purchaseOrders).orElse(new ArrayList<>()).stream()
                .filter(p -> p.getFlagsName().indexOf("特急") >= 0).collect(Collectors.toList()).size();
        if (existTj > 0) {
            responseJson.setMessage("特急包裹!");
            return responseJson;
        }

        return responseJson;
    }
    
    /**
     * 物流收货
     */
    @RequestMapping(value = "query/express/log", method = { RequestMethod.GET })
    public String queryExpress(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
            @RequestParam("expressId") String expressId, @RequestParam("weight") Double weight,
            @RequestParam("quantity") Integer quantity,
            @RequestParam(value = "boxNo", required = false) String boxNo) {
        logger.info("checkin/scans/query/express: expressId[" + expressId + "], boxNo[" + boxNo + "]");
        return whPurchaseExpressRecordService.receiveExpressRecordLog(domain, expressId, weight, quantity, boxNo);
    }

    /**
     * 是否存在没有完成的物流收货
     * @return
     */
    private List<String> isExpressLogUndone(){
        String data = StringRedisUtils.get(StringRedisUtils.CHECK_SCAN_LOG_KEY + DataContextHolder.getOperationId());
        if (StringUtils.isNotBlank(data)) {
            List<String> expressIds = JSON.parseArray(data, String.class);
            return expressIds;
        }
        return null;
    }

    /**
     *
     * @Title: toPurchaseScan
     * @Description: 进入扫描快递单页面
     *
     * @param domain
     * @return
     */
    @RequestMapping(value = "toPurchaseScan", method = { RequestMethod.GET })
    public String toPurchaseScan(@ModelAttribute("domain") WhCheckInDo domain) {
        logger.info("checkin/scans/toPurchaseScan");
        String key = RedisConstant.CHECKIN_SUCCESS_COUNT_KEY + DateUtils.formatDate(new Date(), DateUtils.DEFAULT_FORMAT) + DataContextHolder.getUserId();
        Integer l = StringRedisUtils.get(key);
        if (l != null) {
            domain.setSuccessCount(l);
        }
        return "checkin/check_scan_purchase_view";
    }

    /**
     * 扫描周转框（只适用于收货周转框）
     *
     * @param domain
     * @param boxNo
     * @param type
     * @return
     */
    @RequestMapping(value = "checkInCheckReceiveBox", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson checkInCheckReceiveBox(@ModelAttribute("domain") WhCheckInDo domain,
            @RequestParam("boxNo") String boxNo, @RequestParam("type") String type) {
        logger.info("checkin/scans/checkInCheckReceiveBox: boxNo[" + boxNo + "]");
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        Integer currentUserId = DataContextHolder.getUserId();
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        WhBox bindWhBox = whBoxService.queryWhBoxByPresentUser(currentUserId);
        if (whBox != null) {
            if (StringUtils.isNotBlank(type) && type.equals("receive")
                    && !BoxType.getShBoxIntCode().contains(whBox.getType())) {
                responseJson.setMessage("此周转筐不是收货专用,请重新输入");
                responseJson.setLocation("checkin/check_scan_purchase_view");
                return responseJson;
            }
            if (null != bindWhBox && !bindWhBox.getBoxNo().equals(whBox.getBoxNo())) {
                responseJson.setMessage("请先处理未完成的周转框任务，" + bindWhBox.getBoxNo());
                responseJson.setLocation("checkin/check_scan_purchase_view");
                return responseJson;
            }
            if (null != whBox.getPresentUser() && !whBox.getPresentUser().equals(currentUserId)) {
                responseJson.setMessage("当前周转框已被领取");
                responseJson.setLocation("checkin/check_scan_purchase_view");
                return responseJson;
            }
            WhBoxItemQueryCondition query = new WhBoxItemQueryCondition();
            query.setBoxNo(boxNo);
            List<WhBoxItem> list = whBoxItemService.queryWhBoxItems(query, null);
            if (CollectionUtils.isNotEmpty(list)) {
                whBox.setPresentUser(currentUserId);
                whBoxService.updateWhBox(whBox);
                List<String> trackingNoList = list.stream()
                        .map(e -> {
                            if(Objects.equals(ItemOrderTypeEnum.EXPRESS_RECEIPT.intCode(),e.getOrderType())
                                    || Objects.equals(ItemOrderTypeEnum.WL_RECEIPT.intCode(),e.getOrderType())){
                                return e.getTrackingNum();
                            }else if(Objects.equals(ItemOrderTypeEnum.WH_CHECK_IN_EXCEPTION.intCode(),e.getOrderType())){
                                return e.getRelationNo();
                            }else{
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(toList());
                Map<String, Object> map = new HashMap<>();
                map.put("trackingNoList", trackingNoList);
                Map<String,String> flagMap = new HashMap<>();
                try {
                    WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
                    queryCondition.setBoxNo(boxNo);
                    List<WhPurchaseExpressRecord> whPurchaseExpressRecordList = whPurchaseExpressRecordService.queryWhPurchaseExpressRecords(queryCondition,null);
                    if (CollectionUtils.isNotEmpty(whPurchaseExpressRecordList)){
                        whPurchaseExpressRecordService.setWhPurchaseExpressRecordFlags(whPurchaseExpressRecordList);
                        Map<String,String> map1 = whPurchaseExpressRecordList.stream().filter(w ->
                                StringUtils.isNotBlank(w.getTrackingNumber()) && StringUtils.isNotBlank(w.getFlags()) && StringUtils.contains(w.getFlags(),"TJ"))
                                .collect(Collectors.toMap(WhPurchaseExpressRecord::getTrackingNumber, WhPurchaseExpressRecord::getFlags, (key1 , key2)-> key2 ));
                        for (String key:map1.keySet()){
                            for(String str:trackingNoList){
                                if (StringUtils.isNotBlank(str) && StringUtils.contains(key,str)){
                                    flagMap.put(str,map1.get(key));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                map.put("flagMap", flagMap);
                responseJson.setBody(map);
                responseJson.setStatus(StatusCode.SUCCESS);
            }
            else if (BoxStatus.ALREADY_USED.intCode().equals(whBox.getStatus())) {
                responseJson.setMessage("此周转筐正在使用,请重新输入");
                responseJson.setLocation("checkin/check_scan_purchase_view");
                return responseJson;
            }
            else {
                responseJson.setMessage("此周转筐没有绑定的快递单或异常入库单");
                responseJson.setLocation("checkin/check_scan_purchase_view");
                return responseJson;
            }

        }
        else {
            responseJson.setMessage("无此周转筐,请重新输入");
            responseJson.setLocation("checkin/check_scan_purchase_view");
            return responseJson;
        }

        responseJson.setLocation("checkin/check_scan_purchase_view");
        return responseJson;
    }

    /**
     * 快递单号解析
     *
     * @param pExpressId
     * @return
     */
    public String analysisExpressId(String pExpressId) {
        String expressId = pExpressId;
        String[] expressIdArr = null;
        Integer index = 0;
        if (StringUtils.isNotBlank(expressId) && expressId.contains("-")) {
            try {
                expressIdArr = StringUtils.split(expressId, "-");
                index = Integer.valueOf(expressIdArr[expressIdArr.length - 1]);
            }
            catch (Exception e) {
                return pExpressId;
            }
            expressId = expressId.substring(0, expressId.length() - 4);
        }
        WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
        queryCondition.setTrackingNumber(expressId);
        WhPurchaseExpressRecord record = whPurchaseExpressRecordService.queryWhPurchaseExpressRecord(queryCondition);
        if (record != null) {
            Integer quantity = record.getQuantity();
            // 子快递单号拼接"-"后面只能拼加三位，如：xxx-001
            if (index <= quantity && index > 0 && expressIdArr[expressIdArr.length - 1].length() == 3) {
                return expressId;
            }
        }
        return pExpressId;
    }

    /**
     * 快递扫描，扫描快递单号
     *
     * @param domain
     * @param expressId
     * @param itemExpressIdsStr
     * @param purchaseOrderNo
     * @return
     */
    public String checkInIsPoNoScan(WhCheckInDo domain, String expressId, String itemExpressIdsStr, Integer recordCount,
            String purchaseOrderNo, String receiveBoxNo) {
        if (StringUtils.isBlank(itemExpressIdsStr)) {
            // 第一次必须先扫主单号
            WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
            // 解析快递单号
            expressId = analysisExpressId(expressId);

            queryCondition.setTrackingNumber(expressId);
            WhPurchaseExpressRecord record = whPurchaseExpressRecordService
                    .queryWhPurchaseExpressRecord(queryCondition);

            if (StringUtils.isBlank(receiveBoxNo) && record != null && StringUtils.isNotBlank(record.getBoxNo())) {
                domain.setScanReceipted(false);
                domain.setMessageInfo("请先扫描周转框！");
                return "checkin/check_scan_purchase";
            }
            // 获取是否物流单校验
            if (StringUtils.isBlank(receiveBoxNo) && record != null && record.getSerialNumber() == null) {
                domain.setScanReceipted(false);
                domain.setMessageInfo("非物流单，请先绑定周转框扫描！");
                return "checkin/check_scan_purchase";
            }

            Integer quantity = 0;
            if (record != null) {
                quantity = record.getQuantity() == null ? 0 : record.getQuantity();
                if (StringUtils.isNotBlank(record.getPurchaseOrderNo()) && StringUtils.isNotBlank(record.getBoxNo()) && record.getSerialNumber() == null) {
                    // 符合当前扫描快递单所对应的采购单下的当天所有未拆分单号
                    queryCondition = new WhPurchaseExpressRecordQueryCondition();
                    queryCondition.setPurchaseOrderNo(record.getPurchaseOrderNo());
                    queryCondition.setCheckInScanFindRecord(true);
                    queryCondition.setTrackingNumber(record.getTrackingNumber());
                    queryCondition.setIsSplitStr("no");
                    queryCondition.setRejectRecord(false);
                    List<WhPurchaseExpressRecord> matchRecordList = whPurchaseExpressRecordService
                            .queryWhPurchaseExpressRecords(queryCondition, null);
                    recordCount = matchRecordList.size();
                    if (recordCount > 0) {
                        WhCheckInQueryCondition whCheckInQueryCondition = new WhCheckInQueryCondition();
                        List<String> trackingNumbers = matchRecordList.stream()
                                .map(WhPurchaseExpressRecord::getTrackingNumber).collect(toList());
                        whCheckInQueryCondition.setTrackingNumbers(trackingNumbers);
                        domain.setQuery(whCheckInQueryCondition);
                    }
                    purchaseOrderNo = record.getPurchaseOrderNo();
                }
            }
            else if (!expressId.contains("-")) {
                domain.setScanReceipted(false);
                domain.setMessageInfo("该采购单未签收，请先签收！");
                return "checkin/check_scan_purchase";
            }

            if (record != null && recordCount != null && (quantity > 1 || recordCount > 1)
                    && false == record.getCheckInScanStatus()) {
                String itemExpressIdsArr = expressId;
                // 存入子单号
                if (StringUtils.isNotBlank(purchaseOrderNo) && 1 == record.getQuantity()) {
                    domain.setExpressQuantity(recordCount);
                    domain.setMessageInfo("采购单关联多个快递单，请先扫描完关联的所有未拆分快递单！");
                }
                else {
                    domain.setMessageInfo("请扫描完所有的子快递单号！");
                    domain.setExpressQuantity(quantity);
                }
                domain.setPurchaseOrderNo(purchaseOrderNo);
                domain.setScanExpressIdsStr(itemExpressIdsArr);
                domain.setItemExpressIdsStr(itemExpressIdsArr);
                domain.setRecordCount(recordCount);
                return "checkin/check_scan_purchase";
            }
        }
        return expressId;
    }

    /**
     * 点数入库，兼容一个采购单对应多个快递单
     * 
     * @param domain
     * @param expressId
     * @param itemExpressIdsStr
     * @param expressQuantity
     * @param scanExpressIdsStr
     * @param purchaseOrderNo
     * @param recordCount
     * @return
     */
    @RequestMapping(value = "purchaseScanManyTrackingNos", method = { RequestMethod.GET })
    public String purchaseScanManyTrackingNos(@ModelAttribute("domain") WhCheckInDo domain,
            @RequestParam("expressId") String expressId, @RequestParam("itemExpressIdsStr") String itemExpressIdsStr,
            @RequestParam("expressQuantity") Integer expressQuantity,
            @RequestParam("scanExpressIdsStr") String scanExpressIdsStr,
            @RequestParam(value = "purchaseOrderNo", required = false) String purchaseOrderNo,
            @RequestParam("recordCount") Integer recordCount) {
        initFormData(domain);
        if (StringUtils.isBlank(expressId) || StringUtils.isBlank(itemExpressIdsStr) || recordCount == null
                || recordCount < 2) {
            domain.setMessageInfo("参数为空！");
            return "checkin/check_scan_purchase";

        }
        if (itemExpressIdsStr.contains(expressId)) {
            domain.setMessageInfo("采购单关联多个快递单，请先扫描完关联的所有未拆分快递单！");
            domain.setItemExpressIdsStr(itemExpressIdsStr);
            domain.setExpressQuantity(expressQuantity);
            domain.setRecordCount(recordCount);
            return "checkin/check_scan_purchase";
        }
        else {
            Integer quantity = 1;
            boolean existTrackingNo = false;
            if (StringUtils.isNotBlank(purchaseOrderNo)) {
                // 查询当前扫描快递单所对应的采购单下的所有未拆分单号的总数
                WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
                queryCondition.setPurchaseOrderNo(purchaseOrderNo);
                queryCondition.setCheckInScanFindRecord(true);
                queryCondition.setTrackingNumber(StringUtils.split(itemExpressIdsStr, ",")[0]);
                queryCondition.setIsSplitStr("no");
                queryCondition.setRejectRecord(false);
                List<WhPurchaseExpressRecord> purchaseOrderList = whPurchaseExpressRecordService
                        .queryWhPurchaseExpressRecords(queryCondition, null);
                if (CollectionUtils.isNotEmpty(purchaseOrderList)) {
                    for (WhPurchaseExpressRecord record : purchaseOrderList) {
                        if (expressId.equals(record.getTrackingNumber())) {
                            existTrackingNo = true;
                            quantity = record.getQuantity();
                            itemExpressIdsStr += "," + expressId;
                            break;
                        }
                    }
                }
            }

            String[] items = StringUtils.split(itemExpressIdsStr, ",");
            domain.setItemExpressIdsStr(itemExpressIdsStr);
            domain.setExpressQuantity(expressQuantity);
            Boolean flag = items.length < expressQuantity;

            if (flag) {
                domain.setMessageInfo("采购单关联多个快递单，请先扫描完关联的所有未拆分快递单！");
                if ((StringUtils.isNotBlank(purchaseOrderNo) || StringUtils.isNotBlank(scanExpressIdsStr))
                        && existTrackingNo && quantity == 1) {
                    domain.setPurchaseOrderNo(purchaseOrderNo);
                    scanExpressIdsStr = scanExpressIdsStr.contains(expressId) ? scanExpressIdsStr
                            : scanExpressIdsStr + "," + expressId;
                    domain.setScanExpressIdsStr(scanExpressIdsStr);
                }
                else if (StringUtils.isNotBlank(scanExpressIdsStr) && quantity > 1) {
                    domain.setMessageInfo("请扫描完所有的子快递单号！");
                    domain.setItemExpressIdsStr(expressId);
                    domain.setExpressQuantity(quantity);
                    domain.setRecordCount(recordCount);
                }
                return "checkin/check_scan_purchase";
            }
            else if (!expressId.contains("-") && quantity > 1) {
                domain.setMessageInfo("请扫描完所有的子快递单号！");
                domain.setItemExpressIdsStr(expressId);
                domain.setExpressQuantity(quantity);
                domain.setRecordCount(recordCount);
                return "checkin/check_scan_purchase";
            }
            else {
                expressId = items[items.length - 1];
            }
        }
        // 获取采购单接口
        List<PurchaseOrder> purchaseOrders = whPurchaseOrderService.getPurchaseOrders(expressId);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            domain.setMessageInfo("没有找到相关采购单！");
            return "checkin/check_scan_purchase";
        }
        return purchaseScanUpdatePurchaseRecord(domain, purchaseOrders, expressId, scanExpressIdsStr, true);
    }

    /**
     * 点数入库，兼容一个快递单对应多个子快递单
     * 
     * @param domain
     * @param expressId
     * @param itemExpressIdsStr
     * @param expressQuantity
     * @param scanExpressIdsStr
     * @param recordCount
     * @return
     */
    @RequestMapping(value = "purchaseScanSubTrackingNo", method = { RequestMethod.GET })
    public String purchaseScanSubTrackingNo(@ModelAttribute("domain") WhCheckInDo domain,
            @RequestParam("expressId") String expressId, @RequestParam("itemExpressIdsStr") String itemExpressIdsStr,
            @RequestParam("expressQuantity") Integer expressQuantity,
            @RequestParam("scanExpressIdsStr") String scanExpressIdsStr,
            @RequestParam("recordCount") Integer recordCount) {

        initFormData(domain);
        if (StringUtils.isBlank(expressId) || StringUtils.isBlank(itemExpressIdsStr) || expressQuantity == null
                || StringUtils.isBlank("请扫描完所有的子快递单号！")) {
            domain.setMessageInfo("参数为空！");
            return "checkin/check_scan_purchase";

        }
        domain.setItemExpressIdsStr(itemExpressIdsStr);
        domain.setExpressQuantity(expressQuantity);
        if (!expressId.contains("-")) {
            domain.setMessageInfo("请扫描完所有的子快递单号！");
            return "checkin/check_scan_purchase";
        }
        if (itemExpressIdsStr.contains(expressId)) {
            domain.setMessageInfo("请扫描完所有的子快递单号！");
            return "checkin/check_scan_purchase";
        }
        else {
            String[] items = StringUtils.split(itemExpressIdsStr, ",");
            if (expressId.startsWith(items[0])) {
                String[] expressIdArr = StringUtils.split(expressId, "-");
                Integer index = Integer.valueOf(expressIdArr[expressIdArr.length - 1]);
                if (index <= expressQuantity && index > 0 && expressIdArr[expressIdArr.length - 1].length() == 3) {
                    itemExpressIdsStr += "," + expressId;
                }
            }
            items = StringUtils.split(itemExpressIdsStr, ",");

            Boolean flag = items.length < expressQuantity + 1;

            if (flag) {
                domain.setItemExpressIdsStr(itemExpressIdsStr);
                domain.setMessageInfo("请扫描完所有的子快递单号！");
                return "checkin/check_scan_purchase";
            }
            else if (domain.getRecordCount() != null && domain.getRecordCount() > 1
                    && StringUtils.split(scanExpressIdsStr, ",").length < (recordCount - 1)) {
                domain.setItemExpressIdsStr(itemExpressIdsStr);
                domain.setMessageInfo("采购单关联多个快递单，请先扫描完关联的所有未拆分快递单！");
                String returnExpressId = StringUtils.split(expressId, "-")[0];
                scanExpressIdsStr = scanExpressIdsStr.contains(returnExpressId) ? scanExpressIdsStr
                        : scanExpressIdsStr + "," + returnExpressId;
                domain.setItemExpressIdsStr(scanExpressIdsStr);
                domain.setScanExpressIdsStr(scanExpressIdsStr);
                domain.setExpressQuantity(recordCount);
                domain.setRecordCount(recordCount);
                return "checkin/check_scan_purchase";
            }
            else {
                expressId = items[0];
            }
        }

        // 获取采购单接口
        List<PurchaseOrder> purchaseOrders = whPurchaseOrderService.getPurchaseOrders(expressId);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            domain.setMessageInfo("没有找到相关采购单！");
            return "checkin/check_scan_purchase";
        }
        return purchaseScanUpdatePurchaseRecord(domain, purchaseOrders, expressId, scanExpressIdsStr,true);
    }

    /**
     * 快递单扫描,兼容入库异常周转码
     *
     * @param domain
     * @param expressId
     * @return
     */
    @RequestMapping(value = "purchaseScanExceptionBoxNo", method = { RequestMethod.GET })
    public String purchaseScanExceptionBoxNo(@ModelAttribute("domain") WhCheckInDo domain,
                                             @RequestParam(value = "receiveBoxNo") String receiveBoxNo,
                                             @RequestParam("expressId") String expressId) {
        initFormData(domain);
        if (StringUtils.isBlank(expressId)) {
            domain.setMessageInfo("参数为空！");
            return "checkin/check_scan_purchase";
        }
        if(StringUtils.isBlank(receiveBoxNo)){
            domain.setMessageInfo("请先扫描收货周转框！");
            return "checkin/check_scan_purchase";
        }
        String boxNo = expressId;
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox != null && StringUtils.isNotBlank(whBox.getRelationNo())) {
            String relationNo = whBox.getRelationNo();
            WhCheckInException exception = whCheckInExceptionService.getWhCheckInException(Integer.valueOf(relationNo));
            if (exception == null) {
                domain.setMessageInfo("未找到入库单!");
                return "checkin/check_scan_purchase";
            }
            if(!Objects.equals(exception.getReceiveBoxNo(),receiveBoxNo)){
                domain.setMessageInfo("非当前周转筐的入库异常单!");
                return "checkin/check_scan_purchase";
            }
            // 异常单不为空，处理方式为入库或者建单入库，状态为待仓库处理的才能点数入库
            /*if (!ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.intCode().equals(exception.getHandleWay())
                    && !ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(exception.getHandleWay())
                    && !ExceptionHandleWay.UPDATE_DESCRIPTION.intCode().equals(exception.getHandleWay())
                    && !ExceptionHandleWay.REISSUE.intCode().equals(exception.getHandleWay())
                    && !ExceptionHandleWay.CHECK_IN.intCode().equals(exception.getHandleWay())
                    && !ExceptionHandleWay.UPDATE_IMG_SIZE.intCode().equals(exception.getHandleWay())
                    && !ExceptionHandleWay.CHANG_SIZE.intCode().equals(exception.getHandleWay())
                    && !ExceptionHandleWay.UPDATE_QC_REMARK.intCode().equals(exception.getHandleWay())) {
                domain.setMessageInfo("处理方式为入库、带样换图/加图、改尺寸、改描述、修改质检备注/标准尺寸或建单入库的异常单才能入库!");
                return "checkin/check_scan_purchase";
            }
            if (ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(exception.getHandleWay())) {
                if (ExceptionStatus.WAREHOUSE_PENDING.intCode().equals(exception.getStatus())
                    || ExceptionStatus.PRODUCT_PENDING.intCode().equals(exception.getStatus())
                        || ExceptionStatus.WAIT_PUSH_PRODUCT.intCode().equals(exception.getStatus())){
                    if (exception.getConfirmQuantity() == null || exception.getConfirmQuantity() <=1) {
                        domain.setMessageInfo("处理方式为带样换图/加图的待仓库处理、待开发处理、待推送开发入库异常单确认数量大于1的才能入库!");
                        return "checkin/check_scan_purchase";
                    }
                }else if (!(ExceptionStatus.getWarehousePendingCode().contains(exception.getStatus())
                        || ExceptionStatus.STOCK_IN_ING.intCode().equals(exception.getStatus()))) {
                    domain.setMessageInfo("入库中、待质控处理、待补发配件或待仓库处理的入库异常单才能入库!");
                    return "checkin/check_scan_purchase";
                }
            }else if (!(ExceptionStatus.getWarehousePendingCode().contains(exception.getStatus())
                    || ExceptionStatus.STOCK_IN_ING.intCode().equals(exception.getStatus()))) {
                domain.setMessageInfo("入库中、待质控处理、待补发配件或待仓库处理的入库异常单才能入库!");
                return "checkin/check_scan_purchase";
            }*/

            // 状态是否为 待入库，入库中
            if (!ExceptionStatus.WAIT_CHECK_IN.intCode().equals(exception.getStatus())
                    &&!ExceptionStatus.STOCK_IN_ING.intCode().equals(exception.getStatus())) {
                domain.setMessageInfo("入库中、待入库的入库异常单才能入库!");
                return "checkin/check_scan_purchase";
            }
           /* if (exception != null && StringUtils.isBlank(exception.getReceiveBoxNo())) {
                domain.setMessageInfo("该异常单没有绑定收货框，请先绑定收货框!");
                return "checkin/check_scan_purchase";
            }*/
            // 处理方式为新建采购单时(多货)
            if (ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.intCode().equals(exception.getHandleWay())
                    && StringUtils.isNotBlank(exception.getNewPurchaseOrderNo())) {
                expressId = exception.getNewPurchaseOrderNo();// 多货新建采购、补货的采购单
            }
            else if (StringUtils.isNotBlank(exception.getTrackingNumber())) {
                expressId = exception.getTrackingNumber();// 通过追踪号查采购数据
            }
            else if (StringUtils.isNotBlank(exception.getPurchaseOrderNo())) {
                expressId = exception.getPurchaseOrderNo();// 市场订单无追踪号使用
            }

            if (ExceptionType.FOLLOW_ORDER.getCode().equals(exception.getExceptionType())
                    && StringUtils.isNotBlank(exception.getNewPurchaseOrderNo())) {
                expressId = exception.getNewPurchaseOrderNo();
            }

            domain.setWhCheckInException(exception);
        }
        else {
            domain.setMessageInfo("未找到入库单!");
            return "checkin/check_scan_purchase";
        }
        // 获取采购单接口
        List<PurchaseOrder> purchaseOrders = whPurchaseOrderService.getPurchaseOrders(expressId);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            domain.setMessageInfo("没有找到相关采购单！");
            return "checkin/check_scan_purchase";
        }
        String result = purchaseScanUpdatePurchaseRecord(domain, purchaseOrders, expressId, null, true);
        WhCheckInQueryCondition queryCondition = domain.getQuery();
        if (Objects.nonNull(queryCondition) && CollectionUtils.isNotEmpty(queryCondition.getTrackingNumbers())) {
            queryCondition.setTrackingNumbers(Arrays.asList(whBox.getRelationNo()));
        }
        return result;
    }

    /**
     * 点数入库，兼容扫描市场/物流单（采购单号）
     * 
     * @param domain
     * @param expressId
     * @return
     */
    @RequestMapping(value = "purchaseScanPurchaseNo", method = { RequestMethod.GET })
    public String purchaseScanPurchaseNo(@ModelAttribute("domain") WhCheckInDo domain,
            @RequestParam("expressId") String expressId) {
        initFormData(domain);
        if (StringUtils.isBlank(expressId)) {
            domain.setMessageInfo("参数为空！");
            return "checkin/check_scan_purchase";
        }
        // 获取采购单接口
        List<PurchaseOrder> purchaseOrders = whPurchaseOrderService.getPurchaseOrders(expressId);

        if (CollectionUtils.isEmpty(purchaseOrders)) {
            domain.setMessageInfo("没有找到相关采购单！");
            return "checkin/check_scan_purchase";
        }
        // 是否物流订单
        boolean isTransportOrder = purchaseOrders.get(0).getIsTransportOrder() == null ? false
                : purchaseOrders.get(0).getIsTransportOrder();
        // 采购单类型
        String orderType = purchaseOrders.get(0).getPurchaseOrderType();

        if (!PurchaseOrderType.isDirectStorage(orderType)
                && !(StringUtils.equals(PurchaseOrderType.NCGHW.getCode(), orderType) && isTransportOrder)
                && !isTransportOrder) {
            domain.setMessageInfo("该采购单类型不属于错误、市场、产品开发样品单、赠送类型、FBA物流单; 不允许直接入库!");
            return "checkin/check_scan_purchase";
        }

        Date date = DateUtils.getDate(2018, 11, 21);// 截止到2018年11月20日晚24点：2018-11-21
        // 00:00:00
        Date purchaseDate = purchaseOrders.get(0).getPurchaseDate();
        if (purchaseDate.after(date)) {
            if (StringUtils.isNotBlank(orderType)) {
                if (!PurchaseOrderType.isDirectStorage(orderType) && !isTransportOrder) {
                    // 输入采购单号时，既不是市场订单、也不是物流订单
                    domain.setPurchaseOrders(null);
                    domain.setMessageInfo("非市场或物流订单，不允许直接入库！");
                    return "checkin/check_scan_purchase";
                }
            }
        }
        return purchaseScanUpdatePurchaseRecord(domain, purchaseOrders, expressId, null,true);
    }

    /**
     * 快递单扫描(生成入库单)
     */
    @RequestMapping(value = "purchaseScan", method = { RequestMethod.GET })
    public String purchaseScan(@ModelAttribute("domain") WhCheckInDo domain, HttpSession session,
            @RequestParam("expressId") String expressId, @RequestParam("bindExpressIds") String bindExpressIds,
            @RequestParam(value = "receiveBoxNo", required = false) String receiveBoxNo,
            @RequestParam(value = "itemExpressIdsStr", required = false) String itemExpressIdsStr,
            @RequestParam(value = "expressQuantity", required = false) Integer expressQuantity,
            @RequestParam(value = "recordCount", required = false) Integer recordCount,
            @RequestParam(value = "purchaseOrderNo", required = false) String purchaseOrderNo) {
        logger.info("checkin/scans/purchaseScan: expressId[" + expressId + "], itemExpressIdsStr[" + itemExpressIdsStr
                + "], expressQuantity[" + expressQuantity + "]");
        initFormData(domain);
        if (StringUtils.isNotBlank(receiveBoxNo) && StringUtils.isBlank(bindExpressIds)) {
            domain.setMessageInfo("该周转框没有绑定的快递单！");
            return "checkin/check_scan_purchase";
        }
        if (StringUtils.isNotBlank(expressId) && StringUtils.isNotBlank(receiveBoxNo)
                && !bindExpressIds.contains(expressId)) {
            domain.setMessageInfo(" 周转框与快递单不匹配,请确认！");
            return "checkin/check_scan_purchase";
        }

        String result = checkInIsPoNoScan(domain, expressId, itemExpressIdsStr, recordCount, purchaseOrderNo, receiveBoxNo);
        if (StringUtils.isNotBlank(result) && result.contains("/")) {
            return result;
        }
        else {
            expressId = result;
        }
        domain.setItemExpressIdsStr(null);

        // 校验是否存在点数完成的其它快递单
        boolean bool;
        try {
            bool = checkFinish(expressId,receiveBoxNo);
        } catch (Exception e) {
            domain.setMessageInfo("当前用户存在其他未点数完成的快递单【"+e.getMessage()+"】");
            return "checkin/check_scan_purchase";
        }

        // 获取采购单接口
        List<PurchaseOrder> purchaseOrders = whPurchaseOrderService.getPurchaseOrders(expressId);

        if (CollectionUtils.isEmpty(purchaseOrders)) {
            domain.setMessageInfo("没有找到相关采购单！");
            return "checkin/check_scan_purchase";
        }
        String purchaseRecord = purchaseScanUpdatePurchaseRecord(domain, purchaseOrders, expressId, null, bool);
        purchaseOrders= domain.getPurchaseOrders();
        for (PurchaseOrder order : purchaseOrders) {
            List<PurchaseOrderItem> items = order.getPurchaseOrderItems();
            items.sort((a, b) -> {
                return Integer.compare(b.getRemainingQuantity(), a.getRemainingQuantity());
            });
            order.setPurchaseOrderItems(items);
        }
        Map<PurchaseOrder, Integer> orderSums = new HashMap<>();
        for (PurchaseOrder order : purchaseOrders) {
            int sum = order.getPurchaseOrderItems()
                    .stream()
                    .mapToInt(PurchaseOrderItem::getRemainingQuantity)
                    .sum();
            orderSums.put(order, sum);
        }
        purchaseOrders.sort((a, b) ->
                Integer.compare(orderSums.get(b), orderSums.get(a))
        );
        domain.setPurchaseOrders(purchaseOrders);
        return purchaseRecord;
    }

    // 校验当前用户是否存在，扫描还未点数完成的项
    private boolean checkFinish(String expressId, String receiveBoxNo) throws Exception {
        // 获取当前用户扫描还未完成的记录
        List<WhPurchaseExpressRecord> expressRecordList = whPurchaseExpressRecordService.getNotFinishExpressRecord(DataContextHolder.getUserId());
        if (CollectionUtils.isEmpty(expressRecordList)) return true;
        List<String> trackList = expressRecordList.stream().map(e -> e.getTrackingNumber()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trackList)) return true;
        boolean contains = trackList.contains(expressId);
        trackList.remove(expressId);
        if (CollectionUtils.isNotEmpty(trackList)){
            Set<String> pNoSet = expressRecordList.stream().map(e -> e.getPurchaseOrderNo()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(pNoSet) && pNoSet.size() == 1 && contains){
                //【WMS-1586】优化一个采购单对应多个快递单入库时，
                // 如果没有点击完成点数页面被刷新或者关闭的情况下无法继续作业的问题
                return true;
            }
            // 解绑使用人 无需事物
            if(StringUtils.isNotBlank(receiveBoxNo)) {
                List<WhPurchaseExpressRecord> currRecord = expressRecordList.stream().filter(e ->
                        StringUtils.equalsIgnoreCase(e.getBoxNo(),receiveBoxNo)).collect(Collectors.toList());
                WhBox whBox = whBoxService.queryWhBoxByBoxNo(receiveBoxNo);
                if (whBox != null && (CollectionUtils.isEmpty(currRecord))){
                    WhBox updateWhBox = new WhBox();
                    updateWhBox.setId(whBox.getId());
                    updateWhBox.setPresentUser(null);
                    whBoxService.updateWhBox(updateWhBox);
                }
            }
            throw new Exception(StringUtils.join(trackList,","));
        }
        return false;
    }

    /**
     * 更新收货单扫描记录
     * 
     * @param domain
     * @param purchaseOrders
     * @param expressId
     * @param scanExpressIdsStr
     * @return
     */
    public String purchaseScanUpdatePurchaseRecord(WhCheckInDo domain, List<PurchaseOrder> purchaseOrders,
            String expressId, String scanExpressIdsStr, boolean isFinish) {
        if (purchaseOrders.stream().anyMatch(
                p -> StringUtils.equalsIgnoreCase(PurchaseOrderType.NCGHC.getCode(), p.getPurchaseOrderType()))) {
            domain.setMessageInfo("耗材采购单，请到耗材入库界面入库！");
            return "checkin/check_scan_purchase";
        }
        // 南宁仓入库增加限制不允许入库深圳仓的采购单 WMS-5705
        //深圳仓不允许入库南宁仓采购单 WMS-6160
        Map<String, WhPurchaseOrder> existPurchaseOrderMap = new HashMap<>();
        if (purchaseOrders.stream().anyMatch(p -> StringUtils.isNotBlank(p.getPurchaseOrderNo()))) {
            String purchaseOrderNoStr = purchaseOrders.stream().map(PurchaseOrder::getPurchaseOrderNo)
                    .collect(Collectors.joining(","));
            WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
            query.setPurchaseOrderNoStr(purchaseOrderNoStr);
            List<WhPurchaseOrder> records = whPurchaseOrderService.queryWhPurchaseOrders(query, null);
            if (CollectionUtils.isEmpty(records)) {
                String nameByCode = LocationWarehouseType.getNameByCode(CacheUtils.getLocalWarehouseIdStr());
                nameByCode = StringUtils.equals(nameByCode, LocationWarehouseType.OLD.getName()) ? "深圳仓" : nameByCode;
                domain.setMessageInfo(nameByCode + "不存在该包裹" + Optional.ofNullable(purchaseOrders.get(0).getExpressId())
                        .orElse(purchaseOrders.get(0).getPurchaseOrderNo()) + "禁止在" + nameByCode + "入库！");
                return "checkin/check_scan_purchase";
            }
            existPurchaseOrderMap = records.stream()
                    .collect(Collectors.toMap(WhPurchaseOrder::getPurchaseOrderNo, p -> p));
        }

        // 修改签收记录
        Integer warehouseId = null;
        domain.setItemExpressIdsStr(null);

        domain.setPurchaseOrders(purchaseOrders);

        if (!StringUtils.startsWith(expressId, "CG") && !StringUtils.startsWith(expressId, "HWCG")
                && !StringUtils.startsWith(expressId, "NCG")) {
            whPurchaseExpressRecordService.updatePurchaseOrderNo(purchaseOrders, expressId);
        }

        boolean checkTime = DirectTimeSettingUtils.checkTime();
        List<String> orderNoList = purchaseOrders.stream().map(PurchaseOrder::getPurchaseOrderNo).collect(toList());
        Map<String, List<PurchaseApvOutStockMatch>> orderNoMap = Collections.EMPTY_MAP;
        if(checkTime){
            PurchaseApvOutStockMatchQueryCondition stockMatchQueryCondition =new PurchaseApvOutStockMatchQueryCondition();
            stockMatchQueryCondition.setPurchaseOrderNoList(orderNoList);
            stockMatchQueryCondition.setStatus(OutStockMatchStatus.MATCHED.intCode());
            List<PurchaseApvOutStockMatch> purchaseApvOutStockMatches = purchaseApvOutStockMatchService.queryPurchaseApvOutStockMatchs(stockMatchQueryCondition, null);
            purchaseApvOutStockMatches=outStockMatchHandelService.matchOrderLogisticsInfoByDdNo(purchaseApvOutStockMatches);
            orderNoMap = purchaseApvOutStockMatches.stream().collect(Collectors.groupingBy(m->m.getPurchaseOrderNo()+m.getSku(),Collectors.toList()));
        }

        List<String> skus = new ArrayList<>();
        Iterator<PurchaseOrder> purchaseOrderIter = purchaseOrders.iterator();
        while (purchaseOrderIter.hasNext()) {
            PurchaseOrder purchaseOrder = purchaseOrderIter.next();
            if (LocationWarehouseType.NANNING.intCode().equals(CacheUtils.getLocalWarehouseId())
                    && !existPurchaseOrderMap.containsKey(purchaseOrder.getPurchaseOrderNo())) {
                purchaseOrderIter.remove();
                continue;
            }
            // 记录当前扫描的快递单号
            if (!expressId.startsWith("CG") && !expressId.startsWith("HWCG") && !expressId.startsWith("NCG")) {
                purchaseOrder.setTrackingNumber(expressId);
            }
            if (warehouseId != null) {
                purchaseOrder.setWarehouseId(warehouseId.toString());
            }
            boolean waitPurchase = StringUtils.equalsIgnoreCase(purchaseOrder.getPurchaseStatus(),
                    "Wait_Accountant_Confirm")
                    || StringUtils.equalsIgnoreCase(purchaseOrder.getPurchaseStatus(), "Cashier_Pay");
            // 只处理待收货，待上架，部分入库的
            if (!waitPurchase) {
                purchaseOrderIter.remove();
                continue;
            }

            // 特急情况下，标记sku是否s包含shopify
            try {
                if (StringUtils.isNotBlank(purchaseOrder.getFlagsName())
                        && purchaseOrder.getFlagsName().indexOf("特急")>0
                        && CollectionUtils.isNotEmpty(purchaseOrder.getPurchaseOrderItems())){
                    boolean bool = purchaseOrder.getPurchaseOrderItems().stream().anyMatch(item -> StringUtils.isNotBlank(item.getPurchaseSkuSpecialGoods())
                            && StringUtils.contains(item.getPurchaseSkuSpecialGoods(), WhSkuSpecialGoods.SPECIAL_TYPE_CODE));
                    if (bool && !domain.isHasShopifyTJSku()){
                        domain.setHasShopifyTJSku(bool);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            // 生成入库单只显示非完全入库的
            List<PurchaseOrderItem> items = new ArrayList<PurchaseOrderItem>(purchaseOrder.getPurchaseOrderItems());

            //获取不需要质检的SKU列表
            ExemptionQcConfiguration exemptionQcConfiguration = null;
            ExemptionQcConfigurationQueryCondition queryCondition = new ExemptionQcConfigurationQueryCondition();
            queryCondition.setStatus(1);
            List<ExemptionQcConfiguration> exemptionQcConfigurations = qcConfigurationService.queryExemptionQcConfigurations(queryCondition,null);
            if (CollectionUtils.isNotEmpty(exemptionQcConfigurations)) {
                exemptionQcConfiguration = exemptionQcConfigurations.get(0);
            }
            domain.setExemptionQcConfiguration(exemptionQcConfiguration);
            Map<String,Boolean> skuMap=whCheckInService.getExemptedSku(exemptionQcConfiguration, items);

            purchaseOrder.getPurchaseOrderItems().clear();
            for (PurchaseOrderItem item : items) {
                if(checkTime) {
                    List<PurchaseApvOutStockMatch> outStockMatchList = orderNoMap.get(purchaseOrder.getPurchaseOrderNo() + item.getSku());
                    if (CollectionUtils.isNotEmpty(outStockMatchList)) {
                        item.setPurchaseApvOutStockMatchList(outStockMatchList);
                    }
                }
                if (MapUtils.isNotEmpty(skuMap) && skuMap.get(item.getSku()+item.getVendorName())!=null && skuMap.get(item.getSku()+item.getVendorName())) {
                    item.setIsFreeCheck(true);
                }

                WhCheckInQueryCondition query = new WhCheckInQueryCondition();
                query.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
                query.setSku(item.getSku());
                List<WhCheckIn> whCheckIns = whCheckInService.queryWhCheckIns(query, null);
                if (CollectionUtils.isNotEmpty(whCheckIns)) {
                    Integer count = 0;
                    for (WhCheckIn whCheckIn : whCheckIns) {

                        /*
                         * UNCONFIRM("未确认入库", "1"), WAITING_QC("等待QC", "3"), QC_NG_PENDING("NG采购待处理",
                         * "5"), QC_NG("QC不良品", "7"), WAITING_UP("等待上架", "9"), UPING("上架中",
                         * "11"),UPERROR("上架失败", "12"),
                         */

                        // 如果采购单分批到货，需要计算之前到货但还未上架的数量
                        Integer status = whCheckIn.getStatus();
                        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
                        if (CheckInStatus.WAITING_QC.intCode().equals(status)) {
                            if (whCheckInItem != null) {
                                count += whCheckInItem.getQuantity() == null ? 0 : whCheckInItem.getQuantity();
                            }
                        }
                        if (CheckInStatus.WAITING_UP.intCode().equals(status) || CheckInStatus.UPING.intCode().equals(status)
                                || CheckInStatus.UPERROR.intCode().equals(status)) {
                            if (whCheckInItem != null) {
                                count += whCheckInItem.getQcQuantity() == null ? whCheckInItem.getQuantity()
                                        : whCheckInItem.getQcQuantity();
                            }
                        }
                    }
                    item.setUndefinedInQuantity(count);
                }

                // 采购数据量
                Integer quantity = item.getQuantity() == null ? 0 : item.getQuantity();

                // 已入库
                Integer inedQuantity = item.getInedQuantity() == null ? 0 : item.getInedQuantity();

                // 未确认
                Integer undefinedInQuantity = item.getUndefinedInQuantity() == null ? 0 : item.getUndefinedInQuantity();

                // 取消在途
                Integer revokedQuantity = item.getRevokedQuantity() == null ? 0 : item.getRevokedQuantity();

                if (inedQuantity == 0) {
                    item.setInedQuantity(inedQuantity);
                }
                if (undefinedInQuantity == 0) {
                    item.setUndefinedInQuantity(undefinedInQuantity);
                }
                if (revokedQuantity == 0) {
                    item.setRevokedQuantity(revokedQuantity);
                }

                logger.info("purchaseOrder: purchaseOrderNo[" + purchaseOrder.getPurchaseOrderNo() + "],sku["
                        + item.getSku() + "], quantity[" + quantity + "], inedQuantity[" + inedQuantity
                        + "], undefinedInQuantity[" + undefinedInQuantity + "], revokedQuantity[" + revokedQuantity
                        + "]");
                // 完成入库不用显示 或者显示未点数完成项
                if (inedQuantity + undefinedInQuantity + revokedQuantity < quantity || !isFinish) {
                    purchaseOrder.getPurchaseOrderItems().add(item);
                    if (!skus.contains(item.getSku())) {
                        skus.add(item.getSku());
                    }
                }
            }

            // 已入库的不用出现
            if (CollectionUtils.isEmpty(purchaseOrder.getPurchaseOrderItems())) {
                purchaseOrderIter.remove();
            }
        }
        if (CollectionUtils.isEmpty(domain.getPurchaseOrders())) {
            // 该采购单已入库
            domain.setHasCheckIn(true);
        }
        else {
            Map<String, WhSku> skuMap = new HashMap<>();
            // 查找是否使用原包装，产品标识信息
            Map<String, WhSkuWithPmsInfo> whSkuWithPmsInfoMap = new HashMap<>();
            Map<String, HistoryCheckInExceptionCount> historyCountMap = new HashMap<String, HistoryCheckInExceptionCount>();
            //获取封口袋或背心袋耗材
            List<WhPackagingMaterialManagement> materialManagements = null;

            if (CollectionUtils.isNotEmpty(skus)) {
                WhSkuQueryCondition query = new WhSkuQueryCondition();
                query.setSkus(skus);
                List<WhSku> whSkus = whSkuService.queryWhSkus(query, null);
                //当SKU产品规格值不为空时需要根据产品规格匹配对应的封口袋或背心袋
                boolean anyMatchNotNull = Optional.ofNullable(whSkus).orElse(new ArrayList<>()).stream()
                        .anyMatch(s -> s.getLength() != null && s.getWidth() != null && s.getHeight() != null);
                if (anyMatchNotNull)
                    materialManagements = whPackagingMaterialManagementService.queryPackagingMaterialForCheckIn();
                //获取保质期SKU集合
                List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(skus);

                HistoryCheckInExceptionCountQueryCondition historyCountQuery = new HistoryCheckInExceptionCountQueryCondition();
                historyCountQuery.setSkus(skus);
                List<HistoryCheckInExceptionCount> historyCheckInExceptionCountList = historyCheckInExceptionCountService
                        .queryHistoryCheckInExceptionCounts(historyCountQuery, null);

                if (CollectionUtils.isNotEmpty(historyCheckInExceptionCountList)) {
                    for (HistoryCheckInExceptionCount count : historyCheckInExceptionCountList) {
                        historyCountMap.put(count.getSku(), count);
                    }
                }

                List<WhSkuWithPmsInfo> whSkuWithPmsInfoList = ApvPackUtils.getPmsSkuInfoList(skus);
                if (CollectionUtils.isNotEmpty(whSkuWithPmsInfoList)) {
                    for (WhSkuWithPmsInfo whSkuWithPmsInfo : whSkuWithPmsInfoList) {
                        whSkuWithPmsInfoMap.put(whSkuWithPmsInfo.getSku(), whSkuWithPmsInfo);
                    }
                }

                List<SkuAttributeTag> skuAttributeTags = skuAttributeTagService.queryAllSkuAttributeTags();

                if (CollectionUtils.isNotEmpty(whSkus)) {
                    for (WhSku whSku : whSkus) {
                        whSku.setFloorLocation(ProcessType.getIntCode(whSku.getSkuAlias(), whSku.getFloorLocation()));
                        WhSkuExtendQueryCondition queryCondition=new WhSkuExtendQueryCondition();
                        queryCondition.setSku(whSku.getSku());
                        List<WhSkuExtend> whSkuExtends = whSkuExtendService.queryWhSkuExtends(queryCondition, null);
                        if (CollectionUtils.isNotEmpty(whSkuExtends)) {
                            WhSkuExtend skuExtend = whSkuExtends.get(0);
                            this.handelSkuExtendData(skuExtend, skuAttributeTags);
                            whSku.setWhSkuExtend(skuExtend);
                        }
                        // 查询是否有FZ标签
                        WhSkuSpecialGoodsQueryCondition specCondition = new WhSkuSpecialGoodsQueryCondition();
                        specCondition.setSpecialSku(whSku.getSku());
                        specCondition.setSpecialTypeList(Arrays.asList(SpecialTypeEnums.FZ.intCode(),SpecialTypeEnums.EMPHASIS_QC.intCode()));
                        List<WhSkuSpecialGoods> whSkuSpecialGoods = whSkuSpecialGoodsService.queryWhSkuSpecialGoodss(specCondition, null);
                        List<WhSkuSpecialGoods> skuSpecialGoods = Optional.ofNullable(whSkuSpecialGoods).orElse(new ArrayList<>()).stream().filter(a -> SpecialTypeEnums.FZ.intCode().equals(a.getSpecialType())).collect(toList());
                        if (CollectionUtils.isNotEmpty(skuSpecialGoods)) {
                            whSku.setSpecialType(skuSpecialGoods.get(0).getSpecialType());
                        }
                        whSku.setExpSku(CollectionUtils.isNotEmpty(expSkuList) && expSkuList.contains(whSku.getSku()));

                        List<WhSkuSpecialGoods> specialTagList = Optional.ofNullable(whSkuSpecialGoods).orElse(new ArrayList<>()).stream().filter(a -> SpecialTypeEnums.EMPHASIS_QC.intCode().equals(a.getSpecialType())).collect(toList());
                        //是否重点质检服装
                        if (CollectionUtils.isNotEmpty(specialTagList)) {
                            whSku.setSpecialTag(SpecialTypeEnums.getNameByCode(specialTagList.get(0).getSpecialType()+"") );
                        }
                        skuMap.put(whSku.getSku(), whSku);
                    }
                }
            }

            // 保存可入库数据
            for (PurchaseOrder purchaseOrder : domain.getPurchaseOrders()) {
                List<Integer> specialTypeList = new ArrayList<>();
                for (PurchaseOrderItem item : purchaseOrder.getPurchaseOrderItems()) {
                    // 备货SKU，移除匹配缺货信息，直接入库
                    /*if (CollectionUtils.isNotEmpty(item.getPurchaseApvOutStockMatchList())
                            && skuMap.get(item.getSku()) != null && (skuMap.get(item.getSku()).getNoStockUp() == null
                            || skuMap.get(item.getSku()).getNoStockUp() == 0)) {
                        item.setPurchaseApvOutStockMatchList(null);
                    }*/
                    if (whSkuWithPmsInfoMap != null && whSkuWithPmsInfoMap.get(item.getSku()) != null) {
                        item.setUseOlderPackage(whSkuWithPmsInfoMap.get(item.getSku()).getIsOriginalPackage());// 设置是否带原包装发货
                        item.setProductFlag(whSkuWithPmsInfoMap.get(item.getSku()).getSkuLabelName());// 设置产品标识
                        item.setSize(whSkuWithPmsInfoMap.get(item.getSku()).getSize());// 设置标准尺寸
                        item.setPackingMemo(whSkuWithPmsInfoMap.get(item.getSku()).getPackingMemo()); // 包装备注
                        item.setPackageAttribute(whSkuWithPmsInfoMap.get(item.getSku()).getPackageAttribute());  // 包材属性
                    }
                    item.setSkuQcCategoryDesc(GetSkuQcCategoryDescUtil.getSkuQcCategoryDesc(item.getSku()));// 产品系统质检备注
                    item.setWhSku(skuMap.get(item.getSku()));
                    if (item.getWhSku() != null && item.getWhSku().getSpecialType() != null){
                        specialTypeList.add(item.getWhSku().getSpecialType());
                    }
                    item.setHistoryCheckInExceptionCount(historyCountMap.get(item.getSku()));

                    if (CollectionUtils.isNotEmpty(materialManagements))
                        matchMaterial(item, materialManagements);
                    

                    String key = PurchaseOrder.STRINGREDIS_KEY_PREFIX + purchaseOrder.getPurchaseOrderNo() + "_"
                            + item.getSku();
                    Integer couldCheckInQuantity = item.getQuantity() - item.getRevokedQuantity();
                    StringRedisUtils.set(key, couldCheckInQuantity.toString(), 7 * 24 * 60 * 60L);
                    logger.info("set Redis: " + key + ", value: " + couldCheckInQuantity);
                }
                if (CollectionUtils.isNotEmpty(specialTypeList) && specialTypeList.contains(SpecialTypeEnums.VALUABLES.intCode())) {
                    purchaseOrder.setSpecialType(SpecialTypeEnums.VALUABLES.intCode());
                }
            }
        }

        // 扫描成功关联采购单后记录扫描人，扫描时间，设置扫描状态为"1：已扫描"
        scanExpressIdsStr = scanExpressIdsStr == null ? expressId : scanExpressIdsStr + "," + expressId;
        String[] expressIdArray = StringUtils.split(scanExpressIdsStr, ",");
        Arrays.asList(expressIdArray).stream().forEach(updateExpressId -> {
            WhPurchaseExpressRecord whPurchaseExpressRecord = new WhPurchaseExpressRecord();
            whPurchaseExpressRecord.setTrackingNumber(updateExpressId);
            whPurchaseExpressRecord.setCheckInScanner(DataContextHolder.getUserId());
            whPurchaseExpressRecord.setCheckInScanStatus(true);
            whPurchaseExpressRecord.setCheckInScanTime(new Timestamp(System.currentTimeMillis()));
            // 状态标记成已扫描
            whPurchaseExpressRecord.setStatus(PurchaseExpressRecordStatus.SCANNER.intCode());
            whPurchaseExpressRecordService.updatePurchaseExpressRecordByTrackingNumber(whPurchaseExpressRecord);
        });
        WhCheckInQueryCondition queryCondition = domain.getQuery() == null ? new WhCheckInQueryCondition()
                : domain.getQuery();
        queryCondition.setTrackingNumbers(CommonUtils.splitList(scanExpressIdsStr, ","));
        domain.setQuery(queryCondition);

        // SKU描述置空，防止生成异常时报错
        if (CollectionUtils.isNotEmpty(domain.getPurchaseOrders())){
            domain.getPurchaseOrders().forEach(order -> {
                if (CollectionUtils.isNotEmpty(order.getPurchaseOrderItems())){
                    order.getPurchaseOrderItems().forEach(item -> {
                        if (item.getWhSku() != null) {
                            item.getWhSku().setDescription(null);
                            item.getWhSku().setTitle(null);
                        }
                    });
                }
            });
            buildSkuClothingData(domain);
        }
        domain.setPurchaseOrderArrayStr(JSON.toJSONString(domain.getPurchaseOrders()));
        return "checkin/check_scan_purchase";
    }


    private void handelSkuExtendData(WhSkuExtend skuExtend, List<SkuAttributeTag> attributeTags) {
        if (CollectionUtils.isEmpty(attributeTags) || skuExtend == null || StringUtils.isEmpty(skuExtend.getTags()))
            return;
        Map<String, String> tagMap = attributeTags.stream().collect(
                Collectors.toMap(SkuAttributeTag::getValue, SkuAttributeTag::getPromptPageStr, (t1, t2) -> t1));
        List<String> tagList = new ArrayList<>();
        skuExtend.getTagList().forEach(tag -> {
            if (tagMap.get(tag) != null && tagMap.get(tag).contains(PromptPageEnum.COUNT_CHECK_IN.getName())) {
                tagList.add(tag);
            }
        });
        skuExtend.setTags(StringUtils.join(tagList, ","));
    }

    private void matchMaterial(PurchaseOrderItem item, List<WhPackagingMaterialManagement> materialManagements) {
        if (item == null || item.getWhSku() == null || item.getWhSku().getWidth() == null
                || item.getWhSku().getLength() == null || item.getWhSku().getHeight() == null
                || CollectionUtils.isEmpty(materialManagements))
            return;
        // 1、产品规格的长＜耗材的长-1
        // 2、产品规格的宽*高＜4*耗材的宽
        // 在满足条件的耗材中选择数据最接近的耗材，当有多个耗材尺寸一致时，推荐多个
        Double pLength = item.getWhSku().getLength();
        Double pWidth = item.getWhSku().getWidth();
        Double pHeight = item.getWhSku().getHeight();

        Map<Double, List<WhPackagingMaterialManagement>> vMap = materialManagements.stream()
                .filter(m -> (pLength < (m.getLength() == null ? 0 : m.getLength())/10 - 1
                        && pWidth * pHeight < 4 * (m.getWidth() == null ? 0 : m.getWidth())/10))
                .collect(Collectors.groupingBy(
                        m -> (m.getLength() == null ? 0 : m.getLength()) * (m.getWidth() == null ? 0 : m.getWidth())));
        if (MapUtils.isEmpty(vMap))
            return;
        Double minKey = vMap.keySet().stream().sorted(Double::compareTo).collect(Collectors.toList()).get(0);
        item.setMatchMaterials(vMap.get(minKey));
    }

    public void buildSkuClothingData(WhCheckInDo domain){
        if (CollectionUtils.isEmpty(domain.getPurchaseOrders())){
            return;
        }
        List<String> skuList = new ArrayList<>();
        domain.getPurchaseOrders().forEach(order -> {
            if (CollectionUtils.isNotEmpty(order.getPurchaseOrderItems())){
                order.getPurchaseOrderItems().forEach(item -> {
                    skuList.add(item.getSku());
                });
            }
        });
        if (CollectionUtils.isNotEmpty(skuList)) {
            WhSkuExpandAttrQueryCondition query = new WhSkuExpandAttrQueryCondition();
            query.setSkuList(skuList);
            query.setCodeList(ClothingItemAttr.getCodeList());
            List<WhSkuExpandAttr> whSkuExpandAttrs = whSkuExpandAttrService.queryWhSkuExpandAttrs(query, null);
            if (CollectionUtils.isNotEmpty(whSkuExpandAttrs)){
                Map<String,List<WhSkuExpandAttr>> mapList = new HashMap<>();
                for (WhSkuExpandAttr entry : whSkuExpandAttrs) {
                    List<WhSkuExpandAttr> list = mapList.get(entry.getSku());
                    if (list == null){
                        list = new ArrayList<>();
                    }
                    list.add(entry);
                    mapList.put(entry.getSku(), list);
                }
                Map<String, String> skuToClothingSizeMap = new HashMap<>();
                for (Map.Entry<String, List<WhSkuExpandAttr>> entry : mapList.entrySet()) {
                    String sku = entry.getKey();
                    List<WhSkuExpandAttr> list =entry.getValue();
                    Map<String, String> map = list.stream().collect(Collectors.toMap(WhSkuExpandAttr::getAttrCode, item->item.getAttrValue()));
                    skuToClothingSizeMap.put(sku, JSON.toJSONString(map));
                }
                domain.setSkuToClothingSizeMap(JSON.toJSONString(skuToClothingSizeMap));
                domain.getPurchaseOrders().forEach(order -> {
                    if (CollectionUtils.isNotEmpty(order.getPurchaseOrderItems())){
                        order.getPurchaseOrderItems().forEach(item -> {
                            if (mapList.containsKey(item.getSku()) && item.getWhSku() != null){
                                item.getWhSku().setWhSkuExpandAttr(mapList.get(item.getSku()));
                                /*boolean hasClothingButAllNull = true;
                                for (WhSkuExpandAttr attr : item.getWhSku().getWhSkuExpandAttr()) {
                                    if (StringUtils.isNotBlank(attr.getAttrValue())){
                                        hasClothingButAllNull = false;
                                        break;
                                    }
                                }
                                item.setHasClothingButAllNull(hasClothingButAllNull)*/;
                            }else {
                                String arr = StringRedisUtils.get(RedisConstant.CLOTHING_SKU_CATEGORY_ID_LIST);
                                if (item.getWhSku() != null && item.getWhSku().getCategoryId() != null && StringUtils.isNotBlank(arr)){
                                    List<String> idList = CommonUtils.splitList(arr, ",");
                                    if (idList.contains(item.getWhSku().getCategoryId().toString())){
                                        item.setHasClothingButAllNull(true);
                                    }
                                }
                            }
                        });
                    }
                });
            }else {
                domain.getPurchaseOrders().forEach(order -> {
                    if (CollectionUtils.isNotEmpty(order.getPurchaseOrderItems())){
                        order.getPurchaseOrderItems().forEach(item -> {
                            String arr = StringRedisUtils.get(RedisConstant.CLOTHING_SKU_CATEGORY_ID_LIST);
                            if (item.getWhSku() != null && item.getWhSku().getCategoryId() != null && StringUtils.isNotBlank(arr)){
                                List<String> idList = CommonUtils.splitList(arr, ",");
                                if (idList.contains(item.getWhSku().getCategoryId().toString())){
                                    item.setHasClothingButAllNull(true);
                                }
                            }
                        });
                    }
                });
            }
        }
    }

    /**
     * 生成入库单
     */
    @RequestMapping(value = "checkIn", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createCheckIn(@ModelAttribute("domain") WhCheckInDo domain,
            @RequestParam(value = "receiveBoxNo", required = false) String receiveBoxNo,
            @RequestParam(value = "trackingNumbers", required = false) String trackingNumbers,
            @RequestParam(value = "arrayTrackingNo", required = false) String arrayTrackingNo,
            @RequestParam(value = "purchaseQuantity", required = false) Integer purchaseQuantity,
            HttpServletRequest request, HttpSession session) {
        logger.info("checkin/scans/checkIn");

        ResponseJson response = new ResponseJson();
        WhCheckIn whCheckIn = domain.getWhCheckIn();
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        if (whCheckIn == null || whCheckInItem == null || StringUtils.isBlank(whCheckInItem.getSku())) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("参数为空！");
            return response;
        }

        if (whCheckIn != null && whCheckIn.isRestrictedStorage() && whCheckInItem.getQuantity() != null
                && whCheckInItem.getQuantity() > 0) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("SKU即将过保质期，请提交异常单！");
            return response;
        }
        //获取保质期SKU集合
        List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(Arrays.asList(whCheckInItem.getSku()));
        if (CollectionUtils.isEmpty(whCheckIn.getWhCheckInExcessList()) && (CollectionUtils.isNotEmpty(expSkuList) && (whCheckIn == null || whCheckIn.getDays() == null
                || StringUtils.isBlank(whCheckIn.getProDate()) || StringUtils.isBlank(whCheckIn.getExpDate())))) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("保质期数据为空，请填写完再提交！");
            return response;
        }

        // 判断SKU是否合并
        Map<String, String> skuMap = whSkuService.queryWhSkuDiscard(Arrays.asList(whCheckInItem.getSku()));
        List<String> discardSku = whSkuService.queryDiscardSku(Arrays.asList(whCheckInItem.getSku()));
        if (skuMap.get(whCheckInItem.getSku()) != null) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("该SKU已被合并到新SKU: " + skuMap.get(whCheckInItem.getSku()) + ", 请创建异常单处理!");
            return response;
        }
        if (CollectionUtils.isNotEmpty(discardSku)){
            response.setStatus(StatusCode.FAIL);
            response.setMessage("该SKU已废弃!");
            return response;
        }
        // 判断sku仓库和本仓
        WhWarehouse whWarehouse = whWarehouseService.queryOriginalWhWarehouse(true);
        if (whWarehouse != null) {
            WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
            whSkuQueryCondition.setSku(whCheckInItem.getSku());
            WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
            if (whSku == null) {
                response.setMessage(whCheckInItem.getSku() + "sku不存在，请确认后再入库！");
                return response;
            }
            if (whSku != null && whSku.getWarehouseId() == null) {
                response.setMessage(whSku.getSku() + "仓库属性为空！");
                return response;
            }
            if (whSku != null && whSku.getWarehouseId() != null
                    && !whSku.getWarehouseId().equals(whWarehouse.getId())) {
                response.setMessage(whSku.getSku() + "不是本仓SKU，请确认！");
                return response;
            }

            whCheckIn.setWarehouseId(whWarehouse.getId());
            // 标记贵重标签
            if (whSku.getSpecialType() != null && whSku.getSpecialType().equals(SpecialTypeEnums.VALUABLES.intCode()))
                whCheckInItem.setFirstOrderType(
                        StringUtils.isEmpty(whCheckInItem.getFirstOrderType()) ? SpecialTypeEnums.VALUABLES.getCode()
                                : whCheckInItem.getFirstOrderType() + "," + SpecialTypeEnums.VALUABLES.getCode());
            
        }
        // 有移库任务不让创建
        if (Integer.valueOf(CacheUtils.getLocalWarehouseId()).equals(WarehousePropertyEnum.MJ.intCode())) {
            whCheckInItem.getSku();
            String mes = chekMoveLocation(whCheckInItem.getSku());
            if (StringUtils.isNotBlank(mes)) {
                response.setMessage(mes);
                return response;
            }
        }
        //是否不上架直发
        boolean isShelfStraightHair=false;
        //不贴标
        boolean noLabel = false;
        //不贴标入库
        if (StringUtils.isNotBlank(whCheckIn.getFlags())
                && StringUtils.contains(whCheckIn.getFlags(), PurchaseOrderFlags.NO_LABEL.getEnCode())) {
            whCheckInItem.addTag(CheckInFlags.NO_LABEL.getCode());
            noLabel = true;
        }
        //是否免检
        Boolean isFreeCheck = Optional.ofNullable(whCheckInItem.getIsFreeCheck()).orElse(false);
        // 免检判断需要增加入库数量配置判断
        if (isFreeCheck){
            ExemptionQcConfiguration exemptionQcConfiguration = null;
            ExemptionQcConfigurationQueryCondition queryCondition=new ExemptionQcConfigurationQueryCondition();
            queryCondition.setStatus(1);
            List<ExemptionQcConfiguration> exemptionQcConfigurations = qcConfigurationService.queryExemptionQcConfigurations(queryCondition,null);
            if (CollectionUtils.isNotEmpty(exemptionQcConfigurations)) {
                exemptionQcConfiguration = exemptionQcConfigurations.get(0);
            }
            Integer checkInSkuNum = Objects.isNull(exemptionQcConfiguration) ? null : exemptionQcConfiguration.getCheckInSkuNum();
            if (Objects.nonNull(checkInSkuNum) && checkInSkuNum > 0
                    && (whCheckInItem.getQuantity() == null || whCheckInItem.getQuantity() <= 0 || whCheckInItem.getQuantity() > checkInSkuNum)) {
                isFreeCheck = false;
            }
        }
        //避免多人同时提交一个采购单
        String key = PrintSkuQrCodeRedisLock.CHECK_IN_PURCHASE_NO_SKU + whCheckIn.getPurchaseOrderNo() + "_"
                + whCheckIn.getWhCheckInItem().getSku();
        try {

            if (RedissonLockUtil.tryLock(key, 1, PrintSkuQrCodeRedisLock.CHECK_IN_PURCHASE_NO_SKU.getTimeout())) {
                if (StringUtils.isNotBlank(whCheckInItem.getFirstOrderType())) {
                    List<String> firstOrderTypeList=new ArrayList<>();
                    if (isFreeCheck){
                        firstOrderTypeList.add(CheckInFlags.NO_CHECK.getCode());
                    }

                    if (whCheckInItem.getFirstOrderType().contains(",")) {
                        firstOrderTypeList =new ArrayList<>(Arrays.asList(whCheckInItem.getFirstOrderType().split(",")));
                    }
                    else{
                        firstOrderTypeList.add(whCheckInItem.getFirstOrderType());
                    }
                    if (whCheckInItem.getQuantity() != null && whCheckInItem.getQuantity() > 0
                            && firstOrderTypeList.contains(CheckInFlags.SHELF_STRAIGHT_HAIR.getCode())) {
                        response = purchaseApvOutStockMatchService.checkUpdateStockMatch(whCheckInItem,
                                whCheckIn.getPurchaseOrderNo());
                        firstOrderTypeList.remove(CheckInFlags.SHELF_STRAIGHT_HAIR.getCode());
                    }
                    if (StatusCode.FAIL.equals(response.getStatus())) {
                        return response;
                    }
                    if (StatusCode.SUCCESS.equals(response.getStatus()) &&  CheckInFlags.SHELF_STRAIGHT_HAIR.getName().equals(response.getMessage())) {
                        isShelfStraightHair=true;
                        firstOrderTypeList.add(CheckInFlags.SHELF_STRAIGHT_HAIR.getCode());
                    }

                    if (CollectionUtils.isNotEmpty(firstOrderTypeList)) {
                        whCheckInItem.setFirstOrderType(StringUtils.join(firstOrderTypeList, ","));
                    }
                    else {
                        whCheckInItem.setFirstOrderType(null);
                    }
                }else if (isFreeCheck){
                    String firstOrderType = whCheckInItem.getFirstOrderType();
                    if (StringUtils.isNotBlank(firstOrderType)){
                        firstOrderType+=","+CheckInFlags.NO_CHECK.getCode();
                    }
                    else{
                        firstOrderType=CheckInFlags.NO_CHECK.getCode();
                    }
                    whCheckInItem.setFirstOrderType(firstOrderType);
                }

                if(whCheckIn.getWhCheckInException() != null && whCheckIn.getWhCheckInException().getBoxNo() != null){
                    WhBox whBox = whBoxService.queryWhBoxByBoxNo(whCheckIn.getWhCheckInException().getBoxNo());
                    if (whBox != null) {
                        if (StringUtils.isNotBlank(whBox.getRelationNo())) {
                            response.setMessage(whCheckIn.getWhCheckInException().getBoxNo()+"周转筐已使用,请确认后再入库!");
                            return response;
                        }
                    }
                }
                // 标记服装量尺标签
                if (CollectionUtils.isNotEmpty(whCheckIn.getCheckInClothing()))
                    whCheckInItem.setFirstOrderType(StringUtils.isEmpty(whCheckInItem.getFirstOrderType())
                            ? CheckInFlags.CLOTHING_MEASURE.getCode()
                            : whCheckInItem.getFirstOrderType() + "," + CheckInFlags.CLOTHING_MEASURE.getCode());

                whCheckInItem.setIsFreeCheck(isFreeCheck);
                if (isFreeCheck){
                    whCheckIn.setQcTime(new Timestamp(System.currentTimeMillis()));
                }
                //多货循环调
                if (CollectionUtils.isNotEmpty(whCheckIn.getWhCheckInExcessList())) {
                    List<WhCheckInExcess> whCheckInExcesses = whCheckIn.getWhCheckInExcessList().stream().filter(w -> w.getMatchedQuantity() != null
                            && w.getMatchedQuantity() > 0).collect(toList());
                    if (CollectionUtils.isEmpty(whCheckInExcesses)) {
                        response.setMessage("多货匹配数量不能为0！");
                        return response;
                    }
                    whCheckIn.setWhCheckInExcessList(whCheckInExcesses);
                    response = whCheckInService.createExcessCheckInAndCheckInException(Arrays.asList(whCheckInItem.getSku()),
                            whCheckIn, receiveBoxNo, trackingNumbers, arrayTrackingNo, purchaseQuantity);
                } else {
                    response = whCheckInService.createCheckInAndCheckInException(Arrays.asList(whCheckInItem.getSku()),
                            whCheckIn, receiveBoxNo, trackingNumbers, arrayTrackingNo, purchaseQuantity);
                }
                if (StatusCode.FAIL.equals(response.getStatus())) {
                    return response;
                }
            }
            else {
                response.setMessage("有人正在提交该采购单，请稍后再提交！");
                return response;
            }

        }
        catch (RuntimeException e) {
            logger.error(e.getMessage(), e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
        finally {
            RedissonLockUtil.unlock(key);
        }

        //判断是否是促销sku,促销sku则取消促销标签
        whInvoicingChangeMonthCountLocalService.checkInCancelSkuPromotion(Arrays.asList(whCheckInItem.getSku()));

        sendMessageToPms(response.getBody());

        List<WhCheckIn> inStocks = new ArrayList<>();
        inStocks.add(whCheckIn);
        response.getBody().put("inStocks", inStocks);
        response.getBody().put("isShelfStraightHair", isShelfStraightHair);
        response.getBody().put("isFreeCheck", isFreeCheck);
        response.getBody().put("noLabel", noLabel);
        return response;
    }

    /**
     * 美景生成入库单时拦截正在移库的SKU
     * @param sku
     * @return
     */
    private String chekMoveLocation(String sku) {
        MoveLocationTaskItemQueryCondition queryCondition = new MoveLocationTaskItemQueryCondition();
        queryCondition.setSku(sku);
        List<MoveLocationTaskItem> items = moveLocationTaskItemService.queryMoveLocationTaskItems(queryCondition, null);
        //0-待领取 1-拣货中 2-待上架 4-已完成 5-已废弃
        List<MoveLocationTaskItem> collect = items.stream().filter(item ->
                item.getStatus().equals(MoveLocationTaskStatus.WAIT_RECEIVE.intCode()) ||
                        item.getStatus().equals(MoveLocationTaskStatus.PICKING.intCode()) ||
                        item.getStatus().equals(MoveLocationTaskStatus.WAITTING_UP.intCode()) ||
                        item.getStatus().equals(MoveLocationTaskStatus.UPING.intCode())
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            return sku + "正在跨仓移库单，不能操作！";
        }
        return null;
    }

    /**
     * 点数完成，批量生成少货/少sku异常单
     * 
     * @param purchaseOrderArrayStr
     * @return
     */
    @RequestMapping(value = "batchCreateWhCheckInException", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson batchCreateWhCheckInException(
            @RequestParam(value = "purchaseOrderArrayStr") String purchaseOrderArrayStr,
            @RequestParam(value = "intersection") String intersection,
            @RequestParam(value = "expressId") String expressId, @RequestParam(value = "boxNo") String boxNo) {
        ResponseJson rsp = new ResponseJson();
        if (StringUtils.isBlank(purchaseOrderArrayStr)) {
            rsp.setMessage("没有相关采购单，生成异常失败！");
            rsp.setStatus(StatusCode.FAIL);
            return rsp;
        }
        else {
            rsp = whCheckInService.batchCreateCheckInExceptionToPms(purchaseOrderArrayStr, intersection, boxNo,
                    expressId);
            sendMessageToPms(rsp.getBody());
        }
        return rsp;
    }

    /**
     *
     * @Title: obtainWhBox
     * @Description: 提货扫描
     *
     * @param domain
     * @return
     */
    @RequestMapping(value = "obtainWhBox", method = { RequestMethod.GET })
    public String obtainWhBox(@ModelAttribute("domain") WhCheckInDo domain) {
        logger.info("checkin/scans/obtainWhBox");
        return "checkin/obtain_scan_box_view";
    }

    @RequestMapping(value = "checkBox", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson checkBox(@ModelAttribute("domain") WhCheckInDo domain, @RequestParam("boxNo") String boxNo) {
        logger.info("checkin/scans/checkBox: boxNo[" + boxNo + "]");
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        List<WhCheckInItem> whCheckInItems = whCheckInItemService.querySkuByBox(boxNo);
        if (CollectionUtils.isNotEmpty(whCheckInItems)) {
            WhCheckIn whCheckIn = whCheckInService.getWhCheckIn(whCheckInItems.get(0).getInId());
            if (CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())
                    || CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus())) {// 上架失败可以重新提货
                response.setMessage(JSON.toJSONString(whCheckInItems.get(0)));
                response.setStatus(StatusCode.SUCCESS);
            }
            else if (CheckInStatus.QC_NG.intCode().equals(whCheckIn.getStatus())) {
                response.setMessage("不良品，无法提货");
            }
            else if (whCheckIn.getStatus() < CheckInStatus.WAITING_UP.intCode()) {
                response.setMessage("未QC，无法提货");
            }
            else {
                response.setMessage("不是待上架或上架失败状态，无法提货");
            }
        }
        else {
            response.setMessage("未绑定,无提货信息");
        }
        logger.info("response: " + response);
        return response;
    }

    /**
     *
     * @Title: upIng
     * @Description: 提货扫描-上架中
     *
     * @param domain
     * @param uuidSku
     * @return
     */
    @RequestMapping(value = "upIng", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson upIng(@ModelAttribute("domain") WhCheckInDo domain, @RequestParam("uuidSku") String uuidSku,
            @RequestParam("obtainUser") Integer obtainUser) {
        logger.info("checkin/scans/upIng: uuidSku[" + uuidSku + "], obtainUser[" + obtainUser + "]");
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        try {
            if (StringUtils.isBlank(uuidSku) || !uuidSku.contains("=")) {
                response.setMessage("扫描唯一码有误");
                return response;
            }

            String sku = StringUtils.split(uuidSku, "=")[0];
            String uuid = StringUtils.split(uuidSku, "=")[1];
            WhUniqueSkuQueryCondition uuidQuery = new WhUniqueSkuQueryCondition();
            uuidQuery.setSku(sku);
            uuidQuery.setUuid(uuid);
            uuidQuery.setUuid(uuid);
            WhUniqueSku whUnique = whUniqueSkuService.queryWhUniqueSku(uuidQuery);
            if (null == whUnique) {
                response.setMessage("未绑定,无提货信息");
                return response;
            }
            Integer inId = whUnique.getRelationId();
            WhCheckIn whCheckIn = whCheckInService.getWhCheckInDetail(inId);
            if (null == whCheckIn) {
                response.setMessage("未绑定,无提货信息");
                return response;
            }

            response = whCheckInService.doUpIngCheckIn(Arrays.asList(sku), whCheckIn, obtainUser);

        }
        catch (RuntimeException e) {
            response.setMessage(e.getMessage());
            return response;
        }

        return response;
    }

    @RequestMapping(value = "createCheckIn/checkBox", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson createCheckInTocheckBox(@ModelAttribute("domain") WhCheckInDo domain,
            @RequestParam("boxNo") String boxNo, @RequestParam("type") String type) {
        logger.info("checkin/scans/createCheckIn/checkBox: boxNo[" + boxNo + "]");
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox != null) {
            if (StringUtils.isNotBlank(whBox.getRelationNo())) {
                response.setMessage("此周转筐已使用,请重新输入");
                return response;
            }
            if (StringUtils.isNotBlank(type) && type.equals("checkIn")
                    && !BoxType.PURCHASE.intCode().equals(whBox.getType())) {
                response.setMessage("此周转筐不是入库专用,请重新输入");
                return response;
            }
            if (StringUtils.isNotBlank(type) && type.equals("exception")
                    && !BoxType.CHECKIN_EXCEPTION.intCode().equals(whBox.getType())) {
                response.setMessage("此周转筐不是入库异常专用,请重新输入");
                return response;
            }
            response.setMessage(JSON.toJSONString(whBox));
            response.setStatus(StatusCode.SUCCESS);

        }
        else {
            response.setMessage("无此周转筐,请重新输入");
        }
        logger.info("response: " + response);
        return response;
    }

    /**
     * 发送异常消息到采购
     * 
     * @param map
     * @return
     */
    public void sendMessageToPms(Map<String, Object> map) {
        if (map.size() > 0) {
            PushCheckInException pushCheckInException = (PushCheckInException) map.get("pushCheckInException");
            // 推送异常消息到采购系统
            logger.info("start to send createException message to pms ====pushSize:"
                    + pushCheckInException.getWhCheckInExceptionList().size());
            rabbitmqProducerService.pushCheckInExceptionMsgToPms(null, null, pushCheckInException);
            logger.info("send createException message to pms end ");
        }
    }

    /**
     * 多货匹配采购单接口
     */
    @RequestMapping(value = "matchExcess", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson matchExcess(@RequestParam("currentExcessSku") String currentExcessSku,
                                    @RequestParam("currentExcessPurchaseOrderNo") String currentExcessPurchaseOrderNo,
                                    @RequestParam("excessQuantity") Integer excessQuantity) {
        logger.info("checkin/scans/matchExcess: sku[{}], poNo[{}], qty[{}]", currentExcessSku, currentExcessPurchaseOrderNo, excessQuantity);
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            if (StringUtils.isBlank(currentExcessSku) || StringUtils.isBlank(currentExcessPurchaseOrderNo) || excessQuantity == null) {
                responseJson.setMessage("参数不能为空");
                return responseJson;
            }
            // 构造查询条件
          //  WhCheckInExcessQueryCondition query = new WhCheckInExcessQueryCondition();
         //   query.setSku(currentExcessSku);
         //   List<whCheckInExcess> resultList = whcheckInExcessService.queryWhcheckInExcesss(query, null),responseJson.setstatus(StatusCode.SUCCESS):Map<String,Object>map =new HashMap<>();map.put("data",resultList);
         //   responseJson.setBody(map);// 构造请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("purchaseOrderNo", currentExcessPurchaseOrderNo);
            requestBody.put("sku", currentExcessSku);
            requestBody.put("excessQuantity", excessQuantity);

            // 获取接口URL
            String url = CacheUtils.SystemParamGet("PMS_PARAM.HANDLE_MULTIPLE_SKU_INBOUND").getParamValue();
           if (StringUtils.isBlank(url)) {
                responseJson.setMessage("多货匹配采购单失败: 未配置接口URL");
                return responseJson;
           }
            // 调用外部接口
            ApiResult apiResult = HttpExtendUtils.post(url, HttpUtils.ACCESS_TOKEN, requestBody, ApiResult.class,120000,120000);
            log.info("调用采购多货匹配返回："+ JSONObject.toJSONString(apiResult));
            if (apiResult != null && apiResult.isSuccess()) {
                responseJson.setStatus(StatusCode.SUCCESS);
                Map<String, Object> map = new HashMap<>();
                // 字段手动映射
                List<WhCheckInExcess> resultList = new ArrayList<>();
                Object resultObj = apiResult.getResult();
                if (resultObj instanceof List) {
                    List<?> rawList = (List<?>) resultObj;
                    for (Object item : rawList) {
                        if (item instanceof Map) {
                            Map<?,?> rawMap = (Map<?,?>) item;
                            // 判空校验
                            if (rawMap.get("purchaseOrderNo") == null ||
                                rawMap.get("matchQuantity") == null ||
                                rawMap.get("onloadQuantity") == null ||
                                rawMap.get("creater") == null) {
                                responseJson.setStatus(StatusCode.FAIL);
                                responseJson.setMessage("多货匹配采购单失败: 字段缺失"+JSON.toJSONString(rawMap));
                                return responseJson;
                            }
                            WhCheckInExcess excess = new WhCheckInExcess();
                            excess.setSku(currentExcessSku);
                            excess.setExcessQuantity(excessQuantity);
                            // purchaseOrderNo
                            excess.setPurchaseOrderNo(String.valueOf(rawMap.get("purchaseOrderNo")));
                            // matchedQuantity <-> matchQuantity
                            excess.setMatchedQuantity(((Number)rawMap.get("matchQuantity")).intValue());
                            // purchaseQuantity <-> onloadQuantity
                            excess.setPurchaseQuantity(((Number)rawMap.get("onloadQuantity")).intValue());
                            if (excess.getPurchaseQuantity() == 0 || excess.getMatchedQuantity() == 0) {
                               continue;
                            }
                            // purchaseUser <-> creater
                            excess.setPurchaseUser(String.valueOf(rawMap.get("creater")));
                            // processingMethod <-> matchType
                            if (rawMap.get("matchType") != null) excess.setProcessingMethod(String.valueOf(rawMap.get("matchType")));
                            
                            // Add new field mappings
                            // shippingOrderNo
                            if (rawMap.get("shippingOrderNo") != null) {
                                excess.setTrackingNumber(String.valueOf(rawMap.get("shippingOrderNo")));
                            }
                            // shippingCost
                            if (rawMap.get("shippingCost") != null) {
                                excess.setShippingCost((new BigDecimal((Double)rawMap.get("shippingCost"))));
                            }
                            // totalWeight
                            if (rawMap.get("totalWeight") != null) {
                                excess.setWeight(((Number)rawMap.get("totalWeight")).doubleValue());
                            }
                            // vendorCode
                            if (rawMap.get("vendorCode") != null) {
                                excess.setSupplierId(String.valueOf(rawMap.get("vendorCode")));
                            }
                            // vendorName
                            if (rawMap.get("vendorName") != null) {
                                excess.setVendorName(String.valueOf(rawMap.get("vendorName")));
                            }
                            
                            resultList.add(excess);
                        }
                    }
                }
                map.put("data", resultList);
                responseJson.setBody(map);
            } else {
                responseJson.setMessage(apiResult != null ? apiResult.getErrorMsg() : "多货匹配采购单失败: 外部接口无响应");
            }
        } catch (Exception e) {
            logger.error("多货匹配采购单异常", e);
            responseJson.setMessage("多货匹配采购单失败: " + e.getMessage());
        }
        return responseJson;
    }

    /**
     * 验证周转筐号是否可用于物流收货
     * @param boxNo 周转筐号
     * @return 验证结果
     */
    @RequestMapping(value = "validateBox", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson validateBox(@RequestParam("boxNo") String boxNo) {
        logger.info("checkin/scans/validateBox: boxNo[" + boxNo + "]");
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        
        try {
            if (StringUtils.isBlank(boxNo)) {
                responseJson.setMessage("周转筐号不能为空");
                return responseJson;
            }
            
            // 查询周转筐是否存在
            WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
            if (whBox == null) {
                responseJson.setMessage("周转筐号不存在");
                return responseJson;
            }
            
            // 检查周转筐类型是否为收货周转筐
            if (!whBox.getType().equals(BoxType.WL_RECEIVE.intCode())) {
                responseJson.setMessage("请使用物流收货周转筐");
                return responseJson;
            }
            
            // 检查周转筐状态
            if (BoxStatus.ALREADY_USED.intCode().equals(whBox.getStatus()) && whBox.getPresentUser() != null) {
                responseJson.setMessage("此周转筐已被" + whBox.getPresentUserStr() + "领取,请重新输入");
                return responseJson;
            }
            if (BoxStatus.ALREADY_USED.intCode().equals(whBox.getStatus()) && whBox.getConsignee() != null
                    && !whBox.getConsignee().equals(DataContextHolder.getUserId())) {
                responseJson.setMessage("此周转筐正在被" + whBox.getConsigneeStr() + "使用,请重新输入");
                return responseJson;
            }
            
            responseJson.setStatus(StatusCode.SUCCESS);
            responseJson.setMessage("验证通过");
            
        } catch (Exception e) {
            logger.error("验证周转筐号异常: {}", e.getMessage(), e);
            responseJson.setMessage("验证周转筐号失败: " + e.getMessage());
        }
        
        return responseJson;
    }

}
