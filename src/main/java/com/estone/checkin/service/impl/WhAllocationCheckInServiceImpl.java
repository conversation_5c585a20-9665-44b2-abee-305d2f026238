package com.estone.checkin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.allocation.bean.WhApvAllocation;
import com.estone.allocation.bean.WhApvAllocationItem;
import com.estone.allocation.bean.WhApvAllocationItemQueryCondition;
import com.estone.allocation.bean.WhApvAllocationQueryCondition;
import com.estone.allocation.enums.AllocationStatusEnum;
import com.estone.allocation.enums.AllocationUpStatusEnum;
import com.estone.allocation.enums.AllocationtInventoryType;
import com.estone.allocation.service.WhApvAllocationService;
import com.estone.allocation.util.AllocationPushUtil;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.android.domain.AndroidProductDo;
import com.estone.checkin.bean.*;
import com.estone.checkin.dao.WhAllocationCheckInDao;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.WhAllocationCheckInExceptionService;
import com.estone.checkin.service.WhAllocationCheckInItemService;
import com.estone.checkin.service.WhAllocationCheckInService;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.common.enums.LogModule;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.util.*;
import com.estone.sku.bean.*;
import com.estone.sku.enums.ProcessType;
import com.estone.sku.enums.UniqueSkuFrom;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.*;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.model.ProductSkuMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.service.impl.AmqMessageServiceImpl;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.service.SaleUserService;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.enums.LocationTagEnum;
import com.estone.warehouse.service.*;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("whAllocationCheckInService")
public class WhAllocationCheckInServiceImpl implements WhAllocationCheckInService {

    private static Logger logger = LoggerFactory.getLogger(WhAllocationCheckInServiceImpl.class);

    final static SystemLogUtils CHECKINLOG = SystemLogUtils.create(LogModule.ALLOCATION_CHECKIN.getCode());

    @Resource
    private WhAllocationCheckInDao whAllocationCheckInDao;

    @Resource
    private WhAllocationCheckInItemService whAllocationCheckInItemService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhRecordService whRecordService;

    @Resource
    private WhApvAllocationService whApvAllocationService;

    @Resource
    private WhAllocationCheckInExceptionService whAllocationCheckInExceptionService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private AllocationUpdateStockService allocationUpdateStockService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhAllocationCheckInService whAllocationCheckInService;

    @Resource
    private SaleUserService saleUserService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private ExpManageService expManageService;

    @Resource
    private ExpManageItemService expManageItemService;
    @Resource
    private UniqueSkuExpRelationService uniqueSkuExpRelationService;
    @Resource
    private WhStockService whStockService;
    @Resource
    private WhUniqueSkuLogService whUniqueSkuLogService;
    
    @Resource
    private LocationMoveInfoService locationMoveInfoService;

    @Resource
    private CheckInUpdateStockService checkInUpdateStockService;

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public WhAllocationCheckIn getWhAllocationCheckIn(Integer id) {
        WhAllocationCheckIn whAllocationCheckIn = whAllocationCheckInDao.queryWhAllocationCheckIn(id);
        return whAllocationCheckIn;
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public WhAllocationCheckIn getWhAllocationCheckInDetail(Integer id) {
        WhAllocationCheckIn whAllocationCheckIn = whAllocationCheckInDao.queryWhAllocationCheckIn(id);
        // 关联查询
        return whAllocationCheckIn;
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public WhAllocationCheckIn queryWhAllocationCheckIn(WhAllocationCheckInQueryCondition query) {
        Assert.notNull(query);
        WhAllocationCheckIn whAllocationCheckIn = whAllocationCheckInDao.queryWhAllocationCheckIn(query);
        return whAllocationCheckIn;
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public List<WhAllocationCheckIn> queryAllWhAllocationCheckIns() {
        return whAllocationCheckInDao.queryWhAllocationCheckInList();
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public List<WhAllocationCheckIn> queryWhAllocationCheckIns(WhAllocationCheckInQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whAllocationCheckInDao.queryWhAllocationCheckInCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhAllocationCheckIn>();
            }
        }
        List<WhAllocationCheckIn> whAllocationCheckIns = whAllocationCheckInDao.queryWhAllocationCheckInList(query,
                pager);
        return whAllocationCheckIns;
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public void createWhAllocationCheckIn(WhAllocationCheckIn whAllocationCheckIn) {
        try {
            whAllocationCheckInDao.createWhAllocationCheckIn(whAllocationCheckIn);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public void batchCreateWhAllocationCheckIn(List<WhAllocationCheckIn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whAllocationCheckInDao.batchCreateWhAllocationCheckIn(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public void deleteWhAllocationCheckIn(Integer id) {
        try {
            whAllocationCheckInDao.deleteWhAllocationCheckIn(id);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    @Override
    public int updateWhAllocationCheckIn(WhAllocationCheckIn whAllocationCheckIn) {
        try {
            return whAllocationCheckInDao.updateWhAllocationCheckIn(whAllocationCheckIn);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
    public void batchUpdateWhAllocationCheckIn(List<WhAllocationCheckIn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whAllocationCheckInDao.batchUpdateWhAllocationCheckIn(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public WhAllocationCheckIn queryWhAllocationCheckInByBoxNo(String boxNo) {
        WhAllocationCheckIn whAllocationCheckIn = null;
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox != null && whBox.getType().equals(BoxType.ALLOCATION_CHECKIN.intCode())
                && StringUtils.isNotBlank(whBox.getRelationNo())) {
            whAllocationCheckIn = this.getWhAllocationCheckIn(Integer.valueOf(whBox.getRelationNo()));
        }
        return whAllocationCheckIn;
    }

    /**
     * 点数入库
     *
     * @param whAllocationCheckIn
     */
    @Override
    @StockServicelock
    public void createWhAllocationCheckInAndWhAllocationCheckInItem(List<String> skus,WhAllocationCheckIn whAllocationCheckIn,List<String> uuidList) {
        WhAllocationCheckInItem whAllocationCheckInItem = whAllocationCheckIn.getWhAllocationCheckInItem();
        this.createWhAllocationCheckIn(whAllocationCheckIn);
        logger.info("create whAllocationCheckIn :" + whAllocationCheckIn);

        // 创建成功先绑定周转码、防止重复提交
        String boxNo = whAllocationCheckIn.getBoxNo();
        if (whAllocationCheckIn.getInId() != null) {
            if (whAllocationCheckInItem != null) {
                whAllocationCheckInItem.setInId(whAllocationCheckIn.getInId());
                whAllocationCheckInItemService.createWhAllocationCheckInItem(whAllocationCheckInItem);
                logger.info("create whAllocationCheckInItem :" + whAllocationCheckInItem);
                Integer exceptionQuantity = whAllocationCheckIn.getWhAllocationCheckInItem().getExceptionQuantity();
                if (null != exceptionQuantity && exceptionQuantity > 0) {
                    WhAllocationCheckInException checkInException = new WhAllocationCheckInException();
                    checkInException.setUuId(UUID.randomUUID().toString());
                    checkInException.setInId(whAllocationCheckInItem.getInId());
                    checkInException.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());
                    checkInException.setSku(whAllocationCheckInItem.getSku());
                    checkInException.setExceptionFrom(ExceptionFrom.CHECK_IN_EXCEPTION.intCode());
                    checkInException.setExceptionType(whAllocationCheckInItem.getExceptionTypeStr());
                    checkInException.setQuantity(whAllocationCheckInItem.getExceptionQuantity());
                    checkInException.setStatus(AllocationExceptionStatus.UNCONFIRM.intCode());
                    Integer deliveryWarehouseId = WarehousePropertyEnum.HHD.intCode();
                    Integer destWarehouseId = CacheUtils.getLocalWarehouseId();
                    if (destWarehouseId.equals(WarehousePropertyEnum.HHD.intCode())) {
                        deliveryWarehouseId = WarehousePropertyEnum.NN.intCode();
                    }
                    checkInException.setDeliveryWarehouseId(deliveryWarehouseId);
                    checkInException.setDestWarehouseId(destWarehouseId);
                    checkInException.setCreatedBy(DataContextHolder.getUserId());
                    checkInException.setCreationDate(new Timestamp(System.currentTimeMillis()));
                    whAllocationCheckInExceptionService.createWhAllocationCheckInException(checkInException);
                }

                // TODO 调拨入库加库存
                if (!allocationUpdateStockService.updateCheckIn(whAllocationCheckIn)) {
                    throw new RuntimeException("调拨入库修改库存失败");
                }
            }
            if (StringUtils.isNotBlank(boxNo)) {
                String[][] logs = new String[][]{{"生成入库单", ""},
                        {"relationNo", whAllocationCheckIn.getInId().toString()}};
                int updated = whBoxService.updateWhBoxOfBinding(boxNo, whAllocationCheckIn.getInId().toString(), logs);
                if (updated >= 1) {
                    logger.info("绑定周转码: relationNo[" + whAllocationCheckIn.getInId() + "], boxNo["
                            + whAllocationCheckIn.getBoxNo() + "], boxUpdated[" + updated + "]");
                }
            }
            // 校验唯一码，并同步
            if (whAllocationCheckInItem != null && CollectionUtils.isNotEmpty(uuidList)) {
                String error = syncUUid(uuidList,whAllocationCheckIn);
                if (StringUtils.isNotBlank(error)) {
                    throw new RuntimeException(error);
                }
            }
            CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.CREATE_CHECKIN.getName(), new String[][]{
                    {"SKU", whAllocationCheckIn.getWhAllocationCheckInItem().getSku()},
                    {"良品数量", whAllocationCheckIn.getWhAllocationCheckInItem().getQuantity().toString()},
                    {"不良品数量", whAllocationCheckIn.getWhAllocationCheckInItem().getExceptionQuantity() == null ? "0"
                            : whAllocationCheckIn.getWhAllocationCheckInItem().getExceptionQuantity().toString()}});
        }



    }

    // 同步唯一码
    private String syncUUid(List<String> uuidList,WhAllocationCheckIn whAllocationCheckIn) {

        // 查询当前唯一是否是从本地调拨过去的
        WhUniqueSkuQueryCondition query = new WhUniqueSkuQueryCondition();
        query.setUuidList(uuidList);
        List<WhUniqueSku> whUniqueSkus = whUniqueSkuService.queryWhUniqueSkusAndLogs(query, null);
        //废弃调拨入库单的重新入库扫描唯一码

        List<WhUniqueSku> updateUniqueSkus = new ArrayList<>();
        for (WhUniqueSku uniqueSkus : whUniqueSkus) {
            if (UniqueSkuStep.SCRAP.intCode().equals(uniqueSkus.getStep())
                ||  UniqueSkuFrom.ALLOCATION_CHECK_IN_REPLENISH.intCode().equals(uniqueSkus.getSourceFrom())) {
                WhUniqueSku updateUniqueSku = new WhUniqueSku();
                updateUniqueSku.setId(uniqueSkus.getId());
                updateUniqueSku.setRelationId(whAllocationCheckIn.getInId());
                updateUniqueSku.setStep(UniqueSkuStep.ALLOCATION_CHECK_IN.intCode());
                updateUniqueSkus.add(updateUniqueSku);
                uuidList.remove(uniqueSkus.getSku()+"="+uniqueSkus.getUuid());
            }
        }
        if (CollectionUtils.isNotEmpty(updateUniqueSkus)) {
            whUniqueSkuService.batchUpdateWhUniqueSku(updateUniqueSkus);
        }
        if (CollectionUtils.isEmpty(uuidList)) {
            return null;
        }

        // 获取目标系统URL（示例配置需在WarehouseProperties中添加）
        String url = WarehouseProperties.getApiUrl("/foreigns/syncUuid/"+whAllocationCheckIn.getAllocationOrderNo());
        //String url =  "http://localhost:8282/wms/foreigns/syncUuid/"+whAllocationCheckIn.getAllocationOrderNo();
        ResponseJson remoteResponse = HttpExtendUtils.post(url, "",uuidList, ResponseJson.class, 30000, 30000);
        if (remoteResponse == null || !StatusCode.SUCCESS.equals(remoteResponse.getStatus())) {
            return (remoteResponse != null ? remoteResponse.getMessage() : "调出仓服务不可用");
        }
        Map<String, Object> body = remoteResponse.getBody();
        if (MapUtils.isEmpty(body))
            return "同步唯一码失败";

        // java8将whUniqueSkus转map,key为sku=uuid拼接，value为WhUniqueSku本身
        Map<String, WhUniqueSku> whUniqueSkuMap = whUniqueSkus.stream()
                .collect(Collectors.toMap(k -> k.getSku() + "=" +k.getUuid(), v -> v, (v1, v2) -> v1));
        List<SyncUniqueSkuParam> syncUniqueSkuParams = JSONObject.parseArray(JSON.toJSONString(body.get("syncUniqueSkuParams")), SyncUniqueSkuParam.class);

        List<WhUniqueSku> updateWhUniqueSku = new ArrayList<>();
        List<WhUniqueSkuLog> uniqueSkuLogs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(syncUniqueSkuParams)){
            for (SyncUniqueSkuParam syncUniqueSkuParam : syncUniqueSkuParams) {
                WhUniqueSku existWhUniqueSku = whUniqueSkuMap.get(syncUniqueSkuParam.getSku() + "=" + syncUniqueSkuParam.getUuid());
                WhUniqueSku whUniqueSku = new WhUniqueSku();
                whUniqueSku.setRelationId(whAllocationCheckIn.getInId());
                whUniqueSku.setTag(syncUniqueSkuParam.getTag());
                whUniqueSku.setSourceFrom(UniqueSkuFrom.ALLOCATION_CHECK_IN.intCode());
                whUniqueSku.setStep(UniqueSkuStep.ALLOCATION_CHECK_IN.intCode());
                if (existWhUniqueSku != null) {
                    whUniqueSku.setId(existWhUniqueSku.getId());
                    updateWhUniqueSku.add(whUniqueSku);
                    whUniqueSkus.removeIf(u -> u.getId().equals(existWhUniqueSku.getId()));
                }
                else {
                    whUniqueSku.setUuid(syncUniqueSkuParam.getUuid());
                    whUniqueSku.setSku(syncUniqueSkuParam.getSku());
                    whUniqueSkuService.createWhUniqueSku(whUniqueSku);
                }
                WhUniqueSkuLog uniqueSkuLog = new WhUniqueSkuLog();
                uniqueSkuLog.setUniqueId(whUniqueSku.getId());
                uniqueSkuLog.setStep(UniqueSkuStep.ALLOCATION_CHECK_IN.intCode());
                uniqueSkuLog.setContent("调拨入库");
                uniqueSkuLogs.add(uniqueSkuLog);

            }
        }

        if (CollectionUtils.isNotEmpty(whUniqueSkus)){
            whUniqueSkus.forEach(whUniqueSku -> {
                WhUniqueSku uniqueSku =  new WhUniqueSku();
                uniqueSku.setRelationId(whAllocationCheckIn.getInId());
                uniqueSku.setId(whUniqueSku.getId());
                updateWhUniqueSku.add(uniqueSku);

                WhUniqueSkuLog uniqueSkuLog = new WhUniqueSkuLog();
                uniqueSkuLog.setUniqueId(whUniqueSku.getId());
                uniqueSkuLog.setStep(UniqueSkuStep.ALLOCATION_CHECK_IN.intCode());
                uniqueSkuLog.setContent("调拨入库");
                uniqueSkuLogs.add(uniqueSkuLog);
            });
        }
        // 生成保质期批次信息
        List<String> expSkuList = GetSkuQcCategoryDescUtil.getExpSkuList(syncUniqueSkuParams.stream().
                map(SyncUniqueSkuParam::getSku).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(expSkuList)) {
            // 查询对应调拨单的批次信息
            WhApvAllocationItemQueryCondition itemQuery = new WhApvAllocationItemQueryCondition();
            itemQuery.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());
            itemQuery.setSku(whAllocationCheckIn.getWhAllocationCheckInItem().getSku());
            WhApvAllocationItem whApvAllocationItem = whApvAllocationService.queryWhApvAllocationItem(itemQuery);
            if (whApvAllocationItem != null && whApvAllocationItem.getAfterSaleAndExpInfo() != null) {
                List<UniqueSkuExpRelation> result = new ArrayList<>();
                UniqueSkuExpRelationQueryCondition uniqueSkuExpRelationQueryCondition = new UniqueSkuExpRelationQueryCondition();
                uniqueSkuExpRelationQueryCondition.setUuid(syncUniqueSkuParams.stream().map(SyncUniqueSkuParam::getUuid).collect(Collectors.joining(",")));
                uniqueSkuExpRelationQueryCondition.setSku(syncUniqueSkuParams.stream().map(SyncUniqueSkuParam::getSku).collect(Collectors.joining(",")));
                List<UniqueSkuExpRelation> uniqueSkuExpRelations = uniqueSkuExpRelationService.queryUniqueSkuExpRelations(uniqueSkuExpRelationQueryCondition, null);
                List<String> existUuids = uniqueSkuExpRelations.stream().map(u -> u.getSku() + "=" + u.getUuid()).collect(Collectors.toList());
                for (SyncUniqueSkuParam uniqueSku : syncUniqueSkuParams) {
                    if (existUuids.contains(uniqueSku.getSku() + "=" + uniqueSku.getUuid())) continue;
                    if (!expSkuList.contains(uniqueSku.getSku()) && WarehousePropertyEnum.HHD.getCode().equals(AmqMessageServiceImpl.getWarehouseId()))
                        continue;

                    UniqueSkuExpRelation expRelation = new UniqueSkuExpRelation();
                    expRelation.setRelationId(whAllocationCheckIn.getInId());
                    expRelation.setSku(uniqueSku.getSku());
                    expRelation.setUuid(uniqueSku.getUuid());
                    expRelation.setCreationDate(new Timestamp(System.currentTimeMillis()));
                    expRelation.setDays(whApvAllocationItem.getAfterSaleAndExpInfo().getDays());
                    expRelation.setExpNo(whApvAllocationItem.getAfterSaleAndExpInfo().getBatchNo());
                    expRelation.setExpDate(Timestamp.valueOf(whApvAllocationItem.getAfterSaleAndExpInfo().getExpDate() + " 00:00:00"));
                    expRelation.setProDate(Timestamp.valueOf(whApvAllocationItem.getAfterSaleAndExpInfo().getProDate() + " 00:00:00"));
                    result.add(expRelation);
                }
                uniqueSkuExpRelationService.batchCreateUniqueSkuExpRelation(result);
            }
        }
        whUniqueSkuService.batchUpdateWhUniqueSku(updateWhUniqueSku);
        for (WhUniqueSkuLog uniqueSkuLog : uniqueSkuLogs) {
            whUniqueSkuLogService.createWhUniqueSkuLog(uniqueSkuLog);
        }
        return null;
    }

    @Override
    public void batchMoveCheckInDiscarded(List<Integer> inIds, String reason) {
        for (Integer inId : inIds) {
            WhAllocationCheckIn whAllocationCheckIn = this.getWhAllocationCheckIn(inId);
            Integer status = whAllocationCheckIn.getStatus();
            if (status < CheckInStatus.CONFIRMED.intCode()) {
                whAllocationCheckIn.setStatus(CheckInStatus.DISCARDED.intCode());
                String comment = whAllocationCheckIn.getComment();
                if (StringUtils.isNotBlank(reason)) {
                    CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.DISCARDED.getName(), new String[][]{
                            {"status", CheckInStatus.DISCARDED.intCode().toString()}, {"reason", reason}});
                    if (StringUtils.isNotBlank(comment)) {
                        whAllocationCheckIn.setComment(comment + "<br/>废弃原因: " + reason);
                    } else {
                        whAllocationCheckIn.setComment("废弃原因: " + reason);
                    }
                } else {
                    CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.DISCARDED.getName(),
                            new String[][]{{"status", CheckInStatus.DISCARDED.intCode().toString()}});
                }
                this.updateWhAllocationCheckIn(whAllocationCheckIn);
                logger.info("批量废弃入库单: inId[" + inId + "], boxNo[" + whAllocationCheckIn.getBoxNo() + "], status["
                        + status + "], reason[" + reason + "]");

                if (StringUtils.isNotBlank(whAllocationCheckIn.getBoxNo())) {
                    String boxNo = whAllocationCheckIn.getBoxNo();
                    String[][] logs = new String[][]{{"废弃入库单", ""},
                            {"relationNo", whAllocationCheckIn.getInId().toString()}};
                    int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
                    if (updated >= 1) {
                        logger.info("废弃入库单后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
                    }
                }

            } else {
                logger.info("已上架(或已废弃)的入库单不能废弃: inId[" + inId + "],  allocationOrderNo["
                        + whAllocationCheckIn.getAllocationOrderNo() + "], status[" + status + "]");
            }
        }
    }

    @Override
    public String upSkuToLocation(AndroidProductDo domain) {
        String result = null;
        WhAllocationCheckIn whAllocationCheckIn = this.getWhAllocationCheckIn(domain.getInId());
        WhAllocationCheckInItem whAllocationCheckInItem = whAllocationCheckIn.getWhAllocationCheckInItem();
        boolean flag = false;
        if (CheckInStatus.UPERROR.intCode().equals(whAllocationCheckIn.getStatus())) {
            flag = true;
        }

        // 上架数量
        Integer countQuantity = domain.getQuantity();
        // 装箱数量
        Integer boxQuantity = whAllocationCheckInItem.getBoxQuantity();

        if (whAllocationCheckIn != null && (CheckInStatus.UPING.intCode().equals(whAllocationCheckIn.getStatus()) || flag)
                && whAllocationCheckInItem != null) {
            if (domain.getQuantity() != null && domain.getQuantity() > 0
                    && domain.getQuantity() <= (whAllocationCheckInItem.getQcQuantity() == null
                    ? whAllocationCheckInItem.getQuantity()
                    : whAllocationCheckInItem.getQcQuantity())) {

                boolean result1 = false;
                boolean itemAllUp = false;
                String errorMessage = "";
                // 修改调拨单上架信息
                if (StringUtils.isNotBlank(whAllocationCheckIn.getAllocationOrderNo())) {
                    WhAllocationCheckInQueryCondition query = new WhAllocationCheckInQueryCondition();
                    query.setAllocationOrderNo(whAllocationCheckIn.getAllocationOrderNo());
                    query.setSku(whAllocationCheckInItem.getSku());
                    List<WhAllocationCheckIn> whAllocationCheckInList = whAllocationCheckInDao
                            .queryWhAllocationCheckInList(query, null);
                    Integer allotSurplusQuantity = domain.getQuantity();
                    for (WhAllocationCheckIn dbWhAllocationCheckIn : whAllocationCheckInList) {
                        allotSurplusQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem()
                                .getUpQuantity() == null ? 0
                                : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getUpQuantity();
                        countQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem().getUpQuantity() == null ? 0
                                : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getUpQuantity();
                        countQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem()
                                .getQcExceptionQuantity() == null ? 0
                                : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getQcExceptionQuantity();
                        countQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem()
                                .getExceptionQuantity() == null ? 0
                                : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getExceptionQuantity();
                    }

                    WhApvAllocationItemQueryCondition queryCondition = new WhApvAllocationItemQueryCondition();
                    queryCondition.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());// 调拨单号
                    queryCondition.setUpNum(allotSurplusQuantity);// 上架数量
                    queryCondition.setSku(whAllocationCheckInItem.getSku());// sku

                    if (countQuantity.equals(boxQuantity)) {
                        queryCondition.setUpStatus(3);// 已上架
                        itemAllUp = true;
                    } else {
                        queryCondition.setUpStatus(1);// 部分上架
                    }
                    ResponseJson responseJson = whApvAllocationService.updateApvAllocationByUp(queryCondition);
                    if (responseJson.getStatus().equals(StatusCode.SUCCESS)) {
                        result1 = true;
                    }
                    if (!result1 && StringUtils.isBlank(responseJson.getMessage())) {
                        errorMessage = "上架数量回写异常";
                    }
                    if (StringUtils.isNotBlank(responseJson.getMessage())
                            && responseJson.getMessage().contains("Read timed out")) {
                        errorMessage = "Read timed out";
                    }

                }

                Boolean result2 = false;

                // TODO
                // WMS加库存是否成功
                if (result1) {
                    // 修改调拨单状态
                    updateAllocationStatus(itemAllUp, whAllocationCheckIn.getAllocationOrderNo(),
                            whAllocationCheckInItem.getSku(), whAllocationCheckIn.getInId());

                    List<WhApvAllocationItem> whApvAllocationItemList = new ArrayList<>();
                    WhApvAllocationItem whApvAllocationItem = new WhApvAllocationItem();
                    whApvAllocationItem.setSku(whAllocationCheckInItem.getSku());
                    whApvAllocationItem.setAllotSurplusQuantity(domain.getQuantity());
                    whApvAllocationItem.setUpNum(domain.getQuantity());

                    whApvAllocationItem.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());
                    whApvAllocationItemList.add(whApvAllocationItem);

                    if (CollectionUtils.isNotEmpty(whRecordService.batchInventoryLogic(whApvAllocationItemList,
                            AllocationtInventoryType.ADJUST_SURPLUS_QUANTITY.intCode(), true))) {
                        result2 = true;

                        whAllocationCheckIn.setStatus(CheckInStatus.CONFIRMED.intCode());// 已经入库
                        whAllocationCheckIn.setUpUser(DataContextHolder.getUserId());// 上架员
                        whAllocationCheckIn.setUpTime(new Timestamp(System.currentTimeMillis()));// 上架时间
                        this.updateWhAllocationCheckIn(whAllocationCheckIn);
                        CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.CONFIRMED.getName(),
                                new String[][]{{"SKU", whAllocationCheckInItem.getSku()},
                                        {"数量", domain.getQuantity().toString()}, {"上架数量回写", result1 + ""},
                                        {"加库存", result2 + ""}});

                        logger.info("PDA上架: inId[" + whAllocationCheckIn.getInId() + "], boxNo["
                                + whAllocationCheckIn.getBoxNo() + "], allocationOrderNo["
                                + whAllocationCheckIn.getAllocationOrderNo() + "], sku["
                                + whAllocationCheckInItem.getSku() + "], quantity[" + domain.getQuantity() + "]");

                        // 推送调拨数据至产品系统
                        ProductSkuMessage allocationMessage = new ProductSkuMessage();
                        allocationMessage.setSku(whAllocationCheckInItem.getSku());
                        Integer allocationQuantity = boxQuantity - countQuantity;
                        allocationMessage.setAllocationQuantity(allocationQuantity < 0 ? 0 : allocationQuantity);
                        AmqMessage amqMessage = AssembleMessageDataUtils.assembleStockData(allocationMessage);
                        amqMessageService.createAmqMessage(amqMessage);
                    }

                }
                if (!(result1 && result2)) {
                    CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.UPERROR.getName(),
                            new String[][]{{"SKU", whAllocationCheckInItem.getSku()},
                                    {"数量", domain.getQuantity().toString()}, {"加库存", result2 + ""}});

                    whAllocationCheckIn.setStatus(CheckInStatus.UPERROR.intCode());// 上架失败
                    whAllocationCheckIn.setUpUser(DataContextHolder.getUserId());// 上架员
                    whAllocationCheckIn.setUpTime(new Timestamp(System.currentTimeMillis()));// 上架时间
                    this.updateWhAllocationCheckIn(whAllocationCheckIn);

                    if (StringUtils.isBlank(errorMessage)) {
                        result = "失败原因：null";
                    } else {
                        result = "失败原因:" + errorMessage + ",WMS加库存失败";
                    }
                }
                WhAllocationCheckInItem updateItem = new WhAllocationCheckInItem();
                updateItem.setItemId(whAllocationCheckInItem.getItemId());
                updateItem.setUpQuantity(domain.getQuantity());
                whAllocationCheckInItemService.updateWhAllocationCheckInItem(updateItem);// 更新上架数量
            } else {
                result = "上架数量大于入库数量";
            }
        } else if (CheckInStatus.CONFIRMED.intCode().equals(whAllocationCheckIn.getStatus())) {
            result = "重复操作,入库单已上架";
        } else {
            result = "信息有误,不允许上架";
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> queryDownload(WhAllocationCheckInQueryCondition query, Pager pager) {
        Assert.notNull(query);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        int count = whAllocationCheckInDao.queryWhAllocationCheckInCount(query);
        if (query.getDownload() && count > 100000) {
            // 导出限制10万条数据
            return list;
        }
        list = whAllocationCheckInDao.queryDownload(query, pager);
        return list;
    }

    /**
     * 调拨单修改状态
     *
     * @param allocationOrderNo
     * @return
     */
    public void updateAllocationStatus(boolean itemAllUp, String allocationOrderNo, String sku, Integer inId) {
        int status = AllocationStatusEnum.SEGMENT_STORAGE.intCode();
        int flag2 = 0;
        WhApvAllocationQueryCondition queryCondition = new WhApvAllocationQueryCondition();
        queryCondition.setAllocationNo(allocationOrderNo);
        List<WhApvAllocation> list = whApvAllocationService.queryApvAllocationDetailList(queryCondition, null);
        if (CollectionUtils.isNotEmpty(list)) {
            WhApvAllocation whApvAllocation = list.get(0);
            List<WhApvAllocationItem> itemlist = whApvAllocation.getAllocationItems();
            boolean flag = true;
            for (WhApvAllocationItem whApvAllocationItem : itemlist) {
                if (!whApvAllocationItem.getUpStatus().equals(AllocationUpStatusEnum.UPING.intCode())) {
                    flag = false;
                }
                if (whApvAllocationItem.getUpStatus().equals(AllocationUpStatusEnum.NOT_UP.intCode())) {
                    flag2 += 1;
                }
            }
            if (flag) {
                whApvAllocation.setAllocationStatus(AllocationStatusEnum.ALL_STORAGE.intCode());
                status = AllocationStatusEnum.ALL_STORAGE.intCode();
            } else {
                if (!(flag2 == itemlist.size())) {
                    whApvAllocation.setAllocationStatus(AllocationStatusEnum.SEGMENT_STORAGE.intCode());
                }
            }
        }
        ResponseJson responseJson = whApvAllocationService.updateAllocationStatusByUp(allocationOrderNo, status);
        if (responseJson.getStatus().equals(StatusCode.FAIL)) {
            throw new RuntimeException(responseJson.getMessage());
        }
        if (inId != null)
            CHECKINLOG.log(inId, "修改调拨单状态",
                    new String[][] { { "调拨单", allocationOrderNo }, { "修改状态", responseJson.getMessage() } });

    }

    /**
     * 库存调拨QC质检
     *
     * @return
     */
    @Override
    @StockServicelock
    public ResponseJson updateAndQcPass(List<String> skus, WhAllocationCheckIn whAllocationCheckIn, WhAllocationCheckInItem whAllocationCheckInItem,
                                        String uuidSku, Integer qcPacking) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        Integer inId = whAllocationCheckIn.getInId();
        Integer qcQuantity = whAllocationCheckInItem.getQcQuantity();
        Integer qcExceptionQuantity = whAllocationCheckInItem.getQcExceptionQuantity();
        // 如果QC良品数量为空，默认QC良品数量为0
        if (qcQuantity == null || qcQuantity <= 0) {
            qcQuantity = 0;
        }

        // 修改入库单状态
        WhAllocationCheckIn updateWhAllocationCheckIn = new WhAllocationCheckIn();
        updateWhAllocationCheckIn.setInId(whAllocationCheckIn.getInId());

        // 下一步[等待上架]
        updateWhAllocationCheckIn.setStatus(CheckInStatus.UPING.intCode());
        updateWhAllocationCheckIn.setQcUser(DataContextHolder.getUserId());// QC员
        updateWhAllocationCheckIn.setQcTime(new Timestamp(System.currentTimeMillis()));// QC时间
        updateWhAllocationCheckIn.setQcPacking(qcPacking);// QC是否装袋
        whAllocationCheckInService.updateWhAllocationCheckIn(updateWhAllocationCheckIn);

        WhAllocationCheckInItem updateWhAllocationCheckInItem = new WhAllocationCheckInItem();
        WhAllocationCheckInItem existItem = whAllocationCheckIn.getWhAllocationCheckInItem();
        if (whAllocationCheckInItem.getStockId()==null) {
            whAllocationCheckInItem.setStockId(existItem.getStockId());
        }
        updateWhAllocationCheckInItem.setItemId(existItem.getItemId());
        updateWhAllocationCheckInItem.setQcQuantity(qcQuantity);
        if (qcExceptionQuantity != null && qcExceptionQuantity > 0) {
            updateWhAllocationCheckInItem.setQcExceptionQuantity(qcExceptionQuantity);
            updateWhAllocationCheckInItem.setQcExceptionType(whAllocationCheckInItem.getExceptionType());

            // 调拨QC生成异常
            WhAllocationCheckInException checkInException = new WhAllocationCheckInException();
            checkInException.setUuId(UUID.randomUUID().toString());
            checkInException.setInId(inId);
            checkInException.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());
            checkInException.setSku(whAllocationCheckIn.getWhAllocationCheckInItem().getSku());
            checkInException.setExceptionFrom(ExceptionFrom.QC_EXCEPTION.intCode());
            checkInException.setExceptionType(whAllocationCheckInItem.getExceptionTypeStr());
            checkInException.setQuantity(qcExceptionQuantity);
            checkInException.setStatus(AllocationExceptionStatus.UNCONFIRM.intCode());
            Integer deliveryWarehouseId = WarehousePropertyEnum.HHD.intCode();
            Integer destWarehouseId = CacheUtils.getLocalWarehouseId();
            if (destWarehouseId.equals(WarehousePropertyEnum.HHD.intCode())) {
                deliveryWarehouseId = WarehousePropertyEnum.NN.intCode();
            }
            checkInException.setDeliveryWarehouseId(deliveryWarehouseId);
            checkInException.setDestWarehouseId(destWarehouseId);
            checkInException.setCreatedBy(DataContextHolder.getUserId());
            checkInException.setCreationDate(new Timestamp(System.currentTimeMillis()));
            whAllocationCheckInExceptionService.createWhAllocationCheckInException(checkInException);
        }
        whAllocationCheckInItemService.updateWhAllocationCheckInItem(updateWhAllocationCheckInItem);

        whAllocationCheckIn.setWhAllocationCheckInItem(whAllocationCheckInItem);
        if (!allocationUpdateStockService.updateAndQC(whAllocationCheckIn)) {
            throw new RuntimeException("调拨质检修改库存失败");
        }

        // 如果QC加工装袋选择与系统加工装袋属性不一致时，QC加工选择<系统加工属性时更改系统加工属性，反之则不改
        WhSkuQueryCondition query = new WhSkuQueryCondition();
        query.setSku(whAllocationCheckIn.getWhAllocationCheckInItem().getSku());
        WhSku dbWhSku = whSkuService.queryWhSku(query);
        if ("true".equals(dbWhSku.getSkuAlias()) && dbWhSku.getFloorLocation() == null) {
            dbWhSku.setFloorLocation(ProcessType.GENERAL_PROCESSING.intCode());
        }
        if (qcPacking != null && dbWhSku.getFloorLocation() != null && qcPacking < dbWhSku.getFloorLocation()) {
            WhSku whSku = new WhSku();
            whSku.setId(dbWhSku.getId());
            whSku.setFloorLocation(qcPacking);
            whSku.setGoodsQuantity(dbWhSku.getGoodsQuantityAdd());
            if (qcPacking == 0) {
                whSku.setSkuAlias("false");
                // 修改加工装袋推送消息至产品系统
                ProductSkuMessage productSkuMessage = new ProductSkuMessage();
                productSkuMessage.setSku(dbWhSku.getSku());
                productSkuMessage.setProcessBag(false);
                AmqMessage amqMessage = AssembleMessageDataUtils.assembleSkuData(productSkuMessage);
                amqMessageService.createAmqMessage(amqMessage);
            }
            whSkuService.updateWhSku(whSku);

            SystemLogUtils.SKULOG.log(whSku.getId(),
                    "调拨QC 加工装袋修改为 " + ProcessType.getNameByCode(qcPacking.toString()));
        }

        String sku = CompatibleSkuUtils.getSku(uuidSku);
        String[][] appInfo = new String[][]{{"SKU", sku},{"uuid",uuidSku}};
        CHECKINLOG.log(inId, CheckInLogType.QC_PASS.getName(), appInfo);

        logger.info(
                "whAllocationCheckIn pass: inId[" + inId + "], boxNo[" + whAllocationCheckIn.getBoxNo() + "]");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 提货
     *
     * @return
     */
    @Override
    @StockServicelock
    public ResponseJson updateAndUpIng(WhAllocationCheckIn whAllocationCheckIn) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        Integer oldStstus = whAllocationCheckIn.getStatus();
        Integer obtainUser = whAllocationCheckIn.getObtainUser();
        whAllocationCheckIn.setStatus(CheckInStatus.UPING.intCode());
        whAllocationCheckIn.setObtainUser(obtainUser);// 提货员
        whAllocationCheckIn.setObtainTime(new Timestamp(System.currentTimeMillis()));// 提货时间
        SaleUser saleUser = saleUserService.getSaleUser(obtainUser);
        String obtainUserStr = "";
        if (saleUser != null) {
            obtainUserStr = saleUser.getName();
        }
        whAllocationCheckInService.updateWhAllocationCheckIn(whAllocationCheckIn);
        if (CheckInStatus.WAITING_UP.intCode().equals(oldStstus)) {
            if (!allocationUpdateStockService.updateAndPick(whAllocationCheckIn)) {
                throw new RuntimeException("调拨提货修改库存失败");
            }
        }
        CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.UPING.getName(),
                new String[][]{{"提货员", obtainUserStr}});
        logger.info("提货扫描-上架中: inId[" + whAllocationCheckIn.getInId() + "], obtainUser[" + obtainUserStr + "]");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 上架
     *
     * @param whAllocationCheckIn
     * @return
     */
    @Override
    @StockServicelock
    public ResponseJson updateAndUp(List<String> skus, WhAllocationCheckIn whAllocationCheckIn, AndroidProductDo domain) {

        Integer quantity = domain.getQuantity();
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        if (quantity == null || quantity < 0) {
            response.setMessage("请填写上架数量");
            return response;
        }
        if (StringUtils.isBlank(whAllocationCheckIn.getAllocationOrderNo())) {
            response.setMessage("调拨单号为空");
            return response;
        }

        WhAllocationCheckInItem whAllocationCheckInItem = whAllocationCheckIn.getWhAllocationCheckInItem();
        // 先安排库位
        if (StringUtils.isNotBlank(domain.getLocation())
                && StringUtils.isBlank(whAllocationCheckInItem.getLocation())) {
            ResponseJson moveResponse = locationMoveInfoService.pcMoveLocationBySkuFromPDA(
                    whAllocationCheckIn.getInId(), whAllocationCheckInItem.getSku(),
                    whAllocationCheckInItem.getStockId(), domain.getLocation());
            if (moveResponse.getStatus().equals(StatusCode.FAIL)) {
                return moveResponse;
            }
        }

        if (CheckInStatus.CONFIRMED.intCode().equals(whAllocationCheckIn.getStatus())) {
            response.setMessage("重复操作,入库单已上架");
            return response;
        }
        if (!(CheckInStatus.UPING.intCode().equals(whAllocationCheckIn.getStatus()) || CheckInStatus.UPERROR.intCode().equals(whAllocationCheckIn.getStatus()))) {
            response.setMessage("调拨入库单状态不对");
            return response;
        }
        Integer countQuantity = quantity;
        Integer qcQuantity = whAllocationCheckInItem.getQcQuantity() == null
                ? whAllocationCheckInItem.getQuantity() : whAllocationCheckInItem.getQcQuantity();
        if (countQuantity > qcQuantity) {
            response.setMessage("上架数量大于入库数量");
            return response;
        }
        // 装箱数量
        Integer boxQuantity =Optional.ofNullable(whAllocationCheckInItem.getBoxQuantity()).orElse(0);

        // 修改调拨单上架信息
        WhAllocationCheckInQueryCondition query = new WhAllocationCheckInQueryCondition();
        query.setAllocationOrderNo(whAllocationCheckIn.getAllocationOrderNo());
        query.setSku(whAllocationCheckInItem.getSku());
        List<WhAllocationCheckIn> whAllocationCheckInList = whAllocationCheckInDao
                .queryWhAllocationCheckInList(query, null);
        Integer allotSurplusQuantity = quantity;
        for (WhAllocationCheckIn dbWhAllocationCheckIn : whAllocationCheckInList) {
            allotSurplusQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem()
                    .getUpQuantity() == null ? 0
                    : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getUpQuantity();
            countQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem().getUpQuantity() == null ? 0
                    : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getUpQuantity();
            countQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem()
                    .getQcExceptionQuantity() == null ? 0
                    : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getQcExceptionQuantity();
            countQuantity += dbWhAllocationCheckIn.getWhAllocationCheckInItem()
                    .getExceptionQuantity() == null ? 0
                    : dbWhAllocationCheckIn.getWhAllocationCheckInItem().getExceptionQuantity();
        }

        //更新调拨单状态和上架数量
        WhApvAllocationItemQueryCondition queryCondition = new WhApvAllocationItemQueryCondition();
        queryCondition.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());
        queryCondition.setUpNum(allotSurplusQuantity);
        queryCondition.setSku(whAllocationCheckInItem.getSku());
        queryCondition.setCountQuantity(countQuantity);
        queryCondition.setBoxQuantity(boxQuantity);
        queryCondition.setUpQty(quantity);
        updateAllocationAndItemQtyStatus(queryCondition, whAllocationCheckIn.getInId());

        WhAllocationCheckInItem updateItem = new WhAllocationCheckInItem();
        updateItem.setItemId(whAllocationCheckInItem.getItemId());
        updateItem.setSku(whAllocationCheckInItem.getSku());
        updateItem.setStockId(whAllocationCheckInItem.getStockId());
        updateItem.setUpQuantity(quantity);
        if (whAllocationCheckInItemService.updateWhAllocationCheckInItem(updateItem) < 1) {
            // 更新上架数量
            throw new RuntimeException("更新上架数量失败");
        }

        whAllocationCheckIn.setStatus(CheckInStatus.CONFIRMED.intCode());
        whAllocationCheckIn.setUpUser(DataContextHolder.getUserId());
        whAllocationCheckIn.setUpTime(new Timestamp(System.currentTimeMillis()));

        if (updateWhAllocationCheckIn(whAllocationCheckIn) < 1) {
            throw new RuntimeException("更新入库单状态失败");
        }
        whAllocationCheckIn.setWhAllocationCheckInItem(updateItem);

        // TODO 上架操作库存
        if (!allocationUpdateStockService.updateAndUp(whAllocationCheckIn)) {
            throw new RuntimeException("上架加库存失败");
        }

        WhApvAllocationItemQueryCondition assignmentQueryCondition =  new WhApvAllocationItemQueryCondition();
        assignmentQueryCondition.setAllocationNo(whAllocationCheckIn.getAllocationOrderNo());
        assignmentQueryCondition.setSku(whAllocationCheckInItem.getSku());
        List<WhApvAllocationItem> whApvAllocationItems = whApvAllocationService.queryWhApvAllocationItemList(assignmentQueryCondition);

        if (CollectionUtils.isNotEmpty(whApvAllocationItems)) {
            WhApvAllocationItem apvAllocationItem = whApvAllocationItems.get(0);
            WhStockQueryCondition stockQuery = new WhStockQueryCondition();
            stockQuery.setSku(whAllocationCheckInItem.getSku());
            stockQuery.setId(whAllocationCheckInItem.getStockId());
            WhStock stock = whStockService.queryWhStock(stockQuery);

            // 创建保质期批次并绑定唯一码
            createExpBatchAndBindUuid(apvAllocationItem, whAllocationCheckIn, stock);

            // 售后结算
            if (apvAllocationItem.getAfterSaleAndExpInfo() != null
                    && StringUtils.isNotBlank(apvAllocationItem.getAfterSaleAndExpInfo().getVendorCode())
                    && apvAllocationItem.getAfterSaleAndExpInfo().getAfterSaleQty() != null
                    && apvAllocationItem.getAfterSaleAndExpInfo().getAfterSaleQty() > 0) {
                WhCheckIn checkIn = new WhCheckIn();
                checkIn.setVendorName(apvAllocationItem.getAfterSaleAndExpInfo().getVendorName());
                checkIn.setSupplierId(apvAllocationItem.getAfterSaleAndExpInfo().getVendorCode());
                checkIn.setInId(whAllocationCheckIn.getInId());
                checkIn.setPurchaseOrderNo(whAllocationCheckIn.getAllocationOrderNo());
                Integer afterSaleQty = Math.min(allotSurplusQuantity, apvAllocationItem.getAfterSaleAndExpInfo().getAfterSaleQty());
                checkIn.setAfterSaleQty(afterSaleQty);
                checkIn.setWhCheckInItem(new WhCheckInItem());
                checkInUpdateStockService.createAfterSaleItem(checkIn, stock, updateItem.getUpQuantity(),
                        CheckInType.TRANSFER.intCode(), null);
            }
        }
        
        CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.CONFIRMED.getName(),
                new String[][]{{"SKU", whAllocationCheckInItem.getSku()},
                        {"数量", quantity.toString()}});
        logger.info("PDA上架: inId[" + whAllocationCheckIn.getInId() + "], boxNo["
                + whAllocationCheckIn.getBoxNo() + "], allocationOrderNo["
                + whAllocationCheckIn.getAllocationOrderNo() + "], sku["
                + whAllocationCheckInItem.getSku() + "], quantity[" + quantity + "]");

        try {
            whUniqueSkuService.updateWhUniqueSKuList(domain.getInId(),domain.getCheckInType());
        } catch (Exception e) {
            logger.error("调拨上架唯一码添加日志失败！"+e.getMessage());
        }
        
        //同步上架数量和状态到发货仓
        AllocationPushUtil.pushAllocationAndItemQtyStatus(queryCondition);

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    public void updateAllocationAndItemQtyStatus(WhApvAllocationItemQueryCondition queryCondition, Integer inId) {
        boolean itemAllUp = false;

        if (queryCondition.getCountQuantity() != null && queryCondition.getBoxQuantity() != null
                && queryCondition.getCountQuantity().equals(queryCondition.getBoxQuantity())) {
            // 已上架
            queryCondition.setUpStatus(AllocationUpStatusEnum.UPING.intCode());
            itemAllUp = true;
        }
        else {
            // 部分上架
            queryCondition.setUpStatus(AllocationUpStatusEnum.SEGMENT_UP.intCode());
        }
        ResponseJson responseJson = whApvAllocationService.updateApvAllocationByUp(queryCondition);
        if (responseJson.getStatus().equals(StatusCode.FAIL)) {
            if (StringUtils.isNotBlank(responseJson.getMessage())) {
                throw new RuntimeException(responseJson.getMessage());
            }
            else {
                throw new RuntimeException("上架更新调拨单明细失败");
            }
        }

        // 修改调拨单状态
        updateAllocationStatus(itemAllUp, queryCondition.getAllocationNo(), queryCondition.getSku(), inId);
    }

    /**
     * 创建保质期批次并绑定唯一码
     * @param checkin
     */
    public void createExpBatchAndBindUuid(WhApvAllocationItem allocationItem,WhAllocationCheckIn checkin,WhStock stock) {
        if (checkin == null || checkin.getWhAllocationCheckInItem() == null || stock ==null)
            return;
        
        if (allocationItem == null || StringUtils.isBlank(allocationItem.getJsonStr())
                || allocationItem.getAfterSaleAndExpInfo() == null
                || StringUtils.isBlank(allocationItem.getAfterSaleAndExpInfo().getExpDate())
                || StringUtils.isBlank(allocationItem.getAfterSaleAndExpInfo().getProDate())
                || allocationItem.getAfterSaleAndExpInfo().getDays() == null)
            return;
        ExpManageItemQueryCondition query = new ExpManageItemQueryCondition();
        query.setRelationId(checkin.getInId());
        query.setType(DrpTurnoverOderType.ALLOCATION_IN.intCode());
        List<ExpManageItem> existItemList = expManageItemService.queryExpManageItems(query, null);
        if (CollectionUtils.isNotEmpty(existItemList))
            return;
        
        
        // 根据入库单ID和SKU查询唯一码保质期批次关联表
        UniqueSkuExpRelationQueryCondition queryCondition = new UniqueSkuExpRelationQueryCondition();
        queryCondition.setRelationId(checkin.getInId());
        queryCondition.setSku(checkin.getWhAllocationCheckInItem().getSku());
        List<UniqueSkuExpRelation> relationList = uniqueSkuExpRelationService.queryUniqueSkuExpRelations(queryCondition,
                null);
        String batchNo = CreateTaskNoUtils.createBatNo("EXP", "expManage");
        Timestamp proDate = null;
        Timestamp expDate = null;
        Integer days = null;
        List<UniqueSkuExpRelation> updateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relationList)) {
            proDate = relationList.get(0).getProDate();
            expDate = relationList.get(0).getExpDate();
            days = relationList.get(0).getDays();

            relationList.forEach(expRelation -> {
                UniqueSkuExpRelation skuExpRelation = new UniqueSkuExpRelation();
                skuExpRelation.setId(expRelation.getId());
                skuExpRelation.setExpNo(batchNo);
                updateList.add(skuExpRelation);
            });
        }

        // 创建保质期批次
        ExpManage expManage = new ExpManage();
        expManage.setBatchNo(batchNo);
        expManage.setStockId(stock.getId());
        expManage.setCheckInType(CheckInWhType.LOCAL.intCode());
        expManage.setSource(DrpTurnoverOderType.ALLOCATION_IN.intCode());
        expManage.setSku(checkin.getWhAllocationCheckInItem().getSku());
        expManage.setDays(days);
        expManage.setProDate(proDate);
        expManage.setExpDate(expDate);
        expManage.setCreateBy(checkin.getUpUser());
        expManage.setCreationDate(checkin.getUpTime());
        expManage.setAllocationInQuantity(checkin.getWhAllocationCheckInItem().getUpQuantity());
        expManage.setQuantity(checkin.getWhAllocationCheckInItem().getUpQuantity());
        expManageService.createExpManage(expManage);
        SystemLogUtils.EXP_MANAGE_LOG.log(expManage.getId(), "创建保质期批次");

        // 批次明细
        ExpManageItem manageItem = new ExpManageItem();
        manageItem.setBatchNo(expManage.getBatchNo());
        manageItem.setType(DrpTurnoverOderType.ALLOCATION_IN.intCode());
        manageItem.setQuantity(checkin.getWhAllocationCheckInItem().getUpQuantity());
        manageItem.setRelationId(checkin.getInId());
        manageItem.setCreationDate(checkin.getUpTime());
        expManageItemService.createExpManageItem(manageItem);

        // 唯一码关联
        uniqueSkuExpRelationService.batchUpdateUniqueSkuExpRelation(updateList);

        //修改库位属性
        WhStock updateStock = new WhStock();
        updateStock.setId(stock.getId());
        stock.addLocationTag(LocationTagEnum.SHELF_LIFE);
        updateStock.setLocationTag(stock.getLocationTag());
        whStockService.updateWhStock(updateStock);

    }
    /**
     * 废弃
     *
     * @param skus
     * @param whAllocationCheckIn
     * @param reason
     * @return
     */
    @Override
    @StockServicelock
    public ResponseJson updateDiscarded(List<String> skus, WhAllocationCheckIn whAllocationCheckIn, String reason) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        Integer inId = whAllocationCheckIn.getInId();
        Integer status = whAllocationCheckIn.getStatus();
        if (status >= CheckInStatus.CONFIRMED.intCode()) {
            logger.info("已上架(或已废弃)的入库单不能废弃: inId[" + inId + "],  allocationOrderNo["
                    + whAllocationCheckIn.getAllocationOrderNo() + "], status[" + status + "]");
        }
        WhAllocationCheckIn updateCheckIn = new WhAllocationCheckIn();
        updateCheckIn.setInId(whAllocationCheckIn.getInId());
        updateCheckIn.setStatus(CheckInStatus.DISCARDED.intCode());
        String comment = whAllocationCheckIn.getComment();
        if (StringUtils.isNotBlank(reason)) {
            if (StringUtils.isNotBlank(comment)) {
                updateCheckIn.setComment(comment + "<br/>废弃原因: " + reason);
            } else {
                updateCheckIn.setComment("废弃原因: " + reason);
            }
        }

        if (updateWhAllocationCheckIn(updateCheckIn) < 1) {
            throw new RuntimeException("更新入库单状态失败");
        }

        if (StringUtils.isNotBlank(whAllocationCheckIn.getBoxNo())) {
            String boxNo = whAllocationCheckIn.getBoxNo();
            String[][] logs = new String[][]{{"废弃入库单", ""},
                    {"relationNo", whAllocationCheckIn.getInId().toString()}};
            int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
            if (updated >= 1) {
                logger.info("废弃入库单后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
            } else {
                throw new RuntimeException("解绑周转码失败");
            }
        }

        if (!allocationUpdateStockService.updateAndDiscard(whAllocationCheckIn)) {
            throw new RuntimeException("废弃修改库存失败");
        }

        // 废弃唯一码
        whUniqueSkuService.scrapUniqueSku(whAllocationCheckIn.getInId());

        //废弃调拨异常单
        WhAllocationCheckInExceptionQueryCondition queryCondition = new WhAllocationCheckInExceptionQueryCondition();
        queryCondition.setInId(whAllocationCheckIn.getInId());
        queryCondition.setStatus(AllocationExceptionStatus.UNCONFIRM.intCode());
        List<WhAllocationCheckInException> whAllocationCheckInExceptions = whAllocationCheckInExceptionService.queryWhAllocationCheckInExceptions(queryCondition, null);
        List<WhAllocationCheckInException> updateExceptions =  new ArrayList<>();
        for (WhAllocationCheckInException whAllocationCheckInException : whAllocationCheckInExceptions) {
            WhAllocationCheckInException checkInException=new WhAllocationCheckInException();
            checkInException.setId(whAllocationCheckInException.getId());
            checkInException.setStatus(AllocationExceptionStatus.DISCARDED.intCode());
            checkInException.setDiscardedUser(DataContextHolder.getUserId() );
            checkInException.setDiscardedDate(new Timestamp(System.currentTimeMillis()));
            updateExceptions.add(checkInException);
        }
        if (CollectionUtils.isNotEmpty(updateExceptions)) {
            whAllocationCheckInExceptionService.batchUpdateWhAllocationCheckInException(updateExceptions);
        }

        if (StringUtils.isNotBlank(reason)) {
            CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.DISCARDED.getName(), new String[][]{
                    {"status", CheckInStatus.DISCARDED.intCode().toString()}, {"reason", reason}});
        } else {
            CHECKINLOG.log(whAllocationCheckIn.getInId(), CheckInLogType.DISCARDED.getName(),
                    new String[][]{{"status", CheckInStatus.DISCARDED.intCode().toString()}});
        }
        logger.info("批量废弃入库单: inId[" + inId + "], boxNo[" + whAllocationCheckIn.getBoxNo() + "], status["
                + status + "], reason[" + reason + "]");

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }
}