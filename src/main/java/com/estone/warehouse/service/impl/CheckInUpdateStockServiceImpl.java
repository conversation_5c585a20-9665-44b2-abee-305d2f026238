package com.estone.warehouse.service.impl;

import java.sql.Timestamp;
import java.util.*;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.estone.checkin.bean.WhCheckIn;
import com.estone.checkin.bean.WhCheckInItem;
import com.estone.checkin.enums.CheckInFlags;
import com.estone.checkin.enums.CheckInStatus;
import com.estone.checkin.enums.CheckInType;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.checkin.service.WhCheckInItemService;
import com.estone.checkin.service.WhCheckInService;
import com.estone.common.util.BeanConvertUtils;
import com.estone.foreign.service.OrderRequestService;
import com.estone.multiplelocation.enums.AllocatePhaseEnum;
import com.estone.multiplelocation.enums.HandleResultEnum;
import com.estone.sku.bean.AfterSaleSettlement;
import com.estone.sku.bean.AfterSaleSettlementQueryCondition;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.AfterSaleSettlementService;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.utils.AfterSaleUtils;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.transfer.bean.TransferStock;
import com.estone.transfer.bean.WhFbaPurchaseData;
import com.estone.transfer.bean.WhFbaPurchaseDataQueryCondition;
import com.estone.transfer.service.CheckInUpdateTransferStockService;
import com.estone.transfer.service.WhFbaPurchaseDataService;
import com.estone.warehouse.bean.WhSaleStockLog;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockLog;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.*;
import com.estone.warehouse.service.*;
import com.estone.warehouse.util.FrozenStockUtils;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. Project Name:wms Package
 * Name:com.estone.warehouse.service.impl File
 * Name:CheckInUpdateStockServiceImpl.java Description: 采购入库流程库存变动实现类
 * Author:Yimeil Date:2019-12-04 16:26
 * ---------------------------------------------------------------------------
 */
@Slf4j
@Service("checkInUpdateStockService")
public class CheckInUpdateStockServiceImpl implements CheckInUpdateStockService {

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhStockLogService whStockLogService;

    @Resource
    private WhSaleStockLogService whSaleStockLogService;
    
    @Resource
    private CheckInUpdateTransferStockService checkInUpdateTransferStockService;

    @Resource
    private WhFbaPurchaseDataService whFbaPurchaseDataService;

    @Resource
    private WhStockChangeRecordService whStockChangeRecordService;

    @Resource
    private AfterSaleSettlementService afterSaleSettlementService;
    
    @Resource
    private OrderRequestService orderRequestService;
    @Resource
    private WhSkuService whSkuService;
    @Resource
    private WhCheckInService whCheckInService;

    @Resource
    private WhCheckInItemService whCheckInItemService;

    @Override
    public boolean batchUpdateStockByCheckIn(WhCheckIn whCheckIn) {

        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null || StringUtils.isBlank(
                whCheckIn.getWhCheckInItem().getSku())) {
            return false;
        }
        //根据匹配库位匹配库存
        WhStock stock = getStock(whCheckIn);

        if (whCheckIn.getExceptionType() != null && whCheckIn.getExceptionType().equals(CheckInWhType.FBA.intCode())) {
            // 走中转仓库存
            return checkInUpdateTransferStockService.batchUpdateStockByCheckIn(whCheckIn, stock);
        }
        else {

            try {
                Boolean isFreeCheck = whCheckIn.getWhCheckInItem().getIsFreeCheck();

                // 需要更新的库存
                List<WhStock> updateList = new ArrayList<WhStock>();
    
                List<WhStockLog> whStockLogList = new ArrayList<WhStockLog>();
                Integer skuQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                        : whCheckIn.getWhCheckInItem().getQuantity();

                if (null != stock) {
                    Integer originalWaitQcQuantity = stock.getQcQuantity() == null ? 0 : stock.getQcQuantity();
                    Integer upQuantity = Optional.ofNullable(stock.getUpQuantity()).orElse(0);

                    WhStock updateStock = new WhStock();
                    updateStock.setId(stock.getId());
                    updateStock.setSku(whCheckIn.getWhCheckInItem().getSku());
                    updateStock.setLocationNumber(whCheckIn.getLocationNumber());

                    if (isFreeCheck){
                        updateStock.setUpQuantity(upQuantity + skuQuantity);
                        whStockLogList.add(new WhStockLog(whCheckIn.getWhCheckInItem().getSku(), StockLogType.UPING, stock.getId(), stock.getLocationNumber(),
                                StockLogStep.PURCHASE_CHECK_IN, skuQuantity, upQuantity, whCheckIn.getInId() + ""));
                    }else{
                        updateStock.setQcQuantity(originalWaitQcQuantity + skuQuantity);
                        whStockLogList.add(new WhStockLog(whCheckIn.getWhCheckInItem().getSku(), StockLogType.WAITING_QC, stock.getId(), stock.getLocationNumber(),
                                StockLogStep.PURCHASE_CHECK_IN, skuQuantity, originalWaitQcQuantity, whCheckIn.getInId() + ""));
                    }

                    updateStock.setLastUpdatedBy(DataContextHolder.getUserId());
                    updateStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                    updateList.add(updateStock);
                }
                else {// 入库时没有对应的sku库存
                    stock = new WhStock();
                    stock.setSku(whCheckIn.getWhCheckInItem().getSku());

                    if (isFreeCheck){
                        stock.setUpQuantity(skuQuantity);
                    }else{
                        stock.setQcQuantity(skuQuantity);
                    }
                    stock.setLocationNumber(whCheckIn.getLocationNumber());
                    stock.setLastUpdatedBy(DataContextHolder.getUserId());
                    stock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                    whStockService.createWhStock(stock);

                    if (isFreeCheck){
                        whStockLogList.add(new WhStockLog(whCheckIn.getWhCheckInItem().getSku(), StockLogType.UPING, stock.getId(), stock.getLocationNumber(),
                                StockLogStep.PURCHASE_CHECK_IN, skuQuantity, 0, whCheckIn.getInId() + ""));
                    }else{
                        whStockLogList.add(new WhStockLog(whCheckIn.getWhCheckInItem().getSku(), StockLogType.WAITING_QC, stock.getId(), stock.getLocationNumber(),
                                StockLogStep.PURCHASE_CHECK_IN, skuQuantity, 0, whCheckIn.getInId() + ""));
                    }

                }
                // 增加sku库位
                WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
                whSkuQueryCondition.setSku(stock.getSku());
                WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
                if (whSku != null && StringUtils.isNotEmpty(whCheckIn.getLocationNumber())) {
                    WhSku updateSku = new WhSku();
                    updateSku.setId(whSku.getId());
                    String skuLocation = whSku.addLocationNumber(whCheckIn.getLocationNumber());
                    if (StringUtils.isNotEmpty(stock.getLocationNumber())
                            && whSku.matchLocationNumber(stock.getLocationNumber()))
                        skuLocation = whSku.replaceLocationNumber(whCheckIn.getLocationNumber(), stock.getLocationNumber());
                    updateSku.setLocationNumber(skuLocation);
                    whSkuService.updateWhSku(updateSku);
                }
                // 添加库存变动日志
                if (CollectionUtils.isNotEmpty(whStockLogList)) {
                    whStockLogService.batchAddWhStockLog(whStockLogList);
                }
                whStockService.batchUpdateWhStock(updateList);
                //记录库存ID
                WhCheckInItem whCheckInItem = new WhCheckInItem();
                whCheckInItem.setItemId(whCheckIn.getWhCheckInItem().getItemId());
                whCheckInItem.setInId(whCheckIn.getInId());
                whCheckInItem.setSkuId(stock.getId());
                whCheckInItemService.updateWhCheckInItem(whCheckInItem);
                return true;
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * 根据匹配库位匹配库存
     * @param whCheckIn
     * @return
     */
    private WhStock getStock(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())) {
            return null;
        }
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSku(whCheckIn.getWhCheckInItem().getSku());
        query.setQueryLocationType(true);
        List<WhStock> stocks = whStockService.queryWhStocks(query, null);
        // 仅当库存列表不为空时继续处理
        if (CollectionUtils.isEmpty(stocks))
            return null;
        String sku = whCheckIn.getWhCheckInItem().getSku();
        String locationNumber = whCheckIn.getLocationNumber();
        if (stocks.stream()
                .anyMatch(s -> Objects.equals(s.getLocationNumber(), locationNumber)
                        || Objects.equals(s.getLocationType(), LocationType.VIRTUAL.intCode())
                        || StringUtils.isBlank(s.getLocationNumber()))) {
            Optional<WhStock> matchOpt = stocks.stream()
                    .filter(s -> Objects.equals(s.getLocationNumber(), locationNumber)).findFirst();
            if (matchOpt.isPresent())
                return matchOpt.get();

            Optional<WhStock> matchOpt2 = stocks.stream()
                    .filter(s -> Objects.equals(s.getLocationType(), LocationType.VIRTUAL.intCode())
                            || StringUtils.isBlank(s.getLocationNumber()))
                    .findFirst();
            if (matchOpt2.isPresent())
                return matchOpt2.get();

        }
        // 条件 2: 查找第一个与 check-in 库位号匹配的库存 (忽略大小写)
        Optional<WhStock> stockByLocationOpt = stocks.stream()
                .filter(s -> StringUtils.equalsIgnoreCase(s.getLocationNumber(), locationNumber)).findFirst();
        if (stockByLocationOpt.isPresent())
            return stockByLocationOpt.get(); // 找到匹配库位的库存
        // 按 SKU 和库位号精确匹配查找
        Optional<WhStock> stockBySkuAndLocOpt = stocks.stream()
                .filter(s -> Objects.equals(s.getSku(), sku) && Objects.equals(s.getLocationNumber(), locationNumber))
                .findFirst();
        return stockBySkuAndLocOpt.orElse(null); // 获取找到的库存，否则为 null
    }

    @Override
    public boolean batchUpdateStockByQc(WhCheckIn whCheckIn) {

        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())
                || whCheckIn.getWhCheckInItem().getSkuId() == null) {
            return false;
        }
        if (whCheckIn.getExceptionType() != null && whCheckIn.getExceptionType().equals(CheckInWhType.FBA.intCode())) {
            // 走中转仓库存
            return checkInUpdateTransferStockService.batchUpdateStockByQc(whCheckIn);
        }
        else {
            
            try {
    
                WhStockQueryCondition query = new WhStockQueryCondition();
                query.setSku(whCheckIn.getWhCheckInItem().getSku());
                query.setId(whCheckIn.getWhCheckInItem().getSkuId());
                WhStock whStock = whStockService.queryWhStock(query);
                if (whStock == null) {
                    // 入库时没有对应的sku库存
                    return false;
                }
    
                // 需要更新的库存
                List<WhStock> updateList = new ArrayList<WhStock>();
                List<WhStockLog> whStockLogList = new ArrayList<WhStockLog>();
                Integer skuQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                        : whCheckIn.getWhCheckInItem().getQcQuantity();
                Integer checkInQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                        : whCheckIn.getWhCheckInItem().getQuantity();
    
                Integer originalWaitingUpQuantity = whStock.getWaitingUpQuantity() == null ? 0
                        : whStock.getWaitingUpQuantity();
                Integer originalUpingQuantity = whStock.getUpQuantity() == null ? 0 : whStock.getUpQuantity();
                Integer originalWaitingQcQuantity = whStock.getQcQuantity() == null ? 0 : whStock.getQcQuantity();
    
                if (originalWaitingQcQuantity - checkInQuantity < 0) {
                    return false;
                }
    
                WhStock updateStock = new WhStock();
                updateStock.setId(whStock.getId());
                updateStock.setSku(whCheckIn.getWhCheckInItem().getSku());
                if (skuQuantity > 0 && CheckInStatus.UPING.intCode().equals(whCheckIn.getStatus())) {
                    updateStock.setUpQuantity(originalUpingQuantity + skuQuantity);
                    whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING, whStock.getId(),
                            whStock.getLocationNumber(), StockLogStep.PURCHASE_QC, skuQuantity, originalUpingQuantity,
                            whCheckIn.getInId() + ""));
                }
                else if (skuQuantity > 0 && CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())) {

                    updateStock.setWaitingUpQuantity(originalWaitingUpQuantity + skuQuantity);
                    whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.WAITING_UP, whStock.getId(),
                            whStock.getLocationNumber(), StockLogStep.PURCHASE_QC, skuQuantity,
                            originalWaitingUpQuantity, whCheckIn.getInId() + ""));
                }
                updateStock.setQcQuantity(originalWaitingQcQuantity - checkInQuantity);
                updateStock.setLastUpdatedBy(DataContextHolder.getUserId());
                updateStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                updateList.add(updateStock);

                whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.WAITING_QC, whStock.getId(),
                        whStock.getLocationNumber(), StockLogStep.PURCHASE_QC, -checkInQuantity,
                        originalWaitingQcQuantity, whCheckIn.getInId() + ""));

                if (CollectionUtils.isEmpty(updateList)) {
                    return false;
                }
                // 添加库存变动日志
                if (CollectionUtils.isNotEmpty(whStockLogList)) {
                    whStockLogService.batchAddWhStockLog(whStockLogList);
                }
    
                whStockService.batchUpdateWhStock(updateList);
    
                return true;
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public boolean batchUpdateStockByUping(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())
                || whCheckIn.getWhCheckInItem().getSkuId() == null) {
            return false;
        }
        if (whCheckIn.getExceptionType() != null && whCheckIn.getExceptionType().equals(CheckInWhType.FBA.intCode())) {
            // 走中转仓库存
            return checkInUpdateTransferStockService.batchUpdateStockByUping(whCheckIn);
        }
        else {

            try {
    
                WhStockQueryCondition query = new WhStockQueryCondition();
                query.setSku(whCheckIn.getWhCheckInItem().getSku());
                query.setId(whCheckIn.getWhCheckInItem().getSkuId());
                WhStock whStock = whStockService.queryWhStock(query);
                if (whStock == null) {
                    // 入库时没有对应的sku库存
                    return false;
                }
    
                // 需要更新的库存
                List<WhStock> updateList = new ArrayList<WhStock>();
                List<WhStockLog> whStockLogList = new ArrayList<WhStockLog>();
                Integer skuQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                        : whCheckIn.getWhCheckInItem().getQcQuantity();
                Integer originalUpQuantity = whStock.getUpQuantity() == null ? 0 : whStock.getUpQuantity();
                Integer originalWaitingUpQuantity = whStock.getWaitingUpQuantity() == null ? 0
                        : whStock.getWaitingUpQuantity();
                if (originalWaitingUpQuantity - skuQuantity < 0) {
                    return false;
                }
                WhStock updateRecord = new WhStock();
                updateRecord.setId(whStock.getId());
                updateRecord.setSku(whCheckIn.getWhCheckInItem().getSku());
                updateRecord.setWaitingUpQuantity(originalWaitingUpQuantity - skuQuantity);
                updateRecord.setUpQuantity(originalUpQuantity + skuQuantity);
                updateRecord.setLastUpdatedBy(DataContextHolder.getUserId());
                updateRecord.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                updateList.add(updateRecord);

                whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.WAITING_UP, whStock.getId(),
                        whStock.getLocationNumber(), StockLogStep.PURCHASE_PICK_UP, -skuQuantity,
                        originalWaitingUpQuantity, whCheckIn.getInId() + ""));

                whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING, whStock.getId(),
                        whStock.getLocationNumber(), StockLogStep.PURCHASE_PICK_UP, skuQuantity, originalUpQuantity,
                        whCheckIn.getInId() + ""));

                if (CollectionUtils.isEmpty(updateList)) {
                    return false;
                }
                // 添加库存变动日志
                if (CollectionUtils.isNotEmpty(whStockLogList)) {
                    whStockLogService.batchAddWhStockLog(whStockLogList);
                }
    
                whStockService.batchUpdateWhStock(updateList);
    
                return true;
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public boolean batchUpdateStockByUp(WhCheckIn whCheckIn) {

        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())
                || StringUtils.isEmpty(whCheckIn.getLocationNumber())
                        || whCheckIn.getWhCheckInItem().getSkuId() == null) {
            return false;
        }
        // 直发入库走本地仓
        if (whCheckIn.getWhCheckInItem().zfOrder()) {
            try {
                return batchUpdateLocalStockByUp(whCheckIn);
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }

        if (whCheckIn.getExceptionType() != null && whCheckIn.getExceptionType().equals(CheckInWhType.FBA.intCode())) {
            // 走FBA库存
            return checkInUpdateTransferStockService.batchUpdateStockByUp(whCheckIn);
        }
        else {
            // 走中转仓，有可能包含本地仓
            WhFbaPurchaseDataQueryCondition queryCondition = new WhFbaPurchaseDataQueryCondition();
            queryCondition.setPurchaseorderno(whCheckIn.getPurchaseOrderNo());
            queryCondition.setSku(whCheckIn.getWhCheckInItem().getSku());
            List<WhFbaPurchaseData> whFbaPurchaseDataList = whFbaPurchaseDataService
                    .queryWhFbaPurchaseDatas(queryCondition, null);
            // 走混合仓
            if (CollectionUtils.isNotEmpty(whFbaPurchaseDataList)) {
                whCheckIn.setBlend(true);
                return checkInUpdateTransferStockService.batchUpdateStockByUp(whCheckIn, whFbaPurchaseDataList);
            }
            else {
                // 走本地仓
                try {
                    return batchUpdateLocalStockByUp(whCheckIn);
                }
                catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
                }
            }
        }
    }

    /**
     * 本地仓上架
     * 
     * @param whCheckIn
     * @return
     */
    private boolean batchUpdateLocalStockByUp(WhCheckIn whCheckIn) {
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSku(whCheckIn.getWhCheckInItem().getSku());
        query.setLocationNumber(whCheckIn.getLocationNumber());
        WhStock upStock = whStockService.queryWhStock(query);
        WhStock whStock = BeanConvertUtils.convert(upStock, WhStock.class);
        if (whCheckIn.isPreStoreUp()) {
            query.setLocationNumber(null);
            query.setId(whCheckIn.getWhCheckInItem().getSkuId());
            whStock = whStockService.queryWhStock(query);
        }

        if (whStock == null) {
            // 入库时没有对应的sku库存
            return false;
        }
        // 需要更新的库存
        List<WhStock> updateList = new ArrayList<WhStock>();
        List<WhStockLog> whStockLogList = new ArrayList<WhStockLog>();

        //要修改的老库存表的可用库存
        Map<String, Integer> updateSurplusQuantityMap = new HashMap<String, Integer>();
        Map<String, String> updateIdMap = new HashMap<String, String>();

        Integer skuQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                : whCheckIn.getWhCheckInItem().getQuantity();
        Integer originalSurplusQuantity = whStock.getSurplusQuantity() == null ? 0 : whStock.getSurplusQuantity();
        Integer originalUpingQuantity = whStock.getUpQuantity() == null ? 0 : whStock.getUpQuantity();

        if (originalUpingQuantity - skuQuantity < 0) {
            return false;
        }

        // 加可用库存
        if (whCheckIn.isPreStoreUp()) {
            LocationTagEnum locationTag = LocationTagEnum.PRESTORE;
            if (upStock == null) {
                upStock = new WhStock();
                upStock.setSku(whCheckIn.getWhCheckInItem().getSku());
                upStock.setSurplusQuantity(skuQuantity);
                upStock.setLocationNumber(whCheckIn.getLocationNumber());
                if (locationTag != null)
                    upStock.setLocationTag(locationTag.getCode());
                // 变更的可用库存差异数量
                upStock.setUpdateSurplusQuantity(skuQuantity);
                upStock.setLastUpdatedBy(DataContextHolder.getUserId());
                upStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                upStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));
                whStockService.createWhStock(upStock);
                whStockLogList.add(new WhStockLog(upStock.getSku(), StockLogType.USABLE_STOCK,
                        upStock.getId(), upStock.getLocationNumber(), StockLogStep.PURCHASE_UP, skuQuantity,
                        0, whCheckIn.getInId() + ""));

                // 增加sku库位
                WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
                whSkuQueryCondition.setSku(upStock.getSku());
                WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
                WhSku updateWhSku = new WhSku();
                updateWhSku.setId(whSku.getId());
                updateWhSku.setLocationNumber(whSku.addLocationNumber(upStock.getLocationNumber()));
                whSkuService.updateWhSku(updateWhSku);
                whStockChangeRecordService.generateStockChangeRecord(upStock.getId(), skuQuantity,
                        whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.LOCAL, OrderTypeEnum.CHECKIN, true);
            }
            else {
                upStock.addLocationTag(locationTag);
                WhStock updateStock = WhStock.buildUpdateStock(upStock);
                updateStock.setSurplusQuantity(originalSurplusQuantity + skuQuantity);
                // 变更的可用库存差异数量
                updateStock.setUpdateSurplusQuantity(skuQuantity);
                updateStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));
                updateList.add(updateStock);
                whStockLogList.add(new WhStockLog(upStock.getSku(), StockLogType.USABLE_STOCK,
                        upStock.getId(), upStock.getLocationNumber(), StockLogStep.PURCHASE_UP, skuQuantity,
                        originalSurplusQuantity, whCheckIn.getInId() + ""));
                whStockChangeRecordService.generateStockChangeRecord(upStock.getId(), skuQuantity,
                        whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.LOCAL, OrderTypeEnum.CHECKIN, false);
            }
            WhStock updateStock = WhStock.buildUpdateStock(whStock);
            updateStock.setUpQuantity(originalUpingQuantity - skuQuantity);
            updateList.add(updateStock);
            whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING, whStock.getId(),
                    whStock.getLocationNumber(), StockLogStep.PURCHASE_UP, -skuQuantity,
                    originalUpingQuantity, whCheckIn.getInId() + ""));
            if (locationTag != null)
                whCheckInService.addLocationMatchReocrd(upStock.getSku(),
                        whCheckIn.getAfterSaleQty() != null,
                        StringUtils.contains(
                                whCheckIn.getWhCheckInItem().getFirstOrderType(),
                                CheckInFlags.SHELF_LIFE_STORAGE.getCode()),
                        whCheckIn.getLocationNumber(), whCheckIn.getInId()+"", AllocatePhaseEnum.UPLOAD, HandleResultEnum.STOCK);

            //记录库存ID
            WhCheckInItem whCheckInItem = new WhCheckInItem();
            whCheckInItem.setItemId(whCheckIn.getWhCheckInItem().getItemId());
            whCheckInItem.setInId(whCheckIn.getInId());
            whCheckInItem.setSkuId(upStock.getId());
            whCheckInItemService.updateWhCheckInItem(whCheckInItem);
        }
        else {
            WhStock updateStock = WhStock.buildUpdateStock(whStock);
            updateStock.setSurplusQuantity(originalSurplusQuantity + skuQuantity);
            // 变更的可用库存差异数量
            updateStock.setUpdateSurplusQuantity(skuQuantity);
            updateStock.setUpQuantity(originalUpingQuantity - skuQuantity);
            updateStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));
            updateList.add(updateStock);
            whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING, whStock.getId(),
                    whStock.getLocationNumber(), StockLogStep.PURCHASE_UP, -skuQuantity,
                    originalUpingQuantity, whCheckIn.getInId() + ""));

            whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.USABLE_STOCK, whStock.getId(),
                    whStock.getLocationNumber(), StockLogStep.PURCHASE_UP, skuQuantity,
                    originalSurplusQuantity, whCheckIn.getInId() + ""));
            whStockChangeRecordService.generateStockChangeRecord(whStock.getId(), skuQuantity,
                    whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.LOCAL, OrderTypeEnum.CHECKIN, false);
        }

        updateSurplusQuantityMap.put(whStock.getSku(), skuQuantity);
        updateIdMap.put(whStock.getSku(), whCheckIn.getInId() + "");

        if (CollectionUtils.isEmpty(updateList)) {
            return false;
        }
        whStockService.batchUpdateWhStock(updateList);
        // 添加库存变动日志
        if (CollectionUtils.isNotEmpty(whStockLogList)) {
            whStockLogService.batchAddWhStockLog(whStockLogList);
        }
        // 添加平台库存日志
        whSaleStockLogService.createWhSaleStockLog(new WhSaleStockLog(whCheckIn.getWhCheckInItem().getSku(), skuQuantity, whCheckIn.getInId(), SaleStockLogType.UP));

        // 售后结算数量大于0，记录售后结算明细
        if (whCheckIn.getAfterSaleQty() != null)
            createAfterSaleItem(whCheckIn, upStock, skuQuantity, CheckInWhType.LOCAL.intCode(), null);

        Map<Integer,String> stockIdMap = new HashMap<>();
        stockIdMap.put(upStock.getId(), upStock.getSku());
        // 记录本地仓最新上架时间
        if (!whCheckIn.isPreStoreUp()) {
            FrozenStockUtils.updateTimeAndUser(stockIdMap, false, true);
        }
        return true;
    }
    
    @Override
    public void createAfterSaleItem(WhCheckIn whCheckIn, WhStock whStock, Integer qty, Integer checkInType,
            TransferStock transferStock) {
        if (whCheckIn == null || whCheckIn.getAfterSaleQty() == null || whStock == null || qty == null)
            return;

        boolean zfOrder = whCheckIn.getWhCheckInItem().zfOrder();
        if (zfOrder && StringUtils.isNotBlank(whCheckIn.getWhCheckInItem().getComment())) {
            String comment = whCheckIn.getWhCheckInItem().getComment();
            whStock = whStockService.getWhStock(Integer.valueOf(comment));
        }
        if (whStock == null)
            return;
        Integer orderType = AssetOrderType.PURCHASE_ORDER.intCode();
        if (checkInType != null && CheckInType.TRANSFER.intCode().equals(checkInType)) {
            orderType = AssetOrderType.STOCK_ALLOCATION_IN.intCode();
        }
        checkInType = (checkInType == null || CheckInType.TRANSFER.intCode().equals(checkInType))
                ? CheckInWhType.LOCAL.intCode()
                : checkInType;
        AfterSaleSettlementQueryCondition afsQuery = new AfterSaleSettlementQueryCondition();
        afsQuery.setSku(whStock.getSku());
        afsQuery.setStockId(whStock.getId());
        afsQuery.setVendorCode(whCheckIn.getSupplierId());
        AfterSaleSettlement saleSettlement = afterSaleSettlementService.queryAfterSaleSettlement(afsQuery);
        if (saleSettlement == null) {
            saleSettlement = new AfterSaleSettlement(null, whStock.getSku(), whCheckIn.getSupplierId(),
                    whCheckIn.getVendorName(), whStock.getLocationNumber(), whStock.getId(), CheckInWhType.LOCAL.intCode());
        }
        // 修改库位属性
        WhStock updateStock = new WhStock();
        updateStock.setId(whStock.getId());
        whStock.addLocationTag(LocationTagEnum.SALED_BALANCE);
        updateStock.setLocationTag(whStock.getLocationTag());
        whStockService.updateWhStock(updateStock);
        AfterSaleSettlement settlement = AfterSaleUtils.assembleStockDataDiff(saleSettlement, qty,
                StockLogType.USABLE_STOCK.intCode(), orderType, whCheckIn.getInId(),
                whCheckIn.getPurchaseOrderNo());

        afterSaleSettlementService.batchUpdateQtyAndCreateItem(Collections.singletonList(settlement));
        // 售后结算中上架到中转仓的数据要计算为调拨出库
        if (Objects.equals(CheckInWhType.FBA.intCode(), checkInType)) {
            AfterSaleSettlement settlements = AfterSaleUtils.assembleStockDataDiff(settlement, qty,
                    StockLogType.STOCK_ALLOCATION.intCode(), AssetOrderType.TRANSFER_CHECK_IN.intCode(), whCheckIn.getInId(),
                    whCheckIn.getPurchaseOrderNo());
            afterSaleSettlementService.batchUpdateQtyAndCreateItem(Collections.singletonList(settlements));
        }
        // 生成售后结算库存变更记录
        orderRequestService.getAfterSaleSkuStock(Collections.singletonList(settlement.getSku()));
    }

    @Override
    public boolean batchUpdateStockByDiscardCheckIn(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())) {
            return false;
        }
        // 库位为空时，不用动库存
        if (whCheckIn.getWhCheckInItem().getSkuId() == null)
            return true;
        if (whCheckIn.getExceptionType() != null && whCheckIn.getExceptionType().equals(CheckInWhType.FBA.intCode())) {
            // 走中转仓库存
            return checkInUpdateTransferStockService.batchUpdateStockByDiscardCheckIn(whCheckIn);
        }
        else {

            try {

                WhStockQueryCondition query = new WhStockQueryCondition();
                query.setSku(whCheckIn.getWhCheckInItem().getSku());
                query.setId(whCheckIn.getWhCheckInItem().getSkuId());
                WhStock whStock = whStockService.queryWhStock(query);
                if (whStock == null) {
                    // 入库时没有对应的sku库存
                    return false;
                }

                // 需要更新的库存
                List<WhStock> updateList = new ArrayList<WhStock>();
                List<WhStockLog> whStockLogList = new ArrayList<WhStockLog>();

                Integer waitingUpQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                        : whCheckIn.getWhCheckInItem().getQcQuantity();
                Integer upingQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                        : whCheckIn.getWhCheckInItem().getQcQuantity();
                Integer waitingQcQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                        : whCheckIn.getWhCheckInItem().getQuantity();

                Integer originalWaitingUpQuantity = whStock.getWaitingUpQuantity() == null ? 0
                        : whStock.getWaitingUpQuantity();
                Integer originalUpingQuantity = whStock.getUpQuantity() == null ? 0 : whStock.getUpQuantity();
                Integer originalWaitingQcQuantity = whStock.getQcQuantity() == null ? 0 : whStock.getQcQuantity();

                if (originalWaitingQcQuantity - waitingQcQuantity < 0
                        && CheckInStatus.WAITING_QC.intCode().equals(whCheckIn.getStatus())) {
                    return false;
                }
                else if (originalWaitingUpQuantity - waitingUpQuantity < 0
                        && CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())) {
                    return false;
                }
                else if (originalUpingQuantity - upingQuantity < 0
                        && (CheckInStatus.UPING.intCode().equals(whCheckIn.getStatus())
                                || CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus()))) {
                    return false;
                }

                WhStock updateStock = new WhStock();
                updateStock.setId(whStock.getId());
                updateStock.setSku(whCheckIn.getWhCheckInItem().getSku());

                if (CheckInStatus.WAITING_QC.intCode().equals(whCheckIn.getStatus())) {
                    updateStock.setQcQuantity(originalWaitingQcQuantity - waitingQcQuantity);
                    whStockLogList
                            .add(new WhStockLog(whStock.getSku(), StockLogType.WAITING_QC,whStock.getId(),whStock.getLocationNumber(), StockLogStep.PURCHASE_DISCARD,
                                    -waitingQcQuantity, originalWaitingQcQuantity, "," + whCheckIn.getInId()));
                }
                else if (CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())) {
                    updateStock.setWaitingUpQuantity(originalWaitingUpQuantity - waitingUpQuantity);
                    whStockLogList
                            .add(new WhStockLog(whStock.getSku(), StockLogType.WAITING_UP,whStock.getId(),whStock.getLocationNumber(), StockLogStep.PURCHASE_DISCARD,
                                    -waitingUpQuantity, originalWaitingUpQuantity, "," + whCheckIn.getInId()));
                }
                else if (CheckInStatus.UPING.intCode().equals(whCheckIn.getStatus())
                        || CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus())) {
                    updateStock.setUpQuantity(originalUpingQuantity - upingQuantity);
                    whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING,whStock.getId(),whStock.getLocationNumber(), StockLogStep.PURCHASE_DISCARD,
                            -upingQuantity, originalUpingQuantity, "," + whCheckIn.getInId()));
                }
                updateStock.setLastUpdatedBy(DataContextHolder.getUserId());
                updateStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                updateList.add(updateStock);

                if (CollectionUtils.isEmpty(updateList)) {
                    return false;
                }

                // 添加库存变动日志
                if (CollectionUtils.isNotEmpty(whStockLogList)) {
                    whStockLogService.batchAddWhStockLog(whStockLogList);
                }

                whStockService.batchUpdateWhStock(updateList);

                return true;
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public boolean batchUpdateStockByPick(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())) {
            return false;
        }

        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();

        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSku(whCheckInItem.getSku());
        stockQuery.setId(whCheckInItem.getSkuId());
        WhStock whStock = whStockService.queryWhStock(stockQuery);
        if (whStock == null) {
            return false;
        }

        Integer skuQuantity = whCheckInItem.getQcQuantity();
        List<WhStock> updateList = new ArrayList<>();
        List<WhStockLog> whStockLogList = new ArrayList<>();

        int originalUpQuantity = whStock.getUpQuantity() == null ? 0 : whStock.getUpQuantity();
        int originalSurplusQuantity = whStock.getSurplusQuantity() == null ? 0 : whStock.getSurplusQuantity();
        WhStock updateStock = WhStock.buildUpdateStock(whStock);
        updateStock.setSurplusQuantity(originalSurplusQuantity + skuQuantity);
        updateStock.setUpdateSurplusQuantity(skuQuantity);
        updateStock.setUpQuantity(originalUpQuantity - skuQuantity);
        updateStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));
        updateList.add(updateStock);
        whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING, whStock.getId(),
                whStock.getLocationNumber(), StockLogStep.PURCHASE_PICK_UP, -skuQuantity, originalUpQuantity,
                whCheckIn.getInId() + ""));
        whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.USABLE_STOCK, whStock.getId(),
                whStock.getLocationNumber(), StockLogStep.PURCHASE_PICK_UP, skuQuantity, originalSurplusQuantity,
                whCheckIn.getInId() + ""));
        whStockService.batchUpdateWhStock(updateList);
        whStockLogService.batchAddWhStockLog(whStockLogList);
        whStockChangeRecordService.generateStockChangeRecord(whStock.getId(), skuQuantity,
                whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.LOCAL, OrderTypeEnum.CHECKIN, false);
        // 售后结算数量大于0，记录售后结算明细
        if (whCheckIn.getAfterSaleQty() != null)
            createAfterSaleItem(whCheckIn, updateStock, skuQuantity, CheckInWhType.LOCAL.intCode(), null);

        // 记录本地仓最新上架时间
        Map<Integer, String> stockIdMap = new HashMap<>();
        stockIdMap.put(whStock.getId(), whStock.getSku());
        FrozenStockUtils.updateTimeAndUser(stockIdMap, false,true);
        return true;
    }
}
