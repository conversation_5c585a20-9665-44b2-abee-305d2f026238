package com.estone.warehouse.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 
 * @Description:
 * 
 * @ClassName: BoxType
 * @Author: qinyangkai
 * @Date: 2018年8月17日
 * @Version: 0.0.1
 */
public enum BoxType {
    PURCHASE("采购入库", "1", "R"),
    MULTI_PRODUCT("普通多品周转框", "2", "JHDP"),
    SINGLE_PRODUCT("普通多件周转框", "3", "JHDJ"),
    KNTJ("库内退件", "4", "KNTJ"),
    HWSJ("海外退件上架", "5", "HWSJ"),
    BZYC("播种异常", "6", "BZYC"),
    CHECKIN_EXCEPTION("入库异常", "7","RKYC"),
    YK("移库", "8", "YK"),
    ALLOCATION_CHECKIN("调拨入库", "9", "DB"),
    RECEIVE("普通收货", "10", "SH"),
    BZCY("播种差异", "11", "BZCY"),
    CH("存货","12","CH"),
    PKYC("少货周转筐","13","PKYC"),

    RECEIVE_TJ("特急收货", "14", "SHJ"),

    TRANSFER_WAREHOUSE("中转仓多品周转筐", "15", "JHDPZ"),

    SINGLE_TRANSFER_WAREHOUSE("中转仓多件周转筐", "18", "JHDJZ"),

    TRANSFER_BZYC("中转仓播种异常", "19", "BZYCZ"),

    TRANSFER_BZCY("中转仓播种差异", "20", "BZCYZ"),

    FBA_FIRST("FBA头程周转筐", "21", "JHDPF"),

    ASN_FIRST("海外仓多品周转筐", "22", "JHDPA"),
    ASN_FIRST_SM("海外仓单品周转筐", "32", "JHDJA"),

    TEMU_FIRST("拼多多备货周转筐", "23", "JHDPT"),

    MULTI_PRODUCT_JB("集包多品周转筐", "16", "JHDPJB"),
    SINGLE_PRODUCT_JB("集包多件周转筐", "17", "JHDJJB"),
    JIT_ASN_SM("仓发单品周转筐", "24", "JHDJCF"),
    JIT_ASN_MM("仓发多品周转筐", "25", "JHDPCF"),
    JIT_ASN_BZYC("仓发异常周转筐", "26", "BZYCCF"),
    JIT_ASN_BZCY("仓发差异周转筐", "27", "BZCYCF"),
    PF_ORDER("批发订单", "28", "PF"),
    PDYC("盘点异常周转筐", "29", "PDYC"),
    WL_RECEIVE("物流收货", "30", "WLSH"),
    ;

    private String code;

    private String name;

    private String shortCode;

    private BoxType(String name, String code, String shortCode) {
        this.name = name;
        this.code = code;
        this.shortCode = shortCode;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static BoxType build(String code) {
        BoxType[] values = values();
        for (BoxType type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        BoxType[] values = values();
        for (BoxType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    public static List<Integer> getInBoxType() {
        return Arrays.stream(valuesInBoxType()).map(v -> v.intCode()).collect(Collectors.toList());
    }

    public static List<Integer> getOutBoxType() {
        return Arrays.stream(valuesOutBoxType()).map(v -> v.intCode()).collect(Collectors.toList());
    }

    public static BoxType[] valuesInBoxType() {
        BoxType[] values = values();
        List<BoxType> except = new ArrayList<BoxType>();
        for (BoxType type : values) {
            if (type.code.equals("1") || type.code.equals("5") || type.code.equals("7") || type.code.equals("9")) {
                except.add(type);
            }
        }
        BoxType[] returns = new BoxType[except.size()];
        return except.toArray(returns);
    }

    public static BoxType[] valuesOutBoxType() {
        BoxType[] values = values();
        List<BoxType> except = new ArrayList<BoxType>();
        for (BoxType type : values) {
            if (!type.code.equals("10") && !type.code.equals("14") && !type.code.equals("1") && !type.code.equals("5")
                    && !type.code.equals("7") && !type.code.equals("9") && !type.code.equals("28")) {
                except.add(type);
            }
        }
        BoxType[] returns = new BoxType[except.size()];
        return except.toArray(returns);
    }

    public static List<Integer> getShBoxIntCode(){
        List<Integer> shBoxCodes = new ArrayList<Integer>();
        shBoxCodes.add(BoxType.RECEIVE.intCode());
        shBoxCodes.add(BoxType.RECEIVE_TJ.intCode());
        shBoxCodes.add(BoxType.PF_ORDER.intCode());
        shBoxCodes.add(BoxType.WL_RECEIVE.intCode());
        return shBoxCodes;
    }

    public static List<String> getShortCodeList(){
        List<String> shortCodeList = new ArrayList<String>();
        shortCodeList.add(BoxType.TRANSFER_WAREHOUSE.getShortCode());
        shortCodeList.add(BoxType.MULTI_PRODUCT_JB.getShortCode());
        shortCodeList.add(BoxType.SINGLE_PRODUCT_JB.getShortCode());
        return shortCodeList;
    }
}
